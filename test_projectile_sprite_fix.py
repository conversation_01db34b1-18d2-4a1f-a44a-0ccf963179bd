#!/usr/bin/env python3
"""
Test script to verify projectile sprite loading is working
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from game_objects.standardized_outfits import OUTFITS_REGISTRY

def test_projectile_sprites():
    """Test that projectile sprites are loading correctly."""
    print("Testing projectile sprite loading...")
    print("="*50)
    
    # Find ammunition outfits
    ammo_outfits = [outfit for outfit in OUTFITS_REGISTRY.values() 
                   if outfit.category == "ammunition"]
    
    if not ammo_outfits:
        print("❌ No ammunition outfits found!")
        return False
    
    success = True
    for ammo in ammo_outfits:
        print(f"\n🔍 Testing {ammo.id} ({ammo.name})")
        print(f"  Category: {ammo.category}")
        print(f"  Projectile sprite: '{ammo.projectile_sprite}'")
        
        if hasattr(ammo, 'projectile_sprite'):
            if ammo.projectile_sprite and ammo.projectile_sprite.strip():
                print(f"  ✅ Has projectile sprite path")
                # Check if file exists
                if os.path.exists(ammo.projectile_sprite):
                    print(f"  ✅ Sprite file exists")
                else:
                    print(f"  ⚠️  Sprite file not found at: {ammo.projectile_sprite}")
            else:
                print(f"  ❌ Projectile sprite is empty")
                success = False
        else:
            print(f"  ❌ No projectile_sprite attribute")
            success = False
        
        # Test other important attributes
        for attr in ['shield_damage', 'armor_damage', 'projectile_behavior']:
            if hasattr(ammo, attr):
                value = getattr(ammo, attr)
                print(f"  ✅ {attr}: {value}")
            else:
                print(f"  ❌ Missing {attr}")
    
    print("\n" + "="*50)
    if success:
        print("✅ All ammunition outfits have projectile sprites!")
    else:
        print("❌ Some ammunition outfits are missing projectile sprites")
    
    return success

if __name__ == "__main__":
    test_projectile_sprites()
