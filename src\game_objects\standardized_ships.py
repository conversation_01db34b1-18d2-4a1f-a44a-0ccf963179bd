"""
Standardized ship system for Escape Velocity Py
This creates a modular ship system similar to the outfit system
"""

import pygame as pg
import json
import os

# Ship size categories
SHIP_SIZE_SMALL = "small"
SHIP_SIZE_MEDIUM = "medium"
SHIP_SIZE_LARGE = "large"
SHIP_SIZE_CAPITAL = "capital"

# Ship classes/types
SHIP_CLASS_FIGHTER = "fighter"
SHIP_CLASS_FREIGHTER = "freighter"
SHIP_CLASS_TRANSPORT = "transport"
SHIP_CLASS_CORVETTE = "corvette"
SHIP_CLASS_FRIGATE = "frigate"
SHIP_CLASS_CRUISER = "cruiser"
SHIP_CLASS_DESTROYER = "destroyer"
SHIP_CLASS_CARRIER = "carrier"
SHIP_CLASS_BATTLESHIP = "battleship"
SHIP_CLASS_DREADNOUGHT = "dreadnought"

class StandardizedShip:
    """Standardized ship class for the editor system."""
    
    def __init__(self, ship_id, name, ship_class, size):
        # Core Identity
        self.id = ship_id
        self.name = name
        self.ship_class = ship_class
        self.size = size
        
        # Physical Properties
        self.outfit_space = 20
        self.cargo_space = 10
        self.mass = 1.0
        self.cost = 10000
        self.min_tech_level = 1
        
        # Performance Stats
        self.max_speed = 3.0
        self.acceleration = 0.5
        self.turn_rate = 1.0
        
        # Defense Stats
        self.shields = 50
        self.max_shields = 50
        self.armor = 30
        self.max_armor = 30
        self.shield_recharge_rate = 1.0
        
        # Visual Assets
        self.shipyard_sprite = ""        # Sprite shown in shipyard
        self.game_sprite = ""            # Sprite used in-game
        self.animation_frames = 1        # Number of rotation frames
        self.sprite_size = 32            # Size of each sprite frame
        
        # Compatibility and Requirements
        self.ship_size_restrictions = []  # Empty = all sizes allowed
        self.description = ""
        
        # Default outfits (what the ship comes with)
        self.default_outfits = {}  # outfit_id -> quantity
        
        # Sprite loading
        self.image = None
        self.image_path = None
        
    def create_default_image(self):
        """Create a default colored rectangle based on ship size."""
        size_colors = {
            SHIP_SIZE_SMALL: (0, 255, 0),      # Green
            SHIP_SIZE_MEDIUM: (255, 255, 0),   # Yellow
            SHIP_SIZE_LARGE: (255, 165, 0),    # Orange
            SHIP_SIZE_CAPITAL: (255, 0, 0)     # Red
        }
        
        size_dimensions = {
            SHIP_SIZE_SMALL: (20, 20),
            SHIP_SIZE_MEDIUM: (30, 30),
            SHIP_SIZE_LARGE: (40, 40),
            SHIP_SIZE_CAPITAL: (50, 50)
        }
        
        color = size_colors.get(self.size, (128, 128, 128))
        dimensions = size_dimensions.get(self.size, (30, 30))
        
        self.image = pg.Surface(dimensions, pg.SRCALPHA)
        self.image.fill(color)
        
    def load_sprite(self, sprite_path):
        """Load sprite from file path."""
        self.image_path = sprite_path
        if sprite_path and os.path.exists(sprite_path):
            try:
                self.image = pg.image.load(sprite_path).convert_alpha()
                return True
            except Exception as e:
                print(f"Failed to load sprite for {self.name}: {e}")
                self.create_default_image()
                return False
        else:
            self.create_default_image()
            return False
            
    def can_install_outfit(self, outfit):
        """Check if an outfit can be installed on this ship."""
        # Check size restrictions
        if outfit.ship_size_restrictions and self.size not in outfit.ship_size_restrictions:
            return False
        
        # Check tech level
        if outfit.min_tech_level > self.min_tech_level:
            return False
            
        return True
        
    def clone(self):
        """Create a copy of this ship for instantiation."""
        new_ship = StandardizedShip(self.id, self.name, self.ship_class, self.size)
        
        # Copy all properties
        new_ship.outfit_space = self.outfit_space
        new_ship.cargo_space = self.cargo_space
        new_ship.mass = self.mass
        new_ship.cost = self.cost
        new_ship.min_tech_level = self.min_tech_level
        
        new_ship.max_speed = self.max_speed
        new_ship.acceleration = self.acceleration
        new_ship.turn_rate = self.turn_rate
        
        new_ship.shields = self.shields
        new_ship.max_shields = self.max_shields
        new_ship.armor = self.armor
        new_ship.max_armor = self.max_armor
        new_ship.shield_recharge_rate = self.shield_recharge_rate
        
        new_ship.shipyard_sprite = self.shipyard_sprite
        new_ship.game_sprite = self.game_sprite
        new_ship.animation_frames = self.animation_frames
        new_ship.sprite_size = self.sprite_size
        
        new_ship.description = self.description
        new_ship.default_outfits = self.default_outfits.copy()
        
        # Copy sprite
        if self.image:
            new_ship.image = self.image.copy()
        new_ship.image_path = self.image_path
        
        return new_ship

# Global ship registry
STANDARDIZED_SHIPS_REGISTRY = {}

def register_ship(ship):
    """Register a ship in the global registry."""
    STANDARDIZED_SHIPS_REGISTRY[ship.id] = ship

def get_ship_by_id(ship_id):
    """Get a ship by its ID."""
    return STANDARDIZED_SHIPS_REGISTRY.get(ship_id)

def get_ships_by_class(ship_class):
    """Get all ships of a specific class."""
    return [ship for ship in STANDARDIZED_SHIPS_REGISTRY.values() if ship.ship_class == ship_class]

def get_ships_by_size(size):
    """Get all ships of a specific size."""
    return [ship for ship in STANDARDIZED_SHIPS_REGISTRY.values() if ship.size == size]

def save_ship_to_json(ship, filepath):
    """Save a ship to a JSON file for the editor."""
    data = {
        'id': ship.id,
        'name': ship.name,
        'ship_class': ship.ship_class,
        'size': ship.size,
        'outfit_space': ship.outfit_space,
        'cargo_space': ship.cargo_space,
        'mass': ship.mass,
        'cost': ship.cost,
        'min_tech_level': ship.min_tech_level,
        'max_speed': ship.max_speed,
        'acceleration': ship.acceleration,
        'turn_rate': ship.turn_rate,
        'shields': ship.shields,
        'max_shields': ship.max_shields,
        'armor': ship.armor,
        'max_armor': ship.max_armor,
        'shield_recharge_rate': ship.shield_recharge_rate,
        'shipyard_sprite': ship.shipyard_sprite,
        'game_sprite': ship.game_sprite,
        'animation_frames': ship.animation_frames,
        'sprite_size': ship.sprite_size,
        'description': ship.description,
        'default_outfits': ship.default_outfits
    }
    
    with open(filepath, 'w') as f:
        json.dump(data, f, indent=2)

def load_ship_from_json(filepath):
    """Load a ship from a JSON file."""
    with open(filepath, 'r') as f:
        data = json.load(f)
    
    ship = StandardizedShip(data['id'], data['name'], data['ship_class'], data['size'])
    
    # Load all properties
    for prop in ['outfit_space', 'cargo_space', 'mass', 'cost', 'min_tech_level',
                 'max_speed', 'acceleration', 'turn_rate', 'shields', 'max_shields',
                 'armor', 'max_armor', 'shield_recharge_rate', 'shipyard_sprite',
                 'game_sprite', 'animation_frames', 'sprite_size', 'description']:
        if prop in data:
            setattr(ship, prop, data[prop])
    
    if 'default_outfits' in data:
        ship.default_outfits = data['default_outfits']
    
    return ship

def convert_legacy_ship_to_standardized(legacy_ship):
    """Convert a legacy ship to the standardized system."""
    ship = StandardizedShip(
        ship_id=legacy_ship.name.lower().replace(" ", "_"),
        name=legacy_ship.name,
        ship_class=legacy_ship.ship_class,
        size=legacy_ship.size
    )
    
    # Copy stats
    ship.outfit_space = legacy_ship.outfit_space
    ship.cargo_space = legacy_ship.cargo_space
    ship.min_tech_level = legacy_ship.min_tech_level
    
    ship.max_speed = legacy_ship.max_speed
    ship.acceleration = legacy_ship.acceleration
    ship.turn_rate = legacy_ship.turn_rate
    
    ship.shields = legacy_ship.shields
    ship.max_shields = legacy_ship.max_shields
    ship.armor = legacy_ship.armor
    ship.max_armor = legacy_ship.max_armor
    
    ship.description = legacy_ship.description
    
    # Calculate cost based on ship size if not present
    ship.cost = getattr(legacy_ship, 'cost', 10000 * (1 + [SHIP_SIZE_SMALL, SHIP_SIZE_MEDIUM, SHIP_SIZE_LARGE, SHIP_SIZE_CAPITAL].index(legacy_ship.size)))
    
    # Add some reasonable defaults for new properties
    ship.weapon_hardpoints = _generate_default_hardpoints(ship.size, ship.ship_class, 'weapon')
    ship.turret_hardpoints = _generate_default_hardpoints(ship.size, ship.ship_class, 'turret') 
    ship.engine_hardpoints = _generate_default_hardpoints(ship.size, ship.ship_class, 'engine')
    
    ship.fuel_capacity = 50 + ([SHIP_SIZE_SMALL, SHIP_SIZE_MEDIUM, SHIP_SIZE_LARGE, SHIP_SIZE_CAPITAL].index(ship.size) * 50)
    ship.energy_capacity = 50 + ([SHIP_SIZE_SMALL, SHIP_SIZE_MEDIUM, SHIP_SIZE_LARGE, SHIP_SIZE_CAPITAL].index(ship.size) * 25)
    ship.energy_recharge = 5.0 + ([SHIP_SIZE_SMALL, SHIP_SIZE_MEDIUM, SHIP_SIZE_LARGE, SHIP_SIZE_CAPITAL].index(ship.size) * 2.5)
    
    ship.crew_capacity = 1 + ([SHIP_SIZE_SMALL, SHIP_SIZE_MEDIUM, SHIP_SIZE_LARGE, SHIP_SIZE_CAPITAL].index(ship.size) * 2)
    ship.manufacturer = "Legacy Shipyards"
    ship.origin = "Core Worlds"
    
    # Copy sprite if available
    if hasattr(legacy_ship, 'image') and legacy_ship.image:
        ship.image = legacy_ship.image.copy()
    else:
        ship.create_default_image()
        
    if hasattr(legacy_ship, 'image_path'):
        ship.image_path = legacy_ship.image_path
    
    return ship

def _generate_default_hardpoints(size, ship_class, hardpoint_type):
    """Generate reasonable default hardpoints based on ship size and class."""
    # Base hardpoint counts by size
    base_counts = {
        SHIP_SIZE_SMALL: {'weapon': 2, 'turret': 0, 'engine': 1},
        SHIP_SIZE_MEDIUM: {'weapon': 4, 'turret': 1, 'engine': 2},
        SHIP_SIZE_LARGE: {'weapon': 6, 'turret': 2, 'engine': 3},
        SHIP_SIZE_CAPITAL: {'weapon': 8, 'turret': 4, 'engine': 4}
    }
    
    # Adjust based on ship class
    class_modifiers = {
        SHIP_CLASS_FIGHTER: {'weapon': 1.2, 'turret': 0.5, 'engine': 1.0},
        SHIP_CLASS_FREIGHTER: {'weapon': 0.5, 'turret': 0.8, 'engine': 1.2},
        SHIP_CLASS_CORVETTE: {'weapon': 1.0, 'turret': 1.2, 'engine': 1.0},
        SHIP_CLASS_FRIGATE: {'weapon': 1.0, 'turret': 1.0, 'engine': 1.0},
        SHIP_CLASS_CRUISER: {'weapon': 1.1, 'turret': 1.3, 'engine': 0.9},
        SHIP_CLASS_DESTROYER: {'weapon': 1.3, 'turret': 1.1, 'engine': 1.0},
        SHIP_CLASS_CARRIER: {'weapon': 0.7, 'turret': 1.5, 'engine': 0.8},
        SHIP_CLASS_BATTLESHIP: {'weapon': 1.4, 'turret': 1.4, 'engine': 0.8}
    }
    
    base_count = base_counts.get(size, base_counts[SHIP_SIZE_MEDIUM]).get(hardpoint_type, 1)
    modifier = class_modifiers.get(ship_class, class_modifiers[SHIP_CLASS_FIGHTER]).get(hardpoint_type, 1.0)
    
    final_count = max(0, int(base_count * modifier))
    
    # Generate hardpoint list with basic properties
    hardpoints = []
    for i in range(final_count):
        hardpoint = {
            'id': f"{hardpoint_type}_{i+1}",
            'type': hardpoint_type,
            'size': 'any',  # Can mount any size weapon/system
            'arc': 360 if hardpoint_type == 'turret' else 45,  # Firing arc in degrees
            'position': {'x': 0, 'y': 0}  # Relative to ship center
        }
        hardpoints.append(hardpoint)
    
    return hardpoints
