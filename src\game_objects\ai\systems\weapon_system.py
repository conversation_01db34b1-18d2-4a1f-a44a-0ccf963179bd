"""
AI Weapon System - Manages weapon selection, targeting, and firing
Provides intelligent weapon usage and ensures ships always have weapons.
"""
import pygame as pg
import math
import random
from ..core.ai_constants import (
    WEAPON_SWITCH_COOLDOWN,
    MISSILE_CONSERVATION_THRESHOLD,
    AI_WEAPON_RANGE,
    FIXED_WEAPON_OPTIMAL_RANGE,
    TURRET_WEAPON_OPTIMAL_RANGE,
    MISSILE_WEAPON_OPTIMAL_RANGE
)
from ..core.ai_utils import get_weapon_damage, angle_difference

class AIWeaponManager:
    """
    Manages AI weapon systems including selection, targeting, and firing.
    Ensures ships always have functional weapons and use them intelligently.
    """
    
    def __init__(self, ai_ship):
        self.ship = ai_ship
        self.current_weapon_index = 0
        self.last_weapon_switch = 0
        self.last_fire_time = 0
        self.target_lead_calculation = True
        
        # Weapon categories for smart selection
        self.primary_weapons = []    # Main combat weapons
        self.secondary_weapons = []  # Missiles, special weapons
        self.fallback_weapons = []   # Basic weapons as last resort
        
        # Firing parameters
        self.firing_accuracy = 0.8   # Base accuracy (0.0 to 1.0)
        self.burst_fire_count = 0
        self.burst_fire_max = 3
        
        # Initialize weapon system
        self.initialize_weapons()
    
    def initialize_weapons(self):
        """Initialize and categorize weapons, ensuring fallback options."""
        if not hasattr(self.ship, 'weapons'):
            self.ship.weapons = []
        
        # Categorize existing weapons
        self.categorize_weapons()
        
        # Ensure ship has at least one weapon
        self.ensure_fallback_weapons()
        
        # Select initial weapon
        self.select_best_weapon()
    
    def categorize_weapons(self):
        """Categorize weapons by type and effectiveness."""
        self.primary_weapons.clear()
        self.secondary_weapons.clear()
        self.fallback_weapons.clear()
        
        for weapon in self.ship.weapons:
            if not weapon:
                continue
                
            # Get weapon damage for evaluation
            shield_damage, armor_damage = get_weapon_damage(weapon)
            total_damage = shield_damage + armor_damage
            
            # Categorize by mount type and damage
            mount_type = getattr(weapon, 'mount_type', 'fixed')
            
            if mount_type == "missile":
                self.secondary_weapons.append(weapon)
            elif total_damage >= 15:  # High damage weapons
                self.primary_weapons.append(weapon)
            else:  # Low damage weapons
                self.fallback_weapons.append(weapon)
    
    def ensure_fallback_weapons(self):
        """Ensure ship has at least one basic weapon."""
        total_weapons = len(self.primary_weapons) + len(self.secondary_weapons) + len(self.fallback_weapons)
        
        if total_weapons == 0:
            # Ship has no weapons - add a basic laser
            basic_weapon = self.create_basic_weapon()
            if basic_weapon:
                self.ship.weapons.append(basic_weapon)
                self.fallback_weapons.append(basic_weapon)
                print(f"Added fallback weapon to {self.ship.ship_type}")
    
    def create_basic_weapon(self):
        """
        Create a basic weapon as fallback.
        
        Returns:
            Basic weapon object or None
        """
        try:
            # Try to import and create a basic laser
            from game_objects.standardized_outfits import LaserCannon
            
            basic_laser = LaserCannon()
            basic_laser.mount_type = "fixed"
            basic_laser.shield_damage = 10
            basic_laser.armor_damage = 8
            basic_laser.fire_rate = 30  # 0.5 seconds at 60 FPS
            basic_laser.range = AI_WEAPON_RANGE
            basic_laser.ammo = -1  # Unlimited ammo
            
            return basic_laser
            
        except ImportError:
            # Fallback to a simple weapon class
            class BasicWeapon:
                def __init__(self):
                    self.mount_type = "fixed"
                    self.shield_damage = 10
                    self.armor_damage = 8
                    self.fire_rate = 30
                    self.range = AI_WEAPON_RANGE
                    self.ammo = -1
                    self.last_fire_time = 0
                
                def can_fire(self):
                    return True
                
                def fire(self, origin, target_pos, game):
                    # Basic firing logic would go here
                    return True
            
            return BasicWeapon()
    
    def update(self, dt, game_time):
        """
        Update weapon system.
        
        Args:
            dt: Delta time
            game_time: Current game time in frames
        """
        # Update all weapons
        for weapon in self.ship.weapons:
            if hasattr(weapon, 'update'):
                weapon.update(dt)
        
        # Check for weapon switching opportunities
        if self.should_switch_weapon(game_time):
            self.select_best_weapon()
    
    def should_switch_weapon(self, game_time):
        """
        Determine if we should switch weapons.
        
        Args:
            game_time: Current game time
            
        Returns:
            bool: True if weapon should be switched
        """
        # Respect weapon switch cooldown
        if game_time - self.last_weapon_switch < WEAPON_SWITCH_COOLDOWN:
            return False
        
        # Switch if current weapon is out of ammo
        current_weapon = self.get_current_weapon()
        if current_weapon and hasattr(current_weapon, 'ammo'):
            if current_weapon.ammo == 0:
                return True
        
        # Switch if current weapon is not optimal for situation
        return random.random() < 0.05  # 5% chance for tactical switching
    
    def select_best_weapon(self):
        """Select the best weapon for the current situation."""
        # Get sensor data for context
        sensor_manager = getattr(self.ship, 'sensor_manager', None)
        target = sensor_manager.get_current_target() if sensor_manager else None
        
        best_weapon_index = 0
        best_score = -1
        
        all_weapons = self.primary_weapons + self.secondary_weapons + self.fallback_weapons
        
        for i, weapon in enumerate(all_weapons):
            score = self.evaluate_weapon(weapon, target)
            if score > best_score:
                best_score = score
                best_weapon_index = i
        
        if all_weapons:
            self.current_weapon_index = best_weapon_index
            self.last_weapon_switch = 0  # Reset for next switch
    
    def evaluate_weapon(self, weapon, target=None):
        """
        Evaluate weapon effectiveness for current situation.
        
        Args:
            weapon: Weapon to evaluate
            target: Current target (if any)
            
        Returns:
            float: Weapon effectiveness score
        """
        if not weapon:
            return 0
        
        score = 0
        
        # Base damage score
        shield_damage, armor_damage = get_weapon_damage(weapon)
        score += (shield_damage + armor_damage) * 0.1
        
        # Ammo considerations
        if hasattr(weapon, 'ammo'):
            if weapon.ammo == -1:  # Unlimited ammo
                score += 20
            elif weapon.ammo > 0:
                # Prefer weapons with more ammo
                score += min(weapon.ammo * 0.5, 15)
            else:
                # No ammo - very low score
                score = 1
        
        # Range considerations
        if target and hasattr(target, 'pos'):
            distance = self.ship.pos.distance_to(target.pos)
            weapon_range = getattr(weapon, 'range', AI_WEAPON_RANGE)
            
            if distance <= weapon_range:
                score += 10  # In range bonus
            else:
                score -= 5   # Out of range penalty
        
        # Mount type considerations
        mount_type = getattr(weapon, 'mount_type', 'fixed')
        if mount_type == "turret":
            score += 5  # Turrets are more flexible
        elif mount_type == "missile":
            # Use missiles conservatively
            if hasattr(weapon, 'ammo') and weapon.ammo > 0:
                ammo_ratio = weapon.ammo / max(getattr(weapon, 'max_ammo', 10), 1)
                if ammo_ratio > MISSILE_CONSERVATION_THRESHOLD:
                    score += 8
                else:
                    score -= 5  # Conserve missiles
        
        return score
    
    def get_current_weapon(self):
        """Get the currently selected weapon."""
        all_weapons = self.primary_weapons + self.secondary_weapons + self.fallback_weapons
        if all_weapons and 0 <= self.current_weapon_index < len(all_weapons):
            return all_weapons[self.current_weapon_index]
        return None
    
    def can_fire_at_target(self, target):
        """
        Check if we can fire at the target.
        
        Args:
            target: Target entity
            
        Returns:
            bool: True if we can fire
        """
        weapon = self.get_current_weapon()
        if not weapon or not target:
            return False
        
        # Check if weapon can fire
        if hasattr(weapon, 'can_fire') and not weapon.can_fire():
            return False
        
        # Check range
        distance = self.ship.pos.distance_to(target.pos)
        weapon_range = getattr(weapon, 'range', AI_WEAPON_RANGE)
        if distance > weapon_range:
            return False
        
        # Check firing angle for fixed weapons
        mount_type = getattr(weapon, 'mount_type', 'fixed')
        if mount_type == "fixed":
            return self.check_firing_angle(target)
        
        return True
    
    def check_firing_angle(self, target):
        """
        Check if target is within firing angle for fixed weapons.
        
        Args:
            target: Target entity
            
        Returns:
            bool: True if target is in firing arc
        """
        # Calculate angle to target
        to_target = target.pos - self.ship.pos
        target_angle = math.degrees(math.atan2(to_target.y, to_target.x))
        
        # Get ship facing angle
        ship_angle = getattr(self.ship, 'angle', 0)
        
        # Calculate angle difference
        angle_diff = abs(angle_difference(ship_angle, target_angle))
        
        # Fixed weapons have limited firing arc (±30 degrees)
        return angle_diff <= 30
    
    def fire_at_target(self, target, game_time):
        """
        Fire current weapon at target.
        
        Args:
            target: Target entity
            game_time: Current game time
            
        Returns:
            bool: True if weapon was fired
        """
        if not self.can_fire_at_target(target):
            return False
        
        weapon = self.get_current_weapon()
        if not weapon:
            return False
        
        # Calculate firing position
        firing_pos = self.calculate_firing_position(target)
        
        # Fire the weapon
        try:
            if hasattr(weapon, 'fire'):
                success = weapon.fire(self.ship.pos, firing_pos, self.ship.game)
                if success:
                    self.last_fire_time = game_time
                    return True
        except Exception as e:
            print(f"Error firing weapon: {e}")
        
        return False
    
    def calculate_firing_position(self, target):
        """
        Calculate where to aim when firing at target (lead calculation).
        
        Args:
            target: Target entity
            
        Returns:
            Vector2: Position to aim at
        """
        if not self.target_lead_calculation or not hasattr(target, 'vel'):
            return target.pos
        
        # Simple lead calculation
        weapon = self.get_current_weapon()
        projectile_speed = getattr(weapon, 'projectile_speed', 300)
        
        # Calculate time to target
        distance = self.ship.pos.distance_to(target.pos)
        time_to_target = distance / projectile_speed
        
        # Predict target position
        predicted_pos = target.pos + target.vel * time_to_target
        
        # Add some inaccuracy based on AI skill
        if self.firing_accuracy < 1.0:
            inaccuracy = (1.0 - self.firing_accuracy) * 50
            offset = pg.math.Vector2(
                random.uniform(-inaccuracy, inaccuracy),
                random.uniform(-inaccuracy, inaccuracy)
            )
            predicted_pos += offset
        
        return predicted_pos
    
    def get_weapon_info(self):
        """Get current weapon information for debugging."""
        weapon = self.get_current_weapon()
        if weapon:
            return {
                'type': getattr(weapon, 'mount_type', 'unknown'),
                'damage': get_weapon_damage(weapon),
                'ammo': getattr(weapon, 'ammo', 'unlimited'),
                'range': getattr(weapon, 'range', 'unknown')
            }
        return {'type': 'none'}
    
    def get_weapon_count(self):
        """Get total number of weapons."""
        return len(self.primary_weapons) + len(self.secondary_weapons) + len(self.fallback_weapons)
