import pygame as pg
import random
import math
from game_objects.player import Player # Import the Player class
from game_objects.planet import Planet # Import the Planet class
from game_objects.system import System # Import the System class
from game_objects.faction import Faction, FACTIONS_DATA # Import Faction stuff
from game_objects.ai_ship import AIShip, AI_STATE_DISABLED # Import AI Ship class and states
from game_objects.ships import get_ship_by_id # Import ship functions
from game_objects.ship_menu import ShipMenu # Import ship menu
from game_objects.trading import TradingSystem # Import trading system
from game_objects.modernized_outfitter import ModernizedOutfitter # Import modernized outfitter system
from game_objects.sound_manager import SoundManager # Import sound manager
from game_objects.shipyard import Shipyard # Import shipyard system
from game_objects.mission_system import MissionSystem # Import mission system

# --- Constants ---
SCREEN_WIDTH = 1920
SCREEN_HEIGHT = 1080
FPS = 60
TITLE = "Escape Velocity Py"
MAX_DOCKING_SPEED = 1.5
DOCKING_REQUEST_DISTANCE = 150
UNDOCK_OFFSET = 50 # Distance from planet center to place player upon undocking
MIN_JUMP_DISTANCE_FROM_OBJECTS = 300 # Min distance from any planet to initiate jump

# --- Colors ---
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
LIGHT_BLUE = (173, 216, 230)
GREY = (128, 128, 128)
STAR_COLOR = (200, 200, 200)
NUM_STARS = 200
# Double the size for each system as requested
WORLD_WIDTH = SCREEN_WIDTH * 2 * 3  # 2x the original size
WORLD_HEIGHT = SCREEN_HEIGHT * 2 * 3  # 2x the original size

# --- UI Constants ---
SIDEBAR_WIDTH = 300
SIDEBAR_COLOR = (30, 30, 50) # Dark blue-grey
SIDEBAR_TEXT_COLOR = WHITE
SIDEBAR_LINE_HEIGHT = 22
SIDEBAR_PADDING = 10

# --- Camera Class ---
class Camera:
    def __init__(self, width, height):
        self.camera = pg.Rect(0, 0, width, height)
        self.width = width
        self.height = height

    def apply(self, entity_rect):
        return entity_rect.move(self.camera.topleft)

    def update(self, target):
        x = -target.rect.centerx + int(SCREEN_WIDTH / 2)
        y = -target.rect.centery + int(SCREEN_HEIGHT / 2)
        x = min(0, x)
        y = min(0, y)
        x = max(-(self.width - SCREEN_WIDTH), x)
        y = max(-(self.height - SCREEN_HEIGHT), y)
        self.camera = pg.Rect(x, y, self.width, self.height)

# --- Game Class ---
class Game:
    def __init__(self):
        pg.init()
        pg.mixer.init()
        self.screen = pg.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pg.display.set_caption(TITLE)
        self.clock = pg.time.Clock()
        self.FPS = FPS # Store FPS as an instance attribute
        self.running = True
        self.playing = False
        self.state = "MENU" # MENU, SETUP, PLAYING, SYSTEM_MAP, DOCKED, GAME_OVER, JUMPING
        self.font_name = pg.font.match_font('arial')
        self.camera = Camera(WORLD_WIDTH, WORLD_HEIGHT)
        self.targeted_object = None
        self.docked_planet = None
        self.status_message = ""
        self.status_message_color = WHITE
        self.status_message_timer = 0

        self.player_name = "Cmdr" # Default player name
        self.ship_name = "Stardust" # Default ship name
        self.num_systems = 20 # Default number of systems
        self.input_field_active = None # Can be "PLAYER_NAME", "SHIP_NAME", "NUM_SYSTEMS"
        self.player_name_input_rect = None
        self.ship_name_input_rect = None
        self.num_systems_input_rect = None

        self.factions = {fid: Faction(fid, data["name"], data["color"]) for fid, data in FACTIONS_DATA.items()}
        self.faction_relations = {} # e.g. {"federation": {"pirates": -1.0, "independent": 0.5}}
        self.player_faction_id = "independent" # Player starts as independent
        self._initialize_faction_relations()


        self.galaxy_systems = {} # Stores System objects, keyed by system_id
        self.current_system_id = None
        self.jump_target_system_id = None

        self.all_sprites = pg.sprite.Group() # Initialize sprite groups once
        self.planets = pg.sprite.Group()
        self.ai_ships = pg.sprite.Group() # Group for AI ships
        self.player = None # Player will be created in new_game_setup
        self.stars = [] # Stars will be generated in new_game_setup

        self.planet_menu_options_rects = [] # For clickable menu options

        # Initialize ship menu, trading system, outfitter, and sound manager
        self.ship_menu = ShipMenu(self)
        self.trading_system = TradingSystem(self)
        self.outfitter = ModernizedOutfitter(self)
        self.shipyard = Shipyard(self)
        self.mission_system = MissionSystem(self) # Initialize shipyard
        self.sound_manager = SoundManager(self) # Initialize sound manager

    def _apply_world_boundaries(self, sprite):
        """Apply boundary constraints to sprites - keep them within world bounds."""
        boundary_margin = 50  # Distance from edge where sprites are pushed back

        # Check if sprite is moving out of bounds and correct position and velocity
        if sprite.pos.x < boundary_margin:
            sprite.pos.x = boundary_margin
            if hasattr(sprite, 'vel') and sprite.vel.x < 0:
                sprite.vel.x = 0  # Stop moving left
        elif sprite.pos.x > WORLD_WIDTH - boundary_margin:
            sprite.pos.x = WORLD_WIDTH - boundary_margin
            if hasattr(sprite, 'vel') and sprite.vel.x > 0:
                sprite.vel.x = 0  # Stop moving right

        if sprite.pos.y < boundary_margin:
            sprite.pos.y = boundary_margin
            if hasattr(sprite, 'vel') and sprite.vel.y < 0:
                sprite.vel.y = 0  # Stop moving up
        elif sprite.pos.y > WORLD_HEIGHT - boundary_margin:
            sprite.pos.y = WORLD_HEIGHT - boundary_margin
            if hasattr(sprite, 'vel') and sprite.vel.y > 0:
                sprite.vel.y = 0  # Stop moving down

        # Update rect position to match pos
        if hasattr(sprite, 'rect'):
            sprite.rect.center = sprite.pos

    def _initialize_faction_relations(self):
        """Sets up default relationships between factions."""
        faction_ids = list(self.factions.keys())
        for f_id1 in faction_ids:
            self.faction_relations[f_id1] = {}
            for f_id2 in faction_ids:
                if f_id1 == f_id2:
                    self.faction_relations[f_id1][f_id2] = 1.0 # Max positive relation with self
                else:
                    # Default to neutral, specific overrides below
                    self.faction_relations[f_id1][f_id2] = 0.0

        # Example specific relationships (can be expanded)
        # Ensure faction IDs exist before setting relations
        if "federation" in self.factions and "pirates" in self.factions:
            self.faction_relations["federation"]["pirates"] = -1.0
            self.faction_relations["pirates"]["federation"] = -1.0
        if "corporation" in self.factions and "pirates" in self.factions:
            self.faction_relations["corporation"]["pirates"] = -0.8
            self.faction_relations["pirates"]["corporation"] = -0.8
        if "merchants" in self.factions and "pirates" in self.factions:
            self.faction_relations["merchants"]["pirates"] = -0.9
            self.faction_relations["pirates"]["merchants"] = -0.9
        if "independent" in self.factions and "pirates" in self.factions:
            self.faction_relations["independent"]["pirates"] = -0.7
            self.faction_relations["pirates"]["independent"] = -0.7

        if "federation" in self.factions and "corporation" in self.factions:
            self.faction_relations["federation"]["corporation"] = 0.2
            self.faction_relations["corporation"]["federation"] = 0.2
        if "federation" in self.factions and "merchants" in self.factions:
            self.faction_relations["federation"]["merchants"] = 0.5
            self.faction_relations["merchants"]["federation"] = 0.5
        if "federation" in self.factions and "independent" in self.factions:
            self.faction_relations["federation"]["independent"] = 0.3
            self.faction_relations["independent"]["federation"] = 0.3


    def generate_galaxy(self):
        self.galaxy_systems.clear()
        # Predefined system names for variety
        system_names = [
            "Sol", "Alpha Centauri", "Sirius", "Proxima Centauri", "Barnard's Star",
            "Wolf 359", "Lalande 21185", "Epsilon Eridani", "Tau Ceti", "Gliese 581",
            "Kepler-186", "TRAPPIST-1", "Vega", "Arcturus", "Capella", "Rigel",
            "Betelgeuse", "Aldebaran", "Polaris", "Deneb", "Altair", "Antares",
            "Canopus", "Procyon", "Achernar", "Hadar", "Acrux", "Gacrux",
            "Shaula", "Bellatrix", "Elnath", "Miaplacidus", "Alnilam", "Regor",
            "Alnair", "Alioth", "Alnitak", "Dubhe", "Mirfak", "Wezen",
            "Sargas", "Kaus Australis", "Avior", "Alkaid", "Menkalinan", "Atria",
            "Alhena", "Peacock", "Alsephina", "Mirzam", "Polux", "Alphard"
        ]
        random.shuffle(system_names) # Shuffle to get random names if num_systems < len(system_names)

        map_width = SCREEN_WIDTH - 100 # Map display area
        map_height = SCREEN_HEIGHT - 100
        padding = 30  # Reduced padding to fit more systems

        # Create systems
        for i in range(self.num_systems):
            sys_id = f"sys_{i}"
            name = system_names[i % len(system_names)]
            pos_x = random.randint(padding, map_width - padding)
            pos_y = random.randint(padding, map_height - padding)

            # Assign a random faction to the system
            system_faction_id = random.choice(list(self.factions.keys()))
            new_system = System(sys_id, name, pos_x, pos_y, faction=system_faction_id)
            self.galaxy_systems[sys_id] = new_system
            if i == 0:
                self.current_system_id = sys_id
                new_system.is_current_system = True
                new_system.is_explored = True

        # Create connections (simple linear connections for now, then some random ones)
        system_ids = list(self.galaxy_systems.keys())
        for i in range(len(system_ids)):
            current_sys_obj = self.galaxy_systems[system_ids[i]]
            # Connect to next system in list (if not last) to ensure basic connectivity
            if i < len(system_ids) - 1:
                next_sys_obj = self.galaxy_systems[system_ids[i+1]]
                current_sys_obj.add_connection(next_sys_obj.id)
                next_sys_obj.add_connection(current_sys_obj.id) # Bidirectional

        # Add a few more random connections to make it less linear (optional)
        for _ in range(self.num_systems // 2):
            sys1_id, sys2_id = random.sample(system_ids, 2)
            if sys1_id != sys2_id:
                 sys1_obj = self.galaxy_systems[sys1_id]
                 sys2_obj = self.galaxy_systems[sys2_id]
                 if sys2_id not in sys1_obj.connections:
                     sys1_obj.add_connection(sys2_id)
                     sys2_obj.add_connection(sys1_id)

        print(f"Generated {len(self.galaxy_systems)} systems. Current: {self.current_system_id}")
        # Print all systems for debugging
        for system in self.galaxy_systems.values():
            print(system)


    def setup_current_system_objects(self):
        self.all_sprites.empty()
        self.planets.empty()
        self.ai_ships.empty() # Clear old AI ships
        self.stars.clear()

        if not self.player:
            # Default to scout ship for new players
            ship_id = "scout"
            self.player = Player(self, self.player_name, self.ship_name, ship_id)
        self.player.pos = pg.math.Vector2(WORLD_WIDTH / 2, WORLD_HEIGHT / 2)
        self.player.vel = pg.math.Vector2(0,0)
        self.all_sprites.add(self.player)

        for _ in range(NUM_STARS):
            x = random.randrange(0, WORLD_WIDTH)
            y = random.randrange(0, WORLD_HEIGHT)
            self.stars.append(pg.math.Vector2(x, y))

        num_planets_in_system = random.randint(1, 3)
        planet_names_pool = ["Terra Nova", "Xylos Outpost", "Barren Rock", "New Kyoto", "Zephyr", "Krypton", "Argon Prime"]

        # Determine the faction for planets in this system
        # Planets will inherit the system's faction for now
        current_system_obj = self.galaxy_systems.get(self.current_system_id)
        planet_faction_id = current_system_obj.faction if current_system_obj else "unaligned"


        for i in range(num_planets_in_system):
            p_name = random.choice(planet_names_pool) + f" {i+1}"
            # p_faction = random.choice(list(self.factions.keys())) # Old: random faction for planet
            p_faction = planet_faction_id # New: Planet inherits system faction
            p_tech = random.randint(0,5)
            p_x = random.randint(int(WORLD_WIDTH * 0.2), int(WORLD_WIDTH * 0.8))
            p_y = random.randint(int(WORLD_HEIGHT * 0.2), int(WORLD_HEIGHT * 0.8))

            valid_pos = False
            attempts = 0
            while not valid_pos and attempts < 10:
                valid_pos = True
                temp_rect = pg.Rect(p_x - 50, p_y - 50, 100, 100)
                for existing_planet in self.planets:
                    if temp_rect.colliderect(existing_planet.rect.inflate(100,100)):
                        p_x = random.randint(int(WORLD_WIDTH * 0.2), int(WORLD_WIDTH * 0.8))
                        p_y = random.randint(int(WORLD_HEIGHT * 0.2), int(WORLD_HEIGHT * 0.8))
                        valid_pos = False
                        break
                attempts += 1

            new_planet = Planet(self, p_x, p_y, name=p_name, faction=p_faction, tech_level=p_tech)
            self.all_sprites.add(new_planet)
            self.planets.add(new_planet)

        # IMPROVED AI SHIP SPAWNING SYSTEM
        self._spawn_ai_ships_for_system(planet_faction_id)


        self.targeted_object = None
        self.docked_planet = None
        self.status_message = ""
        self.status_message_timer = 0

    def _spawn_ai_ships_for_system(self, system_faction_id):
        """
        Spawn AI ships with proper variety based on system properties.
        This replaces the hardcoded ship selection with a dynamic system.
        """
        from game_objects.ships import SHIPS, get_ships_by_size, get_ships_by_class

        # Get current system for determining spawn characteristics
        current_system_obj = self.galaxy_systems.get(self.current_system_id)
        if not current_system_obj:
            return

        # Determine system wealth/danger level based on faction and tech
        system_wealth_level = self._calculate_system_wealth(current_system_obj)
        system_danger_level = self._calculate_system_danger(current_system_obj)

        print(f"\n=== AI Ship Spawning for {current_system_obj.name} ===")
        print(f"System Faction: {system_faction_id}")
        print(f"Wealth Level: {system_wealth_level}")
        print(f"Danger Level: {system_danger_level}")

        # Base number of ships varies by system characteristics
        base_ship_count = random.randint(3, 7)
        if system_danger_level == "high":
            base_ship_count += random.randint(2, 4)
        elif system_danger_level == "low":
            base_ship_count = max(2, base_ship_count - 2)

        print(f"Spawning {base_ship_count} AI ships")

        # Get all available ships from the registry
        available_ships = list(SHIPS.keys())
        print(f"Available ship types: {available_ships}")

        # Create ship spawn pools based on size and probability
        ship_spawn_pools = self._create_ship_spawn_pools(system_wealth_level, system_danger_level)

        spawned_ships = []

        for i in range(base_ship_count):
            # Choose faction (mostly system faction, some variety)
            ship_faction = self._choose_ship_faction(system_faction_id, system_danger_level)

            # Choose ship based on spawn pools
            ship_id = self._choose_ship_from_pools(ship_spawn_pools, available_ships)

            if ship_id:
                # Spawn the ship
                ai_ship = self._spawn_single_ai_ship(ship_id, ship_faction, i)
                if ai_ship:
                    spawned_ships.append((ship_id, ship_faction))
                    print(f"  Spawned: {ship_id} ({ship_faction})")

        print(f"Successfully spawned {len(spawned_ships)} ships")
        print("="*50)

    def _calculate_system_wealth(self, system_obj):
        """Calculate system wealth level based on faction and planets."""
        # Get average planet tech level
        total_tech = sum(planet.tech_level for planet in self.planets)
        avg_tech = total_tech / len(self.planets) if self.planets else 1

        # Faction wealth modifiers
        faction_wealth_mods = {
            "federation": 1.2,
            "corporation": 1.5,
            "merchants": 1.3,
            "independent": 1.0,
            "pirates": 0.6,
            "unaligned": 0.8
        }

        wealth_mod = faction_wealth_mods.get(system_obj.faction, 1.0)
        wealth_score = avg_tech * wealth_mod

        if wealth_score >= 3.5:
            return "high"
        elif wealth_score >= 2.0:
            return "medium"
        else:
            return "low"

    def _calculate_system_danger(self, system_obj):
        """Calculate system danger level based on faction relations."""
        player_relation = self.faction_relations.get(self.player_faction_id, {}).get(system_obj.faction, 0.0)

        # Pirates and hostile factions increase danger
        if system_obj.faction == "pirates" or player_relation < -0.5:
            return "high"
        elif player_relation < 0:
            return "medium"
        else:
            return "low"

    def _create_ship_spawn_pools(self, wealth_level, danger_level):
        """Create weighted spawn pools for different ship types."""
        # Base probabilities for ship sizes
        pools = {
            "small": {"weight": 50, "ships": []},
            "medium": {"weight": 30, "ships": []},
            "large": {"weight": 15, "ships": []},
            "capital": {"weight": 5, "ships": []}
        }

        # Adjust probabilities based on wealth
        if wealth_level == "high":
            pools["medium"]["weight"] = 35
            pools["large"]["weight"] = 25
            pools["capital"]["weight"] = 10
        elif wealth_level == "low":
            pools["small"]["weight"] = 70
            pools["medium"]["weight"] = 20
            pools["large"]["weight"] = 8
            pools["capital"]["weight"] = 2

        # Adjust for danger level
        if danger_level == "high":
            pools["medium"]["weight"] += 10
            pools["large"]["weight"] += 5

        return pools

    def _choose_ship_faction(self, system_faction_id, danger_level):
        """Choose faction for spawned ship with some variety."""
        # Base chance to spawn system faction ships
        system_faction_chance = 0.7

        if danger_level == "high":
            system_faction_chance = 0.5  # More variety in dangerous systems

        if random.random() < system_faction_chance:
            return system_faction_id
        else:
            # Choose a different faction
            other_factions = [f for f in self.factions.keys() if f != system_faction_id]

            # Weight pirates higher in dangerous systems
            if danger_level == "high" and "pirates" in other_factions:
                if random.random() < 0.4:  # 40% chance for pirates in dangerous systems
                    return "pirates"

            return random.choice(other_factions)

    def _choose_ship_from_pools(self, spawn_pools, available_ships):
        """Choose a ship from the weighted spawn pools."""
        from game_objects.ships import SHIPS

        # Populate pools with actual ships from registry
        for ship_id, ship_obj in SHIPS.items():
            if ship_id in available_ships:
                size = ship_obj.size
                if size in spawn_pools:
                    spawn_pools[size]["ships"].append(ship_id)

        # Create weighted list
        weighted_choices = []
        for size, pool_data in spawn_pools.items():
            if pool_data["ships"]:  # Only add if ships are available
                for _ in range(pool_data["weight"]):
                    weighted_choices.extend(pool_data["ships"])

        if not weighted_choices:
            print("WARNING: No ships available for spawning!")
            return "scout"  # Fallback

        return random.choice(weighted_choices)

    def _spawn_single_ai_ship(self, ship_id, ship_faction, attempt_num):
        """Spawn a single AI ship with collision avoidance."""
        max_attempts = 15

        for attempt in range(max_attempts):
            ai_pos_x = random.randrange(int(WORLD_WIDTH * 0.1), int(WORLD_WIDTH * 0.9))
            ai_pos_y = random.randrange(int(WORLD_HEIGHT * 0.1), int(WORLD_HEIGHT * 0.9))

            # Check for collisions with planets and player
            valid_spawn = True
            temp_ai_rect = pg.Rect(ai_pos_x - 40, ai_pos_y - 30, 80, 60)  # Larger collision box

            # Check planets
            for planet_sprite in self.planets:
                if temp_ai_rect.colliderect(planet_sprite.rect.inflate(150, 150)):
                    valid_spawn = False
                    break

            # Check player
            if valid_spawn and self.player:
                player_distance = pg.math.Vector2(ai_pos_x, ai_pos_y).distance_to(self.player.pos)
                if player_distance < 200:  # Keep ships away from player initially
                    valid_spawn = False

            # Check other AI ships (avoid clustering)
            if valid_spawn:
                for existing_ship in self.ai_ships:
                    ship_distance = pg.math.Vector2(ai_pos_x, ai_pos_y).distance_to(existing_ship.pos)
                    if ship_distance < 150:  # Minimum distance between AI ships
                        valid_spawn = False
                        break

            if valid_spawn:
                try:
                    ai_ship = AIShip(self, ai_pos_x, ai_pos_y, ship_faction, ship_id)
                    self.all_sprites.add(ai_ship)
                    self.ai_ships.add(ai_ship)
                    return ai_ship
                except Exception as e:
                    print(f"ERROR spawning ship {ship_id}: {e}")
                    continue

        print(f"WARNING: Failed to spawn {ship_id} after {max_attempts} attempts")
        return None

    def _cycle_targets(self, reverse=False):
        """Enhanced target cycling with better organization and filtering."""
        # Get all targetable entities
        targetable_entities = []

        # Add planets
        for p in self.planets:
            if p.alive():
                targetable_entities.append(p)

        # Add AI ships
        for s in self.ai_ships:
            if s.alive():
                targetable_entities.append(s)

        if not targetable_entities:
            self.targeted_object = None
            self.set_status_message("No targets available", YELLOW)
            return

        # Sort by distance to player
        targetable_entities.sort(key=lambda entity: self.player.pos.distance_squared_to(entity.pos))

        # Find current target index
        current_target_index = -1
        if self.targeted_object and self.targeted_object.alive():
            try:
                current_target_index = targetable_entities.index(self.targeted_object)
            except ValueError:
                current_target_index = -1  # Current target not in list (e.g. destroyed)

        # Cycle to next/previous target
        if reverse:
            # Shift+Tab: cycle backwards
            if current_target_index > 0:
                self.targeted_object = targetable_entities[current_target_index - 1]
            else:
                self.targeted_object = targetable_entities[-1]  # Last entity
        else:
            # Tab: cycle forwards
            if current_target_index != -1 and current_target_index < len(targetable_entities) - 1:
                self.targeted_object = targetable_entities[current_target_index + 1]
            else:
                self.targeted_object = targetable_entities[0]  # First entity

        # Display target info
        self._display_target_info()

    def _display_target_info(self):
        """Display information about the currently targeted object."""
        if not self.targeted_object:
            return

        if hasattr(self.targeted_object, 'name'):  # Planet
            distance = int(self.player.pos.distance_to(self.targeted_object.pos))
            self.set_status_message(f"Targeted: {self.targeted_object.name} ({distance}m)", WHITE)

        elif hasattr(self.targeted_object, 'ship'):  # AI Ship
            ship_name = self.targeted_object.ship.name
            faction = self.targeted_object.faction_id.capitalize()
            ship_size = self.targeted_object.ship.size.capitalize()
            distance = int(self.player.pos.distance_to(self.targeted_object.pos))

            # Get relation status
            relation_val = self.faction_relations.get(self.player_faction_id, {}).get(self.targeted_object.faction_id, 0.0)
            if relation_val < -0.5:
                relation_color = RED
                relation_text = "Hostile"
            elif relation_val > 0.5:
                relation_color = GREEN
                relation_text = "Friendly"
            else:
                relation_color = WHITE
                relation_text = "Neutral"

            self.set_status_message(f"Targeted: {faction} {ship_name} ({ship_size}, {distance}m, {relation_text})", relation_color)

    def _get_target_display_name(self, entity):
        """Get a descriptive name for any targetable entity."""
        if hasattr(entity, 'name'):  # Planet
            return entity.name
        elif hasattr(entity, 'ship'):  # AI Ship
            ship_name = entity.ship.name if hasattr(entity, 'ship') else entity.ship_type
            faction = entity.faction_id.capitalize()
            return f"{faction} {ship_name}"
        else:
            return "Unknown Object"

    def _target_nearest_hostile(self):
        """Target the nearest hostile ship."""
        hostile_ships = []

        for ship in self.ai_ships:
            if not ship.alive():
                continue

            relation_val = self.faction_relations.get(self.player_faction_id, {}).get(ship.faction_id, 0.0)
            if relation_val < -0.5:  # Hostile
                distance = self.player.pos.distance_squared_to(ship.pos)
                hostile_ships.append((ship, distance))

        if hostile_ships:
            # Sort by distance and target the closest
            hostile_ships.sort(key=lambda x: x[1])
            self.targeted_object = hostile_ships[0][0]
            self._display_target_info()
        else:
            self.set_status_message("No hostile ships in range", YELLOW)

    def _target_nearest_friendly(self):
        """Target the nearest friendly ship."""
        friendly_ships = []

        for ship in self.ai_ships:
            if not ship.alive():
                continue

            relation_val = self.faction_relations.get(self.player_faction_id, {}).get(ship.faction_id, 0.0)
            if relation_val >= 0:  # Neutral or friendly
                distance = self.player.pos.distance_squared_to(ship.pos)
                friendly_ships.append((ship, distance))

        if friendly_ships:
            # Sort by distance and target the closest
            friendly_ships.sort(key=lambda x: x[1])
            self.targeted_object = friendly_ships[0][0]
            self._display_target_info()
        else:
            self.set_status_message("No friendly ships in range", YELLOW)

    def _target_nearest_planet(self):
        """Target the nearest planet."""
        if not self.planets:
            self.set_status_message("No planets in system", YELLOW)
            return

        # Find closest planet
        planets_with_distance = []
        for planet in self.planets:
            if planet.alive():
                distance = self.player.pos.distance_squared_to(planet.pos)
                planets_with_distance.append((planet, distance))

        if planets_with_distance:
            planets_with_distance.sort(key=lambda x: x[1])
            self.targeted_object = planets_with_distance[0][0]
            self._display_target_info()
        else:
            self.set_status_message("No planets available", YELLOW)

    def new_game_setup(self):
        self.generate_galaxy()
        self.setup_current_system_objects()

    def game_loop_run(self):
        self.playing = True
        while self.playing:
            self.clock.tick(FPS)
            self.events()
            self.update()
            self.draw()

    def update(self):
        if self.state == "PLAYING":
            # Calculate delta time for smoother updates
            dt = self.clock.get_time() / 1000.0  # Convert milliseconds to seconds

            # Update all sprites with delta time
            for sprite in self.all_sprites:
                if hasattr(sprite, 'update') and callable(sprite.update):
                    if 'dt' in sprite.update.__code__.co_varnames:
                        sprite.update(dt)
                    else:
                        sprite.update()

                # Apply world boundaries for all sprites
                if hasattr(sprite, 'pos'):
                    self._apply_world_boundaries(sprite)

            # Update projectiles for AI ships
            for ship in self.ai_ships:
                if hasattr(ship, 'projectiles'):
                    for projectile in list(ship.projectiles):
                        if projectile.update(dt):
                            projectile.kill()  # Remove from all sprite groups
                            ship.projectiles.remove(projectile)  # Also remove from ship's group

            # Update player projectiles
            if hasattr(self.player, 'projectiles'):
                for projectile in list(self.player.projectiles):
                    if projectile.update(dt):
                        projectile.kill()  # Remove from all sprite groups
                        self.player.projectiles.remove(projectile)  # Also remove from player's group

            self.camera.update(self.player)
            self.update_status_message()

    def events(self):
        for event in pg.event.get():
            if event.type == pg.QUIT:
                self.running = False; self.playing = False; self.state = "QUIT"

            if self.state == "PLAYING":
                if event.type == pg.MOUSEBUTTONDOWN and event.button == 1:
                    mouse_screen_pos = pg.mouse.get_pos()
                    world_x = mouse_screen_pos[0] - self.camera.camera.left
                    world_y = mouse_screen_pos[1] - self.camera.camera.top
                    clicked_entity = None
                    # Check planets first
                    for p in self.planets:
                        if p.rect.collidepoint(world_x, world_y):
                            clicked_entity = p
                            break
                    # If no planet clicked, check AI ships
                    if not clicked_entity:
                        for ship in self.ai_ships:
                            if ship.rect.collidepoint(world_x, world_y):
                                clicked_entity = ship
                                break

                    self.targeted_object = clicked_entity # This will be None if nothing was clicked

                elif event.type == pg.KEYDOWN:
                    if event.key == pg.K_r: # R for weapon/ammo cycling
                        if hasattr(self.player, 'cycle_weapons'):
                            self.player.cycle_weapons()
                            if hasattr(self.player, 'get_active_weapon_info'):
                                weapon_info = self.player.get_active_weapon_info()
                                self.set_status_message(f"Selected weapon: {weapon_info}", GREEN)
                    elif event.key == pg.K_t: # T for ammo cycling on current launcher
                        if hasattr(self.player, 'cycle_ammo_for_current_weapon'):
                            self.player.cycle_ammo_for_current_weapon()
                    elif event.key == pg.K_TAB:
                        # Enhanced Tab targeting system
                        shift_pressed = pg.key.get_pressed()[pg.K_LSHIFT] or pg.key.get_pressed()[pg.K_RSHIFT]
                        self._cycle_targets(reverse=shift_pressed)
                    elif event.key == pg.K_y:  # Y for nearest hostiles
                        self._target_nearest_hostile()
                    elif event.key == pg.K_f:  # F for nearest friendly
                        self._target_nearest_friendly()
                    elif event.key == pg.K_n:  # N for nearest planet
                        self._target_nearest_planet()
                    elif event.key == pg.K_m:
                        self.state = "SYSTEM_MAP"
                        self.playing = False
                        return

                    elif event.key == pg.K_p:
                        self.state = "SHIP_MENU"
                        self.playing = False
                        return

                    elif event.key == pg.K_b:
                        # Check for disabled ships nearby to board
                        self.check_for_boarding()

                    elif event.key == pg.K_j:
                        if self.jump_target_system_id:
                            # Check if the target system is directly connected to current system
                            current_sys = self.galaxy_systems[self.current_system_id]
                            if self.jump_target_system_id in current_sys.connections:
                                can_jump = True
                                for planet_sprite in self.planets:
                                    distance_to_planet = self.player.pos.distance_to(planet_sprite.pos)
                                    if distance_to_planet < MIN_JUMP_DISTANCE_FROM_OBJECTS + planet_sprite.radius:
                                        self.set_status_message(f"Too close to {planet_sprite.name} to jump.", YELLOW)
                                        can_jump = False
                                        break

                                if can_jump:
                                    # NEW: Check fuel for jump
                                    fuel_cost = 20  # Base fuel cost per jump
                                    if self.player.ship.consume_fuel(fuel_cost):
                                        target_sys_name = self.galaxy_systems[self.jump_target_system_id].name
                                        self.set_status_message(f"Jumping to {target_sys_name}...", GREEN)
                                        self.state = "JUMPING"
                                        self.playing = False
                                        return
                                    else:
                                        self.set_status_message("Insufficient fuel for jump!", RED)
                            else:
                                self.set_status_message("Cannot jump: Target system not directly connected.", YELLOW)
                        else:
                            self.set_status_message("No jump target selected. Open map (M).", YELLOW)

                    elif event.key == pg.K_l:
                        if self.targeted_object and isinstance(self.targeted_object, Planet):
                            planet = self.targeted_object
                            if not planet.is_landable:
                                self.set_status_message(f"Cannot land on {planet.name} (Uninhabited/Restricted).", YELLOW); return
                            distance_sq = self.player.pos.distance_squared_to(planet.pos)
                            effective_docking_distance = planet.radius + self.player.rect.width/2 + DOCKING_REQUEST_DISTANCE
                            if distance_sq < effective_docking_distance**2 :
                                if self.player.vel.length() < MAX_DOCKING_SPEED:
                                    if planet.reputation_with_player >= 0:
                                        # NEW: Restore power and fuel when docking
                                        power_restored = self.player.ship.power_capacity - self.player.ship.power
                                        fuel_restored = self.player.ship.fuel_capacity - self.player.ship.fuel

                                        self.player.ship.power = self.player.ship.power_capacity
                                        self.player.ship.fuel = self.player.ship.fuel_capacity
                                        self.player.power = self.player.ship.power
                                        self.player.fuel = self.player.ship.fuel

                                        if power_restored > 0 or fuel_restored > 0:
                                            self.set_status_message(f"Docking: Power & Fuel restored! Welcome to {planet.name}.", GREEN)
                                        else:
                                            self.set_status_message(f"Docking approved: {planet.name}.", GREEN)

                                        self.state = "DOCKED"; self.docked_planet = planet; self.playing = False
                                        # Check for mission completion
                                        self.mission_system.check_mission_completion(planet, self.current_system_id)
                                    else: self.set_status_message(f"Docking denied: {planet.name}. Hostile.", RED)
                                else: self.set_status_message("Too fast to dock. Reduce speed.", YELLOW)
                            else: self.set_status_message(f"Too far from {planet.name} to request docking.", YELLOW)
                        else: self.set_status_message("No planet targeted for docking.", YELLOW)

            elif self.state == "DOCKED":
                if event.type == pg.KEYDOWN:
                    if event.key == pg.K_u:
                        if self.docked_planet and self.player:
                            angle = random.uniform(0, 2 * math.pi)
                            offset_x = (self.docked_planet.radius + UNDOCK_OFFSET) * math.cos(angle)
                            offset_y = (self.docked_planet.radius + UNDOCK_OFFSET) * math.sin(angle)
                            self.player.pos = self.docked_planet.pos + pg.math.Vector2(offset_x, offset_y)
                            self.player.vel = pg.math.Vector2(0,0)
                        self.state = "PLAYING"; self.docked_planet = None; self.playing = True
                    elif event.key == pg.K_c and self.docked_planet.tech_level >=1:
                        self.state = "TRADING"
                        return
                    elif event.key == pg.K_o and self.docked_planet.tech_level >=2:
                        if self.outfitter.open(self.docked_planet):
                            result = self.outfitter.run(self.screen)
                            print(f"[MAIN] Outfitter returned: {result}")
                            if result == "DOCKED":
                                # Force redraw of planet screen and continue docked loop
                                self.show_planet_screen()
                                return
                    elif event.key == pg.K_s and self.docked_planet.tech_level >=3: # 'S' for Shipyard
                        self.state = "SHIPYARD"
                        return
                    elif event.key == pg.K_m and self.docked_planet.tech_level >=1: # 'M' for Mission Board
                        self.state = "MISSION_BOARD"
                        return
                    elif event.key == pg.K_r and self.docked_planet.tech_level >=1: # 'R' for Refuel
                        self._handle_refuel()
                    elif event.key == pg.K_t and self.docked_planet.tech_level >=3: self.set_status_message("Space Tavern Not Implemented", YELLOW, 60)
                elif event.type == pg.MOUSEBUTTONDOWN and event.button == 1:
                     mouse_pos = pg.mouse.get_pos()
                     for rect_action in self.planet_menu_options_rects:
                         rect, action_key = rect_action
                         if rect.collidepoint(mouse_pos):
                             simulated_event = pg.event.Event(pg.KEYDOWN, key=action_key)
                             pg.event.post(simulated_event)
                             break

    def set_status_message(self, message, color=WHITE, duration=180):
        self.status_message = message; self.status_message_color = color; self.status_message_timer = duration

    def _handle_refuel(self):
        """Handle refueling the player's ship"""
        if not self.docked_planet or not self.player:
            return

        # Calculate fuel needed and cost
        fuel_needed = self.player.fuel_capacity - self.player.fuel
        if fuel_needed <= 0:
            self.set_status_message("Fuel tank is already full!", YELLOW, 120)
            return

        # Fuel cost: 2 credits per unit
        fuel_cost_per_unit = 2
        total_cost = int(fuel_needed * fuel_cost_per_unit)

        if self.player.credits < total_cost:
            self.set_status_message(f"Not enough credits! Need {total_cost} credits for fuel.", RED, 120)
            return

        # Refuel the ship
        self.player.credits -= total_cost
        self.player.fuel = self.player.fuel_capacity
        self.set_status_message(f"Refueled! Cost: {total_cost} credits.", GREEN, 120)

    def check_for_boarding(self):
        """Check for disabled ships nearby that can be boarded."""
        print("DEBUG: 'B' pressed, check_for_boarding called.") # DEBUG
        # Find disabled ships within boarding range
        boardable_ships = []

        for ship in self.ai_ships:
            if ship.ai_state == AI_STATE_DISABLED and self.player.pos.distance_to(ship.pos) < 100:
                boardable_ships.append(ship)

        if not boardable_ships:
            print("DEBUG: No boardable ships found.") # DEBUG
            self.set_status_message("No disabled ships nearby to board.", (255, 255, 0))
            return

        # If multiple ships are in range, choose the closest one
        if len(boardable_ships) > 1:
            boardable_ships.sort(key=lambda s: self.player.pos.distance_squared_to(s.pos))

        target_ship = boardable_ships[0]
        print(f"DEBUG: Found boardable ship: {target_ship.ship_type}, attempting to show dialog.") # DEBUG

        # Show boarding dialog
        self.show_boarding_dialog(target_ship)

    def show_boarding_dialog(self, target_ship):
        """Show the boarding dialog for a disabled ship."""
        print(f"DEBUG: show_boarding_dialog called for {target_ship.ship_type}.") # DEBUG
        # Create a simple dialog with options
        dialog_width = 600
        dialog_height = 400
        dialog_rect = pg.Rect(SCREEN_WIDTH // 2 - dialog_width // 2,
                             SCREEN_HEIGHT // 2 - dialog_height // 2,
                             dialog_width, dialog_height)

        # Option buttons
        button_width = 400
        button_height = 50
        button_padding = 20

        capture_button_rect = pg.Rect(dialog_rect.centerx - button_width // 2,
                                     dialog_rect.top + 150,
                                     button_width, button_height)

        plunder_button_rect = pg.Rect(dialog_rect.centerx - button_width // 2,
                                     capture_button_rect.bottom + button_padding,
                                     button_width, button_height)

        cancel_button_rect = pg.Rect(dialog_rect.centerx - button_width // 2,
                                    plunder_button_rect.bottom + button_padding,
                                    button_width, button_height)

        # Display the dialog
        waiting_for_choice = True
        while waiting_for_choice and self.running:
            self.clock.tick(self.FPS)

            # Handle events
            for event in pg.event.get():
                if event.type == pg.QUIT:
                    waiting_for_choice = False
                    self.running = False
                    self.state = "QUIT"

                if event.type == pg.KEYDOWN:
                    if event.key == pg.K_ESCAPE:
                        waiting_for_choice = False

                if event.type == pg.MOUSEBUTTONDOWN and event.button == 1:
                    mouse_pos = pg.mouse.get_pos()

                    if capture_button_rect.collidepoint(mouse_pos):
                        # Attempt to capture the ship (placeholder for future implementation)
                        self.set_status_message("Ship capture not yet implemented.", (255, 255, 0))
                        waiting_for_choice = False

                    elif plunder_button_rect.collidepoint(mouse_pos):
                        # Plunder the ship for resources
                        self.plunder_ship(target_ship)
                        waiting_for_choice = False

                    elif cancel_button_rect.collidepoint(mouse_pos):
                        waiting_for_choice = False

            # Draw the dialog
            self.screen.fill(BLACK)

            # Draw the game in the background (dimmed)
            self.draw()

            # Draw semi-transparent overlay
            overlay = pg.Surface((SCREEN_WIDTH, SCREEN_HEIGHT), pg.SRCALPHA)
            overlay.fill((0, 0, 0, 192))  # Black with 75% opacity
            self.screen.blit(overlay, (0, 0))

            # Draw dialog box
            pg.draw.rect(self.screen, (40, 40, 60), dialog_rect)
            pg.draw.rect(self.screen, (100, 100, 140), dialog_rect, 2)

            # Draw title
            self.draw_text(f"Boarding {target_ship.ship_type}", 32, (220, 220, 255),
                          dialog_rect.centerx, dialog_rect.top + 40, align="center")

            # Draw description
            self.draw_text("This ship is disabled. What would you like to do?", 20, (200, 200, 200),
                          dialog_rect.centerx, dialog_rect.top + 100, align="center")

            # Draw buttons
            pg.draw.rect(self.screen, (60, 60, 100), capture_button_rect)
            pg.draw.rect(self.screen, (100, 100, 140), capture_button_rect, 2)
            self.draw_text("Attempt to Capture Ship (Not Implemented)", 20, (220, 220, 220),
                          capture_button_rect.centerx, capture_button_rect.centery, align="center")

            pg.draw.rect(self.screen, (60, 60, 100), plunder_button_rect)
            pg.draw.rect(self.screen, (100, 100, 140), plunder_button_rect, 2)
            self.draw_text("Plunder Ship for Resources", 20, (220, 220, 220),
                          plunder_button_rect.centerx, plunder_button_rect.centery, align="center")

            pg.draw.rect(self.screen, (60, 60, 100), cancel_button_rect)
            pg.draw.rect(self.screen, (100, 100, 140), cancel_button_rect, 2)
            self.draw_text("Cancel", 20, (220, 220, 220),
                          cancel_button_rect.centerx, cancel_button_rect.centery, align="center")

            pg.display.flip()

    def plunder_ship(self, target_ship):
        """Plunder a disabled ship for resources."""
        # Calculate loot value based on ship size and type
        base_loot_value = 200 * (1 + ["small", "medium", "large", "capital"].index(target_ship.ship.size))

        # Add random variation
        loot_value = int(base_loot_value * random.uniform(0.8, 1.5))

        # Add credits to player
        self.player.credits += loot_value

        # Destroy the ship
        target_ship.kill()

        # Show message
        self.set_status_message(f"Plundered {loot_value} credits from the ship!", (0, 255, 0))

    def update_status_message(self):
        if self.status_message_timer > 0: self.status_message_timer -= 1
        else: self.status_message = ""

    def draw_text(self, text, size, color, x, y, align="topleft"):
        font = pg.font.Font(self.font_name, size)
        text_surface = font.render(text, True, color)
        text_rect = text_surface.get_rect()
        if align == "topleft": text_rect.topleft = (x, y)
        elif align == "midtop": text_rect.midtop = (x, y)
        elif align == "center": text_rect.center = (x, y)
        elif align == "topright": text_rect.topright = (x, y)
        elif align == "right": text_rect.midright = (x, y)
        self.screen.blit(text_surface, text_rect)

    def draw_text_wrapped(self, text, size, color, rect):
        """Draw text that automatically wraps within a rectangle."""
        font = pg.font.Font(self.font_name, size)

        # Split the text into words
        words = text.split(' ')
        space_width = font.size(' ')[0]

        x, y = rect.left, rect.top
        max_width = rect.width

        for word in words:
            word_surface = font.render(word, True, color)
            word_width, word_height = word_surface.get_size()

            if x + word_width >= rect.left + max_width:
                # Move to the next line
                x = rect.left
                y += word_height

            # Check if we've gone beyond the bottom of the rect
            if y + word_height > rect.bottom:
                break

            # Draw the word
            self.screen.blit(word_surface, (x, y))
            x += word_width + space_width

    def draw(self):
        if self.state == "PLAYING":
            self.screen.fill(BLACK)
            for star_world_pos in self.stars:
                star_screen_pos = self.camera.apply(pg.Rect(star_world_pos.x, star_world_pos.y, 1, 1)).topleft
                if 0 <= star_screen_pos[0] < SCREEN_WIDTH and 0 <= star_screen_pos[1] < SCREEN_HEIGHT:
                     pg.draw.circle(self.screen, STAR_COLOR, star_screen_pos, 1)
            for sprite in self.all_sprites:
                if hasattr(sprite, 'pos'):
                    screen_rect = self.camera.apply(sprite.rect)
                    self.screen.blit(sprite.image, screen_rect)
                else: self.screen.blit(sprite.image, sprite.rect)

            # Draw targeting reticle for the current target
            if self.targeted_object and self.targeted_object.alive() and hasattr(self.targeted_object, 'rect'):
                try:
                    target_screen_rect = self.camera.apply(self.targeted_object.rect)
                    # Make the targeting box slightly larger than the target's rect
                    inflated_rect = target_screen_rect.inflate(10, 10)
                    pg.draw.rect(self.screen, RED, inflated_rect, 2)  # Draw a red box, 2px thick
                except AttributeError: # Should not happen if hasattr is checked, but as a safeguard
                    pass

            # --- Draw Sidebar ---
            sidebar_x = SCREEN_WIDTH - SIDEBAR_WIDTH
            sidebar_rect = pg.Rect(sidebar_x, 0, SIDEBAR_WIDTH, SCREEN_HEIGHT)
            pg.draw.rect(self.screen, SIDEBAR_COLOR, sidebar_rect)
            pg.draw.line(self.screen, WHITE, (sidebar_x, 0), (sidebar_x, SCREEN_HEIGHT), 2) # Separator line

            line_y = SIDEBAR_PADDING

            # Player Info
            if self.player:
                self.draw_text(f"{self.player.player_name} ({self.player.ship_name})", 18, LIGHT_BLUE, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                line_y += SIDEBAR_LINE_HEIGHT
                player_faction_name = self.factions[self.player_faction_id].name if self.player_faction_id in self.factions else "Unknown"
                self.draw_text(f"Faction: {player_faction_name}", 16, SIDEBAR_TEXT_COLOR, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                line_y += SIDEBAR_LINE_HEIGHT

                # Current system info
                if self.current_system_id and self.current_system_id in self.galaxy_systems:
                    current_sys_name = self.galaxy_systems[self.current_system_id].name
                    self.draw_text(f"System: {current_sys_name}", 16, YELLOW, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                    line_y += SIDEBAR_LINE_HEIGHT

                # Jump target info
                if self.jump_target_system_id and self.jump_target_system_id in self.galaxy_systems:
                    target_sys_name = self.galaxy_systems[self.jump_target_system_id].name
                    self.draw_text(f"Jump Target: {target_sys_name}", 16, GREEN, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                    line_y += SIDEBAR_LINE_HEIGHT
                    self.draw_text("Press J to initiate jump", 14, GREEN, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                    line_y += SIDEBAR_LINE_HEIGHT

                # Display player ship stats
                self.draw_text(f"Ship: {self.player.ship.name}", 16, SIDEBAR_TEXT_COLOR, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                line_y += SIDEBAR_LINE_HEIGHT
                self.draw_text(f"Class: {self.player.ship.ship_class.capitalize()}", 16, SIDEBAR_TEXT_COLOR, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                line_y += SIDEBAR_LINE_HEIGHT
                self.draw_text(f"Hull: {self.player.armor}/{self.player.max_armor}", 16, GREEN, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                line_y += SIDEBAR_LINE_HEIGHT
                self.draw_text(f"Shields: {int(self.player.shields)}/{self.player.max_shields}", 16, LIGHT_BLUE, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                line_y += SIDEBAR_LINE_HEIGHT
                # NEW: Power and Fuel display with status indicators
                power_color = GREEN if self.player.power > self.player.power_capacity * 0.3 else (YELLOW if self.player.power > self.player.power_capacity * 0.1 else RED)
                power_status = "" if self.player.power > 0 else " [OFFLINE]"
                self.draw_text(f"Power: {int(self.player.power)}/{int(self.player.power_capacity)}{power_status}", 16, power_color, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                line_y += SIDEBAR_LINE_HEIGHT

                # Power status warnings
                if self.player.power <= 0:
                    self.draw_text(">> ALL SYSTEMS OFFLINE <<", 14, RED, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                    line_y += SIDEBAR_LINE_HEIGHT
                elif self.player.power <= self.player.power_capacity * 0.1:
                    self.draw_text(">> CRITICAL POWER <<", 14, RED, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                    line_y += SIDEBAR_LINE_HEIGHT

                fuel_color = GREEN if self.player.fuel > self.player.fuel_capacity * 0.3 else (YELLOW if self.player.fuel > self.player.fuel_capacity * 0.1 else RED)
                self.draw_text(f"Fuel: {int(self.player.fuel)}/{int(self.player.fuel_capacity)}", 16, fuel_color, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                line_y += SIDEBAR_LINE_HEIGHT
                self.draw_text(f"Credits: {self.player.credits}", 16, (255, 255, 0), sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                line_y += SIDEBAR_LINE_HEIGHT
                self.draw_text(f"Cargo: {sum(self.player.cargo.values())}/{self.player.cargo_space} tons", 16, SIDEBAR_TEXT_COLOR, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                line_y += SIDEBAR_LINE_HEIGHT

                # Display active weapon if available
                if hasattr(self.player, 'get_active_weapon_info'):
                    weapon_info = self.player.get_active_weapon_info()
                    self.draw_text(f"Weapon: {weapon_info}", 16, (255, 200, 0), sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                    line_y += SIDEBAR_LINE_HEIGHT

            line_y += SIDEBAR_LINE_HEIGHT # Spacer

            # Target Info
            if self.targeted_object and self.targeted_object.alive():
                self.draw_text("--- TARGET ---", 18, YELLOW, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                line_y += SIDEBAR_LINE_HEIGHT

                target_name = ""
                if hasattr(self.targeted_object, 'name'): # Planets have .name
                    target_name = self.targeted_object.name
                elif hasattr(self.targeted_object, 'ship_type'): # AI Ships have .ship_type
                    # Enhanced AI ship display with proper ship information
                    ship_name = self.targeted_object.ship.name if hasattr(self.targeted_object, 'ship') else self.targeted_object.ship_type
                    ship_class = self.targeted_object.ship_type.capitalize()
                    ship_size = self.targeted_object.ship.size.capitalize() if hasattr(self.targeted_object, 'ship') else "Unknown"
                    target_name = f"{self.targeted_object.faction_id.capitalize()} {ship_name}"


                self.draw_text(f"Name: {target_name}", 16, SIDEBAR_TEXT_COLOR, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                line_y += SIDEBAR_LINE_HEIGHT

                if isinstance(self.targeted_object, Planet):
                    faction_name = self.factions[self.targeted_object.faction].name if self.targeted_object.faction in self.factions else self.targeted_object.faction
                    self.draw_text(f"Type: Planet", 16, SIDEBAR_TEXT_COLOR, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft"); line_y += SIDEBAR_LINE_HEIGHT
                    self.draw_text(f"Faction: {faction_name}", 16, SIDEBAR_TEXT_COLOR, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft"); line_y += SIDEBAR_LINE_HEIGHT
                    self.draw_text(f"Tech: {self.targeted_object.tech_level}", 16, SIDEBAR_TEXT_COLOR, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft"); line_y += SIDEBAR_LINE_HEIGHT
                    self.draw_text(f"Rep: {self.targeted_object.reputation_with_player}", 16, SIDEBAR_TEXT_COLOR, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft"); line_y += SIDEBAR_LINE_HEIGHT
                    if not self.targeted_object.is_landable:
                        self.draw_text("(Not Landable)", 16, YELLOW, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft"); line_y += SIDEBAR_LINE_HEIGHT

                elif isinstance(self.targeted_object, AIShip):
                    faction_name = self.factions[self.targeted_object.faction_id].name if self.targeted_object.faction_id in self.factions else self.targeted_object.faction_id
                    self.draw_text(f"Type: {ship_size} {ship_class}", 16, SIDEBAR_TEXT_COLOR, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft"); line_y += SIDEBAR_LINE_HEIGHT
                    self.draw_text(f"Class: {ship_class}", 16, SIDEBAR_TEXT_COLOR, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft"); line_y += SIDEBAR_LINE_HEIGHT
                    self.draw_text(f"Size: {ship_size}", 16, SIDEBAR_TEXT_COLOR, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft"); line_y += SIDEBAR_LINE_HEIGHT
                    self.draw_text(f"Faction: {faction_name}", 16, SIDEBAR_TEXT_COLOR, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft"); line_y += SIDEBAR_LINE_HEIGHT

                    relation_val = self.faction_relations.get(self.player_faction_id, {}).get(self.targeted_object.faction_id, 0.0)
                    relation_str = "Neutral"
                    relation_color = WHITE
                    if relation_val < -0.5: relation_str = "Hostile"; relation_color = RED
                    elif relation_val > 0.5: relation_str = "Friendly"; relation_color = GREEN
                    elif relation_val < -0.1: relation_str = "Unfriendly"; relation_color = YELLOW
                    elif relation_val > 0.1: relation_str = "Amicable"; relation_color = LIGHT_BLUE

                    self.draw_text(f"Relation: {relation_str}", 16, relation_color, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft"); line_y += SIDEBAR_LINE_HEIGHT
                    self.draw_text(f"Hull: {self.targeted_object.health}/{self.targeted_object.max_health}", 16, GREEN, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft"); line_y += SIDEBAR_LINE_HEIGHT
                    self.draw_text(f"Shields: {int(self.targeted_object.shields)}/{self.targeted_object.max_shields}", 16, LIGHT_BLUE, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft"); line_y += SIDEBAR_LINE_HEIGHT
                    self.draw_text(f"AI State: {self.targeted_object.ai_state}", 16, SIDEBAR_TEXT_COLOR, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft"); line_y += SIDEBAR_LINE_HEIGHT

            else:
                self.draw_text("No Target Selected", 16, GREY, sidebar_x + SIDEBAR_PADDING, line_y, align="topleft")
                line_y += SIDEBAR_LINE_HEIGHT

            # Status Message (now drawn above sidebar if it's at bottom, or could be part of sidebar)
            if self.status_message and self.status_message_timer > 0:
                self.draw_text(self.status_message, 24, self.status_message_color,
                               (SCREEN_WIDTH - SIDEBAR_WIDTH) / 2, SCREEN_HEIGHT - 40, align="center") # Centered in game view

            pg.display.flip()

    def show_planet_screen(self):
        if not self.docked_planet: self.state = "PLAYING"; return

        self.screen.fill(GREY)
        planet = self.docked_planet
        self.draw_text(f"Landed on: {planet.name}", 48, WHITE, SCREEN_WIDTH / 2, 50, align="center")
        self.draw_text(f"Faction: {planet.faction}, Tech Level: {planet.tech_level}", 24, WHITE, SCREEN_WIDTH / 2, 100, align="center")

        y_offset = 200; option_height = 35; padding = 5
        self.planet_menu_options_rects.clear()

        options_map = []
        if planet.tech_level >= 1:
            options_map.append(("(C) Commodities Market", pg.K_c))
            options_map.append(("(M) Mission Board", pg.K_m))
            options_map.append(("(R) Refuel", pg.K_r))
        if planet.tech_level >= 2: options_map.append(("(O) Outfitter", pg.K_o))
        if planet.tech_level >= 3: options_map.append(("(S) Shipyard (NI)", pg.K_s)); options_map.append(("(T) Space Tavern (NI)", pg.K_t))
        options_map.append(("(U) Undock", pg.K_u))

        for i, (option_text, key_code) in enumerate(options_map):
            text_surf = pg.font.Font(self.font_name, 28).render(option_text, True, WHITE)
            text_rect = text_surf.get_rect(center=(SCREEN_WIDTH / 2, y_offset + i * (option_height + padding)))
            self.screen.blit(text_surf, text_rect)
            self.planet_menu_options_rects.append((text_rect, key_code))

        pg.display.flip()

        waiting_on_planet = True
        while waiting_on_planet and self.running and self.state == "DOCKED":
            self.clock.tick(FPS)
            self.events()

            if self.state != "DOCKED":
                waiting_on_planet = False

            if self.status_message_timer > 0:
                self.update_status_message()
                if self.status_message:
                    msg_rect_bg = pg.Rect(0, SCREEN_HEIGHT - 60, SCREEN_WIDTH, 60)
                    pg.draw.rect(self.screen, GREY, msg_rect_bg)
                    self.draw_text(self.status_message, 20, self.status_message_color, SCREEN_WIDTH / 2, SCREEN_HEIGHT - 30, align="center")
                    pg.display.flip()
                elif not self.status_message and waiting_on_planet :
                    pass

    def show_menu_screen(self):
        self.screen.fill(BLACK)
        self.draw_text(TITLE, 64, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 4, align="center")
        self.draw_text("Press ENTER to Start", 32, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2, align="center")
        self.draw_text("Press ESCAPE to Quit", 22, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT * 3 / 4, align="center")
        pg.display.flip()
        waiting_for_input = True
        while waiting_for_input and self.running:
            self.clock.tick(FPS)
            for event in pg.event.get():
                if event.type == pg.QUIT: waiting_for_input = False; self.running = False; self.state = "QUIT"
                if event.type == pg.KEYDOWN:
                    if event.key == pg.K_RETURN: self.state = "SETUP"; waiting_for_input = False
                    if event.key == pg.K_ESCAPE: waiting_for_input = False; self.running = False; self.state = "QUIT"

    def show_setup_screen(self):
        self.screen.fill(BLACK)
        self.draw_text("Game Setup", 48, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 4 - 50, align="center")

        self.draw_text("Player Name:", 28, WHITE, SCREEN_WIDTH / 2 - 150, SCREEN_HEIGHT / 2 - 60, align="topleft")
        self.player_name_input_rect = pg.Rect(SCREEN_WIDTH / 2 - 150, SCREEN_HEIGHT / 2 - 30, 300, 35)
        pg.draw.rect(self.screen, WHITE, self.player_name_input_rect, 2)
        name_surface = pg.font.Font(self.font_name, 28).render(self.player_name, True, WHITE)
        self.screen.blit(name_surface, (self.player_name_input_rect.x + 5, self.player_name_input_rect.y + 5))
        if self.input_field_active == "PLAYER_NAME":
            pg.draw.rect(self.screen, YELLOW, self.player_name_input_rect, 2)

        self.draw_text("Ship Name:", 28, WHITE, SCREEN_WIDTH / 2 - 150, SCREEN_HEIGHT / 2 + 20, align="topleft")
        self.ship_name_input_rect = pg.Rect(SCREEN_WIDTH / 2 - 150, SCREEN_HEIGHT / 2 + 50, 300, 35)
        pg.draw.rect(self.screen, WHITE, self.ship_name_input_rect, 2)
        ship_name_surface = pg.font.Font(self.font_name, 28).render(self.ship_name, True, WHITE)
        self.screen.blit(ship_name_surface, (self.ship_name_input_rect.x + 5, self.ship_name_input_rect.y + 5))
        if self.input_field_active == "SHIP_NAME":
            pg.draw.rect(self.screen, YELLOW, self.ship_name_input_rect, 2)

        self.draw_text("Number of Systems (20-50):", 28, WHITE, SCREEN_WIDTH / 2 - 150, SCREEN_HEIGHT / 2 + 100, align="topleft")
        self.num_systems_input_rect = pg.Rect(SCREEN_WIDTH / 2 - 150, SCREEN_HEIGHT / 2 + 130, 100, 35)
        pg.draw.rect(self.screen, WHITE, self.num_systems_input_rect, 2)
        num_systems_surface = pg.font.Font(self.font_name, 28).render(str(self.num_systems), True, WHITE)
        self.screen.blit(num_systems_surface, (self.num_systems_input_rect.x + 5, self.num_systems_input_rect.y + 5))
        if self.input_field_active == "NUM_SYSTEMS":
            pg.draw.rect(self.screen, YELLOW, self.num_systems_input_rect, 2)

        self.draw_text("Press ENTER to Start Game", 22, GREEN, SCREEN_WIDTH / 2, SCREEN_HEIGHT * 3 / 4 + 50, align="center")
        self.draw_text("Press ESCAPE to go back to Menu", 22, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT * 3 / 4 + 80, align="center")

        pg.display.flip()

        waiting_for_setup_input = True
        while waiting_for_setup_input and self.running:
            self.clock.tick(FPS)
            for event in pg.event.get():
                if event.type == pg.QUIT:
                    waiting_for_setup_input = False; self.running = False; self.state = "QUIT"

                if event.type == pg.MOUSEBUTTONDOWN:
                    if self.player_name_input_rect.collidepoint(event.pos):
                        self.input_field_active = "PLAYER_NAME"
                    elif self.ship_name_input_rect.collidepoint(event.pos):
                        self.input_field_active = "SHIP_NAME"
                    elif self.num_systems_input_rect.collidepoint(event.pos):
                        self.input_field_active = "NUM_SYSTEMS"
                    else:
                        self.input_field_active = None

                if event.type == pg.KEYDOWN:
                    if event.key == pg.K_ESCAPE:
                        self.state = "MENU"; waiting_for_setup_input = False
                    elif event.key == pg.K_RETURN:
                        if self.player_name and self.ship_name and 20 <= self.num_systems <= 50:
                            self.state = "PLAYING_INIT"; waiting_for_setup_input = False
                        else:
                            self.set_status_message("Please fill all fields correctly.", RED, 120)

                    if self.input_field_active == "PLAYER_NAME":
                        if event.key == pg.K_BACKSPACE:
                            self.player_name = self.player_name[:-1]
                        elif len(self.player_name) < 20:
                            self.player_name += event.unicode
                    elif self.input_field_active == "SHIP_NAME":
                        if event.key == pg.K_BACKSPACE:
                            self.ship_name = self.ship_name[:-1]
                        elif len(self.ship_name) < 20:
                            self.ship_name += event.unicode
                    elif self.input_field_active == "NUM_SYSTEMS":
                        if event.key == pg.K_BACKSPACE:
                            current_num_str = str(self.num_systems)
                            current_num_str = current_num_str[:-1]
                            if current_num_str: self.num_systems = int(current_num_str)
                            else: self.num_systems = 0
                        elif event.unicode.isdigit():
                            new_digit = event.unicode
                            current_num_str = str(self.num_systems)
                            if current_num_str == "0": current_num_str = ""
                            temp_num_str = current_num_str + new_digit
                            if len(temp_num_str) <= 2:
                                temp_num = int(temp_num_str)
                                if 0 <= temp_num <= 20:
                                     self.num_systems = temp_num

            self.screen.fill(BLACK)
            self.draw_text("Game Setup", 48, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 4 - 50, align="center")

            self.draw_text("Player Name:", 28, WHITE, SCREEN_WIDTH / 2 - 150, SCREEN_HEIGHT / 2 - 60, align="topleft")
            pg.draw.rect(self.screen, WHITE, self.player_name_input_rect, 2)
            name_surface = pg.font.Font(self.font_name, 28).render(self.player_name, True, WHITE)
            self.screen.blit(name_surface, (self.player_name_input_rect.x + 5, self.player_name_input_rect.y + 5))
            if self.input_field_active == "PLAYER_NAME": pg.draw.rect(self.screen, YELLOW, self.player_name_input_rect, 2)

            self.draw_text("Ship Name:", 28, WHITE, SCREEN_WIDTH / 2 - 150, SCREEN_HEIGHT / 2 + 20, align="topleft")
            pg.draw.rect(self.screen, WHITE, self.ship_name_input_rect, 2)
            ship_name_surface = pg.font.Font(self.font_name, 28).render(self.ship_name, True, WHITE)
            self.screen.blit(ship_name_surface, (self.ship_name_input_rect.x + 5, self.ship_name_input_rect.y + 5))
            if self.input_field_active == "SHIP_NAME": pg.draw.rect(self.screen, YELLOW, self.ship_name_input_rect, 2)

            self.draw_text("Number of Systems (20-50):", 28, WHITE, SCREEN_WIDTH / 2 - 150, SCREEN_HEIGHT / 2 + 100, align="topleft")
            pg.draw.rect(self.screen, WHITE, self.num_systems_input_rect, 2)
            num_systems_surface = pg.font.Font(self.font_name, 28).render(str(self.num_systems), True, WHITE)
            self.screen.blit(num_systems_surface, (self.num_systems_input_rect.x + 5, self.num_systems_input_rect.y + 5))
            if self.input_field_active == "NUM_SYSTEMS": pg.draw.rect(self.screen, YELLOW, self.num_systems_input_rect, 2)

            self.draw_text("Press ENTER to Start Game", 22, GREEN, SCREEN_WIDTH / 2, SCREEN_HEIGHT * 3 / 4 + 50, align="center")
            self.draw_text("Press ESCAPE to go back to Menu", 22, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT * 3 / 4 + 80, align="center")

            if self.status_message and self.status_message_timer > 0:
                self.draw_text(self.status_message, 20, self.status_message_color, SCREEN_WIDTH / 2, SCREEN_HEIGHT - 30, align="center")
                self.update_status_message()

            pg.display.flip()

    def show_jump_sequence(self):
        """Display a visual sequence for jumping between star systems"""
        if not self.jump_target_system_id or self.jump_target_system_id not in self.galaxy_systems:
            return

        target_system_name = self.galaxy_systems[self.jump_target_system_id].name

        # Setup for jump animation
        self.screen.fill(BLACK)
        self.draw_text(f"JUMPING TO {target_system_name}", 48, BLUE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2, align="center")
        pg.display.flip()
        pg.time.delay(1000)  # 1 second pause

        # Create a simple "warp" effect with stars moving outward
        stars = []
        for _ in range(100):  # Create 100 stars
            x = SCREEN_WIDTH / 2
            y = SCREEN_HEIGHT / 2
            speed = random.uniform(1, 10)
            angle = random.uniform(0, 2 * math.pi)
            stars.append([x, y, speed, angle])

        # Animation loop
        animation_time = 0
        max_animation_time = 2000  # 2 seconds
        while animation_time < max_animation_time:
            self.clock.tick(FPS)
            animation_time += self.clock.get_time()

            # Check for events (allow escape from animation)
            for event in pg.event.get():
                if event.type == pg.QUIT:
                    self.running = False
                    return
                if event.type == pg.KEYDOWN and event.key == pg.K_ESCAPE:
                    return

            # Update star positions
            self.screen.fill(BLACK)
            for star in stars:
                # Move star outward
                star[0] += math.cos(star[3]) * star[2]
                star[1] += math.sin(star[3]) * star[2]

                # Draw star
                size = min(5, star[2] / 2)
                pg.draw.circle(self.screen, WHITE, (int(star[0]), int(star[1])), int(size))

                # Increase speed for acceleration effect
                star[2] *= 1.05

            # Draw text overlay with pulsing effect
            # Pulsing text effect based on animation progress
            pulse = 0.5 + 0.5 * math.sin(animation_time / 100)  # Oscillate between 0.5 and 1.0
            text_color = (0, int(100 + 155 * pulse), 255)  # Pulsing blue
            text_surf = pg.font.Font(self.font_name, 48).render(f"JUMPING TO {target_system_name}", True, text_color)
            text_rect = text_surf.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2))
            self.screen.blit(text_surf, text_rect)

            pg.display.flip()

        # Final flash
        self.screen.fill(WHITE)
        pg.display.flip()
        pg.time.delay(100)
        self.screen.fill(BLACK)
        pg.display.flip()

    def show_go_screen(self):
        if not self.running: return
        self.screen.fill(BLACK)
        self.draw_text("GAME OVER", 64, RED, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 4, align="center")
        self.draw_text("Press ENTER to Play Again", 32, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2, align="center")
        self.draw_text("Press ESCAPE to Quit", 22, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT * 3 / 4, align="center")
        pg.display.flip()
        waiting_for_input = True
        while waiting_for_input and self.running:
            self.clock.tick(FPS)
            for event in pg.event.get():
                if event.type == pg.QUIT: waiting_for_input = False; self.running = False; self.state = "QUIT"
                if event.type == pg.KEYDOWN:
                    if event.key == pg.K_RETURN: self.state = "MENU"; waiting_for_input = False
                    if event.key == pg.K_ESCAPE: waiting_for_input = False; self.running = False; self.state = "QUIT"

    def show_system_map_screen(self):
        map_bg_color = (20, 20, 40) # Dark blue
        system_color = LIGHT_BLUE
        current_system_color = YELLOW
        target_system_color = GREEN
        connection_color = (100, 100, 120) # Greyish blue
        text_color = WHITE
        node_radius = 8
        node_selected_radius = 12
        unexplored_hint_color = (60,60,60)
        mission_marker_color = (255, 100, 255)  # Magenta for mission destinations

        self.screen.fill(map_bg_color)
        self.draw_text("Galaxy Map", 48, WHITE, SCREEN_WIDTH / 2, 30, align="center")
        if self.current_system_id and self.current_system_id in self.galaxy_systems:
             current_sys_name = self.galaxy_systems[self.current_system_id].name
             self.draw_text(f"Current System: {current_sys_name}", 24, current_system_color, SCREEN_WIDTH / 2, 70, align="center")

        # Show jump target info if selected
        if self.jump_target_system_id and self.jump_target_system_id in self.galaxy_systems:
            target_sys_name = self.galaxy_systems[self.jump_target_system_id].name
            self.draw_text(f"Jump Target: {target_sys_name}", 24, target_system_color, SCREEN_WIDTH / 2, 100, align="center")

            # Add a jump button
            jump_button_rect = pg.Rect(SCREEN_WIDTH / 2 - 100, 130, 200, 40)
            pg.draw.rect(self.screen, GREEN, jump_button_rect)
            pg.draw.rect(self.screen, WHITE, jump_button_rect, 2)  # White border
            self.draw_text("JUMP (J)", 20, BLACK, SCREEN_WIDTH / 2, 150, align="center")

        self.draw_text("Click adjacent explored system to select jump target. Press M or ESC to close.", 18, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT - 30, align="center")

        map_node_rects = {}

        # Draw connections
        for sys_id, system_obj in self.galaxy_systems.items():
            if not system_obj.is_explored:
                continue
            for connected_id in system_obj.connections:
                if connected_id in self.galaxy_systems:
                    target_sys_obj = self.galaxy_systems[connected_id]
                    line_color_to_draw = connection_color
                    if not target_sys_obj.is_explored:
                        # Only draw hint lines from the current system to its direct unexplored connections
                        if sys_id == self.current_system_id:
                            line_color_to_draw = unexplored_hint_color
                        else:
                            continue # Don't draw lines between two non-current explored systems to an unexplored one

                    pg.draw.line(self.screen, line_color_to_draw, system_obj.pos, target_sys_obj.pos, 1)

        # Get mission markers
        mission_markers = self.mission_system.get_mission_markers()
        mission_systems = [marker[0] for marker in mission_markers]

        # Draw systems
        for sys_id, system_obj in self.galaxy_systems.items():
            if system_obj.is_explored:
                color = current_system_color if sys_id == self.current_system_id else system_color
                radius = node_selected_radius if sys_id == self.jump_target_system_id else node_radius

                pg.draw.circle(self.screen, color, (int(system_obj.pos.x), int(system_obj.pos.y)), radius)

                # Draw mission marker if this system has an active mission
                if sys_id in mission_systems:
                    pg.draw.circle(self.screen, mission_marker_color,
                                 (int(system_obj.pos.x), int(system_obj.pos.y)), radius + 3, 2)

                system_obj.map_rect = pg.Rect(system_obj.pos.x - radius, system_obj.pos.y - radius, radius*2, radius*2)
                map_node_rects[sys_id] = system_obj.map_rect

                self.draw_text(system_obj.name, 16, text_color, system_obj.pos.x, system_obj.pos.y + radius + 5, align="midtop")
                if sys_id == self.jump_target_system_id:
                     self.draw_text("(Targeted)", 14, GREEN, system_obj.pos.x, system_obj.pos.y + radius + 20, align="midtop")
            else: # Unexplored system
                # If this unexplored system is connected to the current system, make it selectable
                if self.current_system_id and self.current_system_id in self.galaxy_systems and \
                   sys_id in self.galaxy_systems[self.current_system_id].connections:
                    # Use a different color for unexplored but selectable systems
                    selectable_unexplored_color = (100, 100, 180)  # Light purple-blue

                    # If this is the jump target, highlight it
                    if sys_id == self.jump_target_system_id:
                        pg.draw.circle(self.screen, GREEN, (int(system_obj.pos.x), int(system_obj.pos.y)), node_radius)
                        self.draw_text("?", 12, BLACK, system_obj.pos.x, system_obj.pos.y, align="center")
                    else:
                        pg.draw.circle(self.screen, selectable_unexplored_color, (int(system_obj.pos.x), int(system_obj.pos.y)), node_radius // 1.5)
                        self.draw_text("?", 12, WHITE, system_obj.pos.x, system_obj.pos.y, align="center")

                    # Create a map_rect for clicking
                    system_obj.map_rect = pg.Rect(system_obj.pos.x - node_radius, system_obj.pos.y - node_radius, node_radius*2, node_radius*2)
                    map_node_rects[sys_id] = system_obj.map_rect

                    # Show system name for unexplored but selectable systems
                    self.draw_text(system_obj.name, 16, selectable_unexplored_color, system_obj.pos.x, system_obj.pos.y + node_radius + 5, align="midtop")
                    if sys_id == self.jump_target_system_id:
                        self.draw_text("(Unexplored)", 14, GREEN, system_obj.pos.x, system_obj.pos.y + node_radius + 20, align="midtop")

                # Other unexplored systems that aren't connected to current system
                else:
                    pg.draw.circle(self.screen, unexplored_hint_color, (int(system_obj.pos.x), int(system_obj.pos.y)), node_radius // 3)
                    # No text or interaction for these


        pg.display.flip()

        waiting_on_map = True
        while waiting_on_map and self.running:
            self.clock.tick(FPS)
            for event in pg.event.get():
                if event.type == pg.QUIT:
                    waiting_on_map = False; self.running = False; self.state = "QUIT"
                if event.type == pg.KEYDOWN:
                    if event.key == pg.K_ESCAPE or event.key == pg.K_m:
                        self.state = "PLAYING"; waiting_on_map = False
                        self.playing = True
                    elif event.key == pg.K_j:
                        if self.jump_target_system_id:
                            current_sys = self.galaxy_systems[self.current_system_id]
                            if self.jump_target_system_id in current_sys.connections:
                                # Allow jumping to any directly connected system, even if unexplored
                                # NEW: Check fuel for jump
                                fuel_cost = 20  # Base fuel cost per jump
                                if self.player.ship.consume_fuel(fuel_cost):
                                    self.set_status_message(f"Jump to {self.galaxy_systems[self.jump_target_system_id].name} initiated.", GREEN)
                                    self.state = "JUMPING"
                                    waiting_on_map = False
                                    return
                                else:
                                    self.set_status_message("Insufficient fuel for jump!", RED)
                            else:
                                self.set_status_message("Cannot jump: Target system not directly connected.", YELLOW)
                        else:
                            self.set_status_message("No jump target selected.", YELLOW)

                if event.type == pg.MOUSEBUTTONDOWN and event.button == 1:
                    mouse_pos = pg.mouse.get_pos()

                    # Check if jump button was clicked
                    if self.jump_target_system_id and self.jump_target_system_id in self.galaxy_systems:
                        jump_button_rect = pg.Rect(SCREEN_WIDTH / 2 - 100, 130, 200, 40)
                        if jump_button_rect.collidepoint(mouse_pos):
                            current_sys = self.galaxy_systems[self.current_system_id]
                            if self.jump_target_system_id in current_sys.connections:
                                # Allow jumping to any directly connected system, even if unexplored
                                # NEW: Check fuel for jump
                                fuel_cost = 20  # Base fuel cost per jump
                                if self.player.ship.consume_fuel(fuel_cost):
                                    self.set_status_message(f"Jump to {self.galaxy_systems[self.jump_target_system_id].name} initiated.", GREEN)
                                    self.state = "JUMPING"
                                    waiting_on_map = False
                                    return
                                else:
                                    self.set_status_message("Insufficient fuel for jump!", RED)
                            else:
                                self.set_status_message("Cannot jump: Target system not directly connected.", YELLOW)

                    # Check if a system was clicked
                    clicked_on_system_id = None
                    # Check against all rects stored in map_node_rects, which includes hints
                    for s_id, rect in map_node_rects.items():
                        if rect and rect.collidepoint(mouse_pos): # Ensure rect exists
                            clicked_on_system_id = s_id
                            break

                    if clicked_on_system_id:
                        # Check if the system is directly connected to current system
                        current_sys_obj = self.galaxy_systems[self.current_system_id]
                        if clicked_on_system_id != self.current_system_id and clicked_on_system_id in current_sys_obj.connections:
                            # Allow selecting any directly connected system, even if unexplored
                            self.jump_target_system_id = clicked_on_system_id
                            target_sys_name = self.galaxy_systems[clicked_on_system_id].name

                            if self.galaxy_systems[clicked_on_system_id].is_explored:
                                self.set_status_message(f"Jump target: {target_sys_name}. Press J in space to jump or click JUMP button.", WHITE)
                            else:
                                self.set_status_message(f"Jump target: {target_sys_name} (Unexplored). Press J in space to jump or click JUMP button.", LIGHT_BLUE)
                        elif clicked_on_system_id == self.current_system_id:
                            # Clicked on current system
                            self.jump_target_system_id = None
                        else:
                            # Not directly connected
                            self.set_status_message("Cannot select: Not a direct connection.", YELLOW)
                            self.jump_target_system_id = None

                        self.show_system_map_screen()
                        waiting_on_map = False
                        return

                    else: # Clicked on empty map space
                        if self.jump_target_system_id is not None:
                            self.jump_target_system_id = None
                            self.show_system_map_screen()
                            waiting_on_map = False
                            return

            if self.status_message_timer > 0:
                self.update_status_message()
                msg_area_rect = pg.Rect(0, SCREEN_HEIGHT - 50, SCREEN_WIDTH, 50)
                pg.draw.rect(self.screen, map_bg_color, msg_area_rect)
                if self.status_message:
                     self.draw_text(self.status_message, 18, self.status_message_color, SCREEN_WIDTH / 2, SCREEN_HEIGHT - 30, align="center")
                pg.display.update(msg_area_rect)

            if not waiting_on_map:
                if self.state == "PLAYING":
                    self.playing = True

if __name__ == '__main__':
    g = Game()
    game_fully_initialized = False

    while g.running:
        if g.state == "MENU":
            g.show_menu_screen()
            game_fully_initialized = False # Reset for new game
        elif g.state == "SETUP":
            g.show_setup_screen()
        elif g.state == "PLAYING_INIT": # New state to ensure setup happens once
            if not game_fully_initialized:
                g.new_game_setup()
                game_fully_initialized = True
            g.state = "PLAYING"
            g.playing = True
            g.game_loop_run()
            if g.running and g.state == "PLAYING":
                g.state = "GAME_OVER"
        elif g.state == "SYSTEM_MAP":
            g.show_system_map_screen()
        elif g.state == "PLAYING":
            if not g.playing:
                 g.playing = True
            g.game_loop_run()
            if g.running and g.state == "PLAYING":
                 g.state = "GAME_OVER"
        elif g.state == "DOCKED":
            g.show_planet_screen()
        elif g.state == "JUMPING":
            if g.jump_target_system_id and g.jump_target_system_id in g.galaxy_systems:
                # Show jump animation/sequence
                g.show_jump_sequence()
                g.current_system_id = g.jump_target_system_id
                g.galaxy_systems[g.current_system_id].is_explored = True
                g.setup_current_system_objects()
                g.state = "PLAYING"
                g.jump_target_system_id = None
            else:
                g.state = "PLAYING"
        elif g.state == "SHIP_MENU":
            next_state_after_ship_menu = g.ship_menu.show_ship_menu(g.screen)
            # If g.running is false (quit from menu), this won't matter as main loop will exit.
            if g.running:
                g.state = next_state_after_ship_menu
                if g.state == "PLAYING":
                    g.playing = True # Resume the game loop if returning to PLAYING state
        elif g.state == "TRADING":
            next_state = g.trading_system.show_trading_screen(g.screen, g.docked_planet)
            g.state = next_state
        elif g.state == "OUTFITTER":
            if g.outfitter.open(g.docked_planet):
                next_state = g.outfitter.run(g.screen)
            else:
                next_state = "DOCKED"
            if g.running: # Ensure game didn't quit from outfitter
                g.state = next_state
        elif g.state == "SHIPYARD":
            next_state_after_shipyard = g.shipyard.show_shipyard_screen(g.screen)
            if g.running: # Ensure game didn't quit from shipyard
                g.state = next_state_after_shipyard
                # Shipyard currently returns "DOCKED", so no need to set g.playing = True here
                # as the DOCKED state loop handles its own logic.
        elif g.state == "MISSION_BOARD":
            next_state = g.mission_system.show_mission_board(g.screen, g.docked_planet)
            if g.running:
                g.state = next_state
        elif g.state == "GAME_OVER":
            g.show_go_screen()
        elif g.state == "QUIT":
            g.running = False

    pg.quit()
