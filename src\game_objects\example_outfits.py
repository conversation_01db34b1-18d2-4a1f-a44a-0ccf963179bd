"""
Example outfits for the standardized system
This will replace the old outfits.py with proper examples
"""

from .standardized_outfits import *

def create_example_outfits():
    """Create and register example outfits for testing and gameplay."""

    # =============================================================================
    # WEAPONS
    # =============================================================================

    # Energy Weapons - Fixed Mount
    laser_cannon = Weapon("laser_cannon", "Laser Cannon", WEAPON_ENERGY)
    laser_cannon.damage = 15
    laser_cannon.fire_rate = 3.0
    laser_cannon.range = 400
    laser_cannon.energy_usage = 8
    laser_cannon.mount_type = MOUNT_TYPE_FIXED
    laser_cannon.damage_type = DAMAGE_TYPE_ENERGY
    laser_cannon.projectile_behavior = BEHAVIOR_INSTANT
    laser_cannon.space_required = 1
    laser_cannon.cost = 2000
    laser_cannon.description = "Basic energy weapon. Reliable and efficient."
    laser_cannon.beam_color = (255, 100, 100)
    register_outfit(laser_cannon)

    # Pulse Laser - Faster firing, less damage
    pulse_laser = Weapon("pulse_laser", "Pulse Laser", WEAPON_ENERGY)
    pulse_laser.damage = 8
    pulse_laser.fire_rate = 6.0
    pulse_laser.range = 350
    pulse_laser.energy_usage = 5
    pulse_laser.mount_type = MOUNT_TYPE_FIXED
    pulse_laser.damage_type = DAMAGE_TYPE_ENERGY
    pulse_laser.projectile_behavior = BEHAVIOR_INSTANT
    pulse_laser.space_required = 1
    pulse_laser.cost = 1500
    pulse_laser.description = "Fast-firing laser for rapid engagement."
    pulse_laser.beam_color = (100, 255, 100)
    register_outfit(pulse_laser)

    # Heavy Laser - Slow but powerful
    heavy_laser = Weapon("heavy_laser", "Heavy Laser", WEAPON_ENERGY)
    heavy_laser.damage = 35
    heavy_laser.fire_rate = 1.2
    heavy_laser.range = 450
    heavy_laser.energy_usage = 20
    heavy_laser.mount_type = MOUNT_TYPE_FIXED
    heavy_laser.damage_type = DAMAGE_TYPE_ENERGY
    heavy_laser.projectile_behavior = BEHAVIOR_INSTANT
    heavy_laser.space_required = 3
    heavy_laser.cost = 8000
    heavy_laser.description = "High-powered laser cannon for capital ships."
    heavy_laser.ship_size_restrictions = ["medium", "large", "capital"]
    heavy_laser.beam_color = (255, 255, 100)
    register_outfit(heavy_laser)

    # Laser Turret - Can track targets
    laser_turret = Weapon("laser_turret", "Laser Turret", WEAPON_ENERGY)
    laser_turret.damage = 12
    laser_turret.fire_rate = 2.5
    laser_turret.range = 380
    laser_turret.energy_usage = 10
    laser_turret.mount_type = MOUNT_TYPE_TURRET
    laser_turret.damage_type = DAMAGE_TYPE_ENERGY
    laser_turret.projectile_behavior = BEHAVIOR_INSTANT
    laser_turret.space_required = 2
    laser_turret.cost = 5000
    laser_turret.description = "Automated laser turret with 360° firing arc."
    laser_turret.ship_size_restrictions = ["medium", "large", "capital"]
    laser_turret.beam_color = (255, 150, 150)
    register_outfit(laser_turret)

    # Beam Laser - Continuous damage
    beam_laser = Weapon("beam_laser", "Beam Laser", WEAPON_BEAM)
    beam_laser.damage = 25  # Damage per second
    beam_laser.fire_rate = 1.0  # Always firing when triggered
    beam_laser.range = 300
    beam_laser.energy_usage = 15  # Per second
    beam_laser.mount_type = MOUNT_TYPE_FIXED
    beam_laser.damage_type = DAMAGE_TYPE_ENERGY
    beam_laser.projectile_behavior = BEHAVIOR_BEAM
    beam_laser.space_required = 2
    beam_laser.cost = 6000
    beam_laser.description = "Continuous beam weapon for sustained damage."
    beam_laser.beam_color = (150, 150, 255)
    register_outfit(beam_laser)

    # =============================================================================
    # MISSILE LAUNCHERS
    # =============================================================================

    # Light Missile Rack
    missile_rack = Weapon("missile_rack", "Missile Rack", WEAPON_LAUNCHER)
    missile_rack.damage = 0  # Damage comes from ammunition
    missile_rack.fire_rate = 1.5
    missile_rack.range = 600
    missile_rack.energy_usage = 2
    missile_rack.mount_type = MOUNT_TYPE_FIXED
    missile_rack.uses_ammo = True
    missile_rack.ammo_type = "light_missile"
    missile_rack.max_ammo = 20
    missile_rack.current_ammo = 0
    missile_rack.space_required = 2
    missile_rack.cost = 3000
    missile_rack.description = "Launches light missiles for versatile combat."
    register_outfit(missile_rack)

    # Heavy Missile Launcher
    heavy_launcher = Weapon("heavy_launcher", "Heavy Missile Launcher", WEAPON_LAUNCHER)
    heavy_launcher.damage = 0
    heavy_launcher.fire_rate = 0.8
    heavy_launcher.range = 800
    heavy_launcher.energy_usage = 5
    heavy_launcher.mount_type = MOUNT_TYPE_FIXED
    heavy_launcher.uses_ammo = True
    heavy_launcher.ammo_type = "heavy_missile"
    heavy_launcher.max_ammo = 10
    heavy_launcher.current_ammo = 0
    heavy_launcher.space_required = 4
    heavy_launcher.cost = 8000
    heavy_launcher.description = "Launches heavy missiles for maximum damage."
    heavy_launcher.ship_size_restrictions = ["medium", "large", "capital"]
    register_outfit(heavy_launcher)

    # Torpedo Tube
    torpedo_tube = Weapon("torpedo_tube", "Torpedo Tube", WEAPON_LAUNCHER)
    torpedo_tube.damage = 0
    torpedo_tube.fire_rate = 0.5
    torpedo_tube.range = 1000
    torpedo_tube.energy_usage = 8
    torpedo_tube.mount_type = MOUNT_TYPE_FIXED
    torpedo_tube.uses_ammo = True
    torpedo_tube.ammo_type = "torpedo"
    torpedo_tube.max_ammo = 5
    torpedo_tube.current_ammo = 0
    torpedo_tube.space_required = 6
    torpedo_tube.cost = 15000
    torpedo_tube.description = "Capital ship weapon for launching heavy torpedoes."
    torpedo_tube.ship_size_restrictions = ["large", "capital"]
    register_outfit(torpedo_tube)

    # =============================================================================
    # AMMUNITION
    # =============================================================================

    # Light Missiles - Dumbfire
    light_missile = Ammunition("light_missile", "Light Missile", "light_missile")
    light_missile.quantity = 10
    light_missile.damage = 40
    light_missile.projectile_speed = 8
    light_missile.projectile_behavior = BEHAVIOR_DUMBFIRE
    light_missile.explosion_radius = 15
    light_missile.cost = 150
    light_missile.description = "Basic unguided missile."
    register_outfit(light_missile)

    # Guided Light Missiles
    guided_missile = Ammunition("guided_missile", "Guided Missile", "light_missile")
    guided_missile.quantity = 8
    guided_missile.damage = 35
    guided_missile.projectile_speed = 6
    guided_missile.projectile_behavior = BEHAVIOR_GUIDED
    guided_missile.tracking_strength = 0.6
    guided_missile.explosion_radius = 15
    guided_missile.cost = 300
    guided_missile.description = "Missile with basic guidance system."
    register_outfit(guided_missile)

    # Smart Missiles - Advanced guidance
    smart_missile = Ammunition("smart_missile", "Smart Missile", "light_missile")
    smart_missile.quantity = 6
    smart_missile.damage = 30
    smart_missile.projectile_speed = 5
    smart_missile.projectile_behavior = BEHAVIOR_GUIDED
    smart_missile.tracking_strength = 0.9
    smart_missile.explosion_radius = 15
    smart_missile.cost = 500
    smart_missile.description = "Advanced missile with superior tracking."
    register_outfit(smart_missile)

    # Heavy Missiles
    heavy_missile = Ammunition("heavy_missile", "Heavy Missile", "heavy_missile")
    heavy_missile.quantity = 5
    heavy_missile.damage = 100
    heavy_missile.projectile_speed = 6
    heavy_missile.projectile_behavior = BEHAVIOR_DUMBFIRE
    heavy_missile.explosion_radius = 30
    heavy_missile.cost = 800
    heavy_missile.description = "Large unguided missile with massive warhead."
    register_outfit(heavy_missile)

    # Torpedoes
    torpedo = Ammunition("torpedo", "Torpedo", "torpedo")
    torpedo.quantity = 2
    torpedo.damage = 250
    torpedo.projectile_speed = 4
    torpedo.projectile_behavior = BEHAVIOR_GUIDED
    torpedo.tracking_strength = 0.7
    torpedo.explosion_radius = 50
    torpedo.cost = 2000
    torpedo.description = "Heavy guided torpedo for capital ship combat."
    register_outfit(torpedo)

    # =============================================================================
    # POINT DEFENSE SYSTEMS
    # =============================================================================

    # Basic Point Defense Turret
    point_defense_turret = DefenseOutfit("point_defense_turret", "Point Defense Turret", DEFENSE_POINT_DEFENSE)
    point_defense_turret.mount_type = DEFENSE_MOUNT_TURRET
    point_defense_turret.defense_range = 300
    point_defense_turret.fire_rate = 4.0
    point_defense_turret.accuracy = 0.75
    point_defense_turret.power_cost = 8.0
    point_defense_turret.target_types = ["projectiles"]
    point_defense_turret.space_required = 2
    point_defense_turret.cost = 4000
    point_defense_turret.energy_drain = 3.0
    point_defense_turret.description = "Automated turret system for intercepting missiles and projectiles."
    register_outfit(point_defense_turret)

    # Anti-Missile Gun (Fixed)
    anti_missile_gun = DefenseOutfit("anti_missile_gun", "Anti-Missile Gun", DEFENSE_POINT_DEFENSE)
    anti_missile_gun.mount_type = DEFENSE_MOUNT_FIXED
    anti_missile_gun.defense_range = 250
    anti_missile_gun.fire_rate = 6.0
    anti_missile_gun.accuracy = 0.8
    anti_missile_gun.power_cost = 5.0
    anti_missile_gun.target_types = ["projectiles"]
    anti_missile_gun.firing_arc = 60
    anti_missile_gun.space_required = 1
    anti_missile_gun.cost = 2500
    anti_missile_gun.energy_drain = 2.0
    anti_missile_gun.description = "Fixed-mount anti-missile system with high accuracy in forward arc."
    register_outfit(anti_missile_gun)

    # Flak Cannon (Area Defense)
    flak_cannon = DefenseOutfit("flak_cannon", "Flak Cannon", DEFENSE_POINT_DEFENSE)
    flak_cannon.mount_type = DEFENSE_MOUNT_TURRET
    flak_cannon.defense_range = 400
    flak_cannon.fire_rate = 2.0
    flak_cannon.accuracy = 0.6  # Lower accuracy but area effect
    flak_cannon.power_cost = 12.0
    flak_cannon.target_types = ["projectiles", "ships"]
    flak_cannon.space_required = 3
    flak_cannon.cost = 6000
    flak_cannon.energy_drain = 4.0
    flak_cannon.ship_size_restrictions = ["medium", "large", "capital"]
    flak_cannon.description = "Area-effect defense system effective against multiple targets."
    register_outfit(flak_cannon)

    # ECM Suite
    ecm_suite = DefenseOutfit("ecm_suite", "ECM Suite", DEFENSE_ECM)
    ecm_suite.mount_type = DEFENSE_MOUNT_OMNIDIRECTIONAL
    ecm_suite.jam_strength = 0.4
    ecm_suite.jam_resistance = 0.2
    ecm_suite.power_cost = 0  # Passive system
    ecm_suite.space_required = 2
    ecm_suite.cost = 3500
    ecm_suite.energy_drain = 5.0
    ecm_suite.description = "Electronic countermeasures to disrupt enemy targeting and sensors."
    register_outfit(ecm_suite)

    # Reactive Armor
    reactive_armor = DefenseOutfit("reactive_armor", "Reactive Armor", DEFENSE_REACTIVE)
    reactive_armor.mount_type = DEFENSE_MOUNT_PASSIVE
    reactive_armor.armor_boost = 25
    reactive_armor.damage_reduction = 0.15  # 15% damage reduction
    reactive_armor.activation_chance = 0.3  # 30% chance to activate
    reactive_armor.activation_threshold = 20  # Minimum 20 damage to trigger
    reactive_armor.space_required = 2
    reactive_armor.cost = 2800
    reactive_armor.description = "Advanced armor that reacts to incoming damage."
    register_outfit(reactive_armor)

    # Basic Shield Generator
    basic_shield = DefenseOutfit("basic_shield", "Basic Shield Generator", DEFENSE_SHIELDS)
    basic_shield.shield_boost = 50
    basic_shield.shield_recharge_boost = 1.0
    basic_shield.energy_drain = 2.0
    basic_shield.space_required = 1
    basic_shield.cost = 1500
    basic_shield.description = "Standard shield generator for small ships."
    register_outfit(basic_shield)

    # Advanced Shield Generator
    advanced_shield = DefenseOutfit("advanced_shield", "Advanced Shield Generator", DEFENSE_SHIELDS)
    advanced_shield.shield_boost = 100
    advanced_shield.shield_recharge_boost = 2.0
    advanced_shield.energy_drain = 4.0
    advanced_shield.space_required = 2
    advanced_shield.cost = 4000
    advanced_shield.min_tech_level = 3
    advanced_shield.description = "High-capacity shield generator."
    register_outfit(advanced_shield)

    # Armor Plating
    armor_plating = DefenseOutfit("armor_plating", "Armor Plating", DEFENSE_ARMOR)
    armor_plating.armor_boost = 30
    armor_plating.damage_reduction = 0.05  # 5% damage reduction
    armor_plating.mass = 2.0  # Adds mass to ship
    armor_plating.space_required = 1
    armor_plating.cost = 800
    armor_plating.description = "Additional armor plating for improved survivability."
    register_outfit(armor_plating)

    # =============================================================================
    # ENGINE OUTFITS
    # =============================================================================

    # Afterburner
    afterburner = EngineOutfit("afterburner", "Afterburner", ENGINE_AFTERBURNER)
    afterburner.max_speed_boost = 3.0
    afterburner.energy_drain = 10.0  # High energy consumption when active
    afterburner.space_required = 2
    afterburner.cost = 3000
    afterburner.description = "Provides temporary speed boost at high energy cost."
    register_outfit(afterburner)

    # Enhanced Thrusters
    enhanced_thrusters = EngineOutfit("enhanced_thrusters", "Enhanced Thrusters", ENGINE_THRUSTER)
    enhanced_thrusters.acceleration_boost = 0.3
    enhanced_thrusters.space_required = 2
    enhanced_thrusters.cost = 2500
    enhanced_thrusters.description = "Improved thruster system for better acceleration."
    register_outfit(enhanced_thrusters)

    # Maneuvering Jets
    maneuvering_jets = EngineOutfit("maneuvering_jets", "Maneuvering Jets", ENGINE_STEERING)
    maneuvering_jets.turn_rate_boost = 0.4
    maneuvering_jets.space_required = 1
    maneuvering_jets.cost = 1800
    maneuvering_jets.description = "Auxiliary thrusters for improved maneuverability."
    register_outfit(maneuvering_jets)

    # Reverse Thrusters - NEW!
    reverse_thrusters = EngineOutfit("reverse_thrusters", "Reverse Thrusters", ENGINE_THRUSTER)
    reverse_thrusters.acceleration_boost = 0.1  # Small acceleration boost
    reverse_thrusters.space_required = 2
    reverse_thrusters.cost = 3500
    reverse_thrusters.min_tech_level = 2
    reverse_thrusters.description = "Specialized reverse thruster system. Allows direct reverse thrust using S key."
    register_outfit(reverse_thrusters)

    # Maneuvering Thrusters - Advanced version with reverse capability
    maneuvering_thrusters = EngineOutfit("maneuvering_thrusters", "Maneuvering Thrusters", ENGINE_THRUSTER)
    maneuvering_thrusters.acceleration_boost = 0.2
    maneuvering_thrusters.turn_rate_boost = 0.3
    maneuvering_thrusters.space_required = 3
    maneuvering_thrusters.cost = 5000
    maneuvering_thrusters.min_tech_level = 3
    maneuvering_thrusters.description = "Advanced maneuvering system with omnidirectional thrust capability."
    register_outfit(maneuvering_thrusters)

    # Advanced Engines - High-end with reverse capability
    advanced_engines = EngineOutfit("advanced_engines", "Advanced Engines", ENGINE_THRUSTER)
    advanced_engines.acceleration_boost = 0.5
    advanced_engines.max_speed_boost = 2.0
    advanced_engines.turn_rate_boost = 0.2
    advanced_engines.space_required = 4
    advanced_engines.cost = 12000
    advanced_engines.min_tech_level = 4
    advanced_engines.ship_size_restrictions = ["medium", "large", "capital"]
    advanced_engines.description = "Top-tier engine system with full directional control and enhanced performance."
    register_outfit(advanced_engines)

    # =============================================================================
    # ELECTRONICS OUTFITS
    # =============================================================================

    # =============================================================================
    # ADVANCED ELECTRONICS OUTFITS
    # =============================================================================

    # Military Sensor Array
    military_sensors = ElectronicsOutfit("military_sensors", "Military Sensor Array", "sensors")
    military_sensors.sensor_range_boost = 500.0
    military_sensors.sensor_resolution_boost = 0.5
    military_sensors.stealth_detection = 0.3
    military_sensors.sensor_sweep_rate = 2.0
    military_sensors.space_required = 2
    military_sensors.cost = 3500
    military_sensors.energy_drain = 4.0
    military_sensors.min_tech_level = 3
    military_sensors.description = "Advanced military-grade sensor system with stealth detection."
    register_outfit(military_sensors)

    # Fire Control Computer
    fire_control = ElectronicsOutfit("fire_control", "Fire Control Computer", "targeting")
    fire_control.targeting_boost = 0.25  # 25% accuracy improvement
    fire_control.targeting_speed_boost = 0.4
    fire_control.multi_target_capacity = 3
    fire_control.target_prediction = 0.3
    fire_control.space_required = 2
    fire_control.cost = 4000
    fire_control.energy_drain = 3.0
    fire_control.min_tech_level = 3
    fire_control.description = "Advanced targeting system with multi-target capability."
    register_outfit(fire_control)

    # Electronic Warfare Suite
    ew_suite = ElectronicsOutfit("ew_suite", "Electronic Warfare Suite", "jamming")
    ew_suite.jamming_strength = 0.6
    ew_suite.jam_resistance = 0.4
    ew_suite.jam_range = 800.0
    ew_suite.frequency_agility = 0.5
    ew_suite.decoy_strength = 0.3
    ew_suite.signal_analysis = 0.4
    ew_suite.space_required = 3
    ew_suite.cost = 8000
    ew_suite.energy_drain = 8.0
    ew_suite.min_tech_level = 4
    ew_suite.ship_size_restrictions = ["medium", "large", "capital"]
    ew_suite.description = "Comprehensive electronic warfare system for military vessels."
    register_outfit(ew_suite)

    # Quantum Communications
    quantum_comms = ElectronicsOutfit("quantum_comms", "Quantum Communications", "communication")
    quantum_comms.communication_range = 10000.0
    quantum_comms.encryption_strength = 0.9
    quantum_comms.bandwidth = 5.0
    quantum_comms.relay_capability = True
    quantum_comms.space_required = 2
    quantum_comms.cost = 6000
    quantum_comms.energy_drain = 2.0
    quantum_comms.min_tech_level = 4
    quantum_comms.description = "Instantaneous quantum-encrypted communication system."
    register_outfit(quantum_comms)

    # Deep Space Scanner
    deep_scanner = ElectronicsOutfit("deep_scanner", "Deep Space Scanner", "scanning")
    deep_scanner.scan_speed_boost = 0.6
    deep_scanner.scan_detail_boost = 0.8
    deep_scanner.scan_penetration = 0.4
    deep_scanner.biological_scan = True
    deep_scanner.space_required = 2
    deep_scanner.cost = 3000
    deep_scanner.energy_drain = 3.5
    deep_scanner.min_tech_level = 2
    deep_scanner.description = "Advanced scanner for detailed analysis of ships and cargo."
    register_outfit(deep_scanner)

    # Targeting Computer
    targeting_computer = ElectronicsOutfit("targeting_computer", "Targeting Computer", "targeting")
    targeting_computer.targeting_boost = 0.15  # 15% accuracy improvement
    targeting_computer.space_required = 1
    targeting_computer.cost = 1200
    targeting_computer.energy_drain = 1.5
    targeting_computer.description = "Advanced targeting system that improves weapon accuracy."
    register_outfit(targeting_computer)

    # ECM System
    ecm_system = ElectronicsOutfit("ecm_system", "ECM System", "jamming")
    ecm_system.jamming_strength = 0.3  # 30% jamming resistance
    ecm_system.space_required = 2
    ecm_system.cost = 2000
    ecm_system.energy_drain = 3.0
    ecm_system.description = "Electronic countermeasures to disrupt enemy targeting."
    register_outfit(ecm_system)

    # =============================================================================
    # UTILITY OUTFITS
    # =============================================================================

    # Cargo Pod
    cargo_pod = UtilityOutfit("cargo_pod", "Cargo Pod", "cargo")
    cargo_pod.cargo_space_boost = 15
    cargo_pod.space_required = 1
    cargo_pod.cost = 500
    cargo_pod.description = "Additional cargo storage compartment."
    register_outfit(cargo_pod)

    # Fuel Tank
    fuel_tank = UtilityOutfit("fuel_tank", "Fuel Tank", "fuel")
    fuel_tank.fuel_capacity_boost = 100
    fuel_tank.space_required = 1
    fuel_tank.cost = 300
    fuel_tank.description = "Extended fuel storage for long-range travel."
    register_outfit(fuel_tank)

    # Energy Generator
    energy_generator = UtilityOutfit("energy_generator", "Energy Generator", "power")
    energy_generator.energy_generation = 5.0  # 5 energy per second
    energy_generator.space_required = 2
    energy_generator.cost = 1500
    energy_generator.description = "Generates additional energy for ship systems."
    register_outfit(energy_generator)

    # Crew Quarters
    crew_quarters = UtilityOutfit("crew_quarters", "Crew Quarters", "crew")
    crew_quarters.crew_capacity_boost = 2
    crew_quarters.space_required = 3
    crew_quarters.cost = 800
    crew_quarters.description = "Additional crew quarters for larger crew capacity."
    register_outfit(crew_quarters)

    print("Created and registered all example outfits!")
    print(f"Total outfits: {len(OUTFITS_REGISTRY)}")

    # Print summary by category
    for category in [OUTFIT_CATEGORY_WEAPONS, OUTFIT_CATEGORY_AMMUNITION,
                     OUTFIT_CATEGORY_DEFENSE, OUTFIT_CATEGORY_ENGINES,
                     OUTFIT_CATEGORY_ELECTRONICS, OUTFIT_CATEGORY_UTILITY]:
        outfits = get_outfits_by_category(category)
        print(f"{category.title()}: {len(outfits)} outfits")

# Initialize outfits from data files instead of hardcoded examples
print("=== LOADING OUTFITS ===")  # DEBUG
try:
    from .outfit_data_loader import load_outfits_from_data_files
    print("Successfully imported outfit_data_loader")  # DEBUG
    
    # Try loading data files
    result = load_outfits_from_data_files()
    print(f"Data loader returned: {result} outfits")  # DEBUG
    
    # If we loaded outfits successfully, we're done
    if result > 0:
        print(f"Successfully loaded {result} outfits from data files")
    else:
        # If no outfits were loaded, fall back to examples
        print("No outfits loaded from data files, creating examples as fallback")
        create_example_outfits()
        
except ImportError as e:
    print(f"Could not import outfit_data_loader: {e}")  # DEBUG
    # Fall back to creating example outfits if loader fails
    print("Creating example outfits as fallback")
    create_example_outfits()
except Exception as e:
    print(f"Error during outfit loading: {e}")  # DEBUG
    import traceback
    traceback.print_exc()  # DEBUG
    # Fall back to creating example outfits if loading fails
    print("Creating example outfits as fallback")
    create_example_outfits()

print(f"Final outfit registry size: {len(OUTFITS_REGISTRY)}")
print(f"Available outfits: {list(OUTFITS_REGISTRY.keys())[:10]}")  # Show first 10
print("=== OUTFIT LOADING COMPLETE ===")
