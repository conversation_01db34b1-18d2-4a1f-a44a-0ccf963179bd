"""
Quick test to see what outfits are being loaded
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("=== TESTING OUTFIT LOADING ===")

try:
    # This should trigger the outfit loading
    from game_objects.standardized_outfits import OUTFITS_REGISTRY
    from game_objects.example_outfits import *  # This triggers the loading
    
    print(f"OUTFITS_REGISTRY size: {len(OUTFITS_REGISTRY)}")
    print(f"Available outfits: {list(OUTFITS_REGISTRY.keys())}")
    
    # Test specific outfits
    test_outfits = ['laser_cannon', 'basic_shield', 'missile_rack', 'light_missile']
    for outfit_id in test_outfits:
        if outfit_id in OUTFITS_REGISTRY:
            outfit = OUTFITS_REGISTRY[outfit_id]
            print(f"  {outfit_id}: {outfit.name} ({outfit.category})")
        else:
            print(f"  {outfit_id}: NOT FOUND")
    
    print("\n=== TESTING COMPLETE ===")
    
except Exception as e:
    print(f"Error during testing: {e}")
    import traceback
    traceback.print_exc()
