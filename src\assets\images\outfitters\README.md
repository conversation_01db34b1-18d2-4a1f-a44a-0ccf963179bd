# Outfitter Images Directory

This directory contains images that will be displayed in the outfitter interface when players browse and purchase equipment.

## Directory Structure

- `weapons/` - Images for weapon outfits
- `ammunition/` - Images for ammunition outfits  
- `defense/` - Images for defense outfits (shields, armor)
- `electronics/` - Images for electronics outfits (sensors, targeting)
- `utility/` - Images for utility outfits (cargo pods, fuel tanks)

## Image Requirements

### Format
- **Supported formats**: PNG, JPG, JPEG, GIF, BMP
- **Recommended format**: PNG (for transparency support)

### Size
- **Recommended size**: 64x64 to 128x128 pixels
- **Maximum size**: 256x256 pixels (larger images will be scaled down)
- **Aspect ratio**: Square (1:1) preferred for consistent display

### Style Guidelines
- Use clear, recognizable icons that represent the outfit
- Maintain consistent art style across similar outfit types
- Consider using transparency for non-rectangular items
- Ensure images are visible against various background colors

## Usage

### Adding Images
1. Place your image files in the appropriate category subdirectory
2. Open the enhanced editor (`enhanced_editor_refactored.py`)
3. Navigate to the appropriate outfit category tab
4. Select an outfit to edit
5. In the "Outfitter Image" field, browse and select your image
6. Use the Preview button to verify the image displays correctly
7. Save the outfit

### In-Game Display
- Images will appear in the outfitter interface when browsing equipment
- Players will see these images when deciding what to purchase
- Images help players quickly identify different types of equipment

## File Naming
- Use descriptive names that match the outfit name when possible
- Examples:
  - `laser_cannon.png`
  - `missile_rack.png`
  - `shield_generator.png`
  - `cargo_pod.png`

## Performance Notes
- Keep file sizes reasonable (under 100KB per image)
- The game will cache images for better performance
- PNG format with transparency is preferred for best visual quality
