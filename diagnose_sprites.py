"""
Diagnostic script to check ship sprite loading issues
"""

import sys
import os
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("=== SHIP SPRITE LOADING DIAGNOSTIC ===")

try:
    # Import ship system
    from game_objects.ships import SHIPS
    from game_objects.sprite_manager import load_sprite
    
    print(f"Found {len(SHIPS)} ships in registry")
    
    # Check sprite directory structure
    sprites_dir = Path(__file__).parent / "assets" / "images" / "sprites" / "ships"
    print(f"\nChecking sprites directory: {sprites_dir}")
    print(f"Directory exists: {sprites_dir.exists()}")
    
    if sprites_dir.exists():
        size_dirs = [d for d in sprites_dir.iterdir() if d.is_dir()]
        print(f"Size directories found: {[d.name for d in size_dirs]}")
        
        for size_dir in size_dirs:
            sprite_files = list(size_dir.glob("*_spritesheet.png"))
            print(f"  {size_dir.name}: {len(sprite_files)} sprite files")
            for sprite_file in sprite_files[:5]:  # Show first 5
                print(f"    - {sprite_file.name}")
            if len(sprite_files) > 5:
                print(f"    ... and {len(sprite_files) - 5} more")
    
    print(f"\n=== TESTING SHIP SPRITE LOADING ===")
    
    # Test each ship
    success_count = 0
    failure_count = 0
    
    for ship_id, ship in SHIPS.items():
        print(f"\nTesting ship: {ship_id} (size: {ship.size})")
        
        try:
            spritesheet = load_sprite("ship", ship_id, ship.size)
            if spritesheet and spritesheet.image:
                print(f"  ✅ SUCCESS: Loaded sprite for {ship_id}")
                success_count += 1
            else:
                print(f"  ❌ FAILED: No sprite loaded for {ship_id}")
                failure_count += 1
                
                # Check if the expected file exists
                expected_path = sprites_dir / ship.size / f"{ship_id}_spritesheet.png"
                print(f"     Expected: {expected_path}")
                print(f"     Exists: {expected_path.exists()}")
                
        except Exception as e:
            print(f"  ❌ ERROR loading {ship_id}: {e}")
            failure_count += 1
    
    print(f"\n=== SUMMARY ===")
    print(f"Successful loads: {success_count}")
    print(f"Failed loads: {failure_count}")
    print(f"Success rate: {success_count / (success_count + failure_count) * 100:.1f}%")
    
    if failure_count > 0:
        print(f"\nSUGGESTIONS:")
        print(f"1. Check that sprite files exist in the correct size directories")
        print(f"2. Verify sprite file names match ship IDs exactly")
        print(f"3. Ensure sprite files are in PNG format")
        print(f"4. Check file permissions")

except Exception as e:
    print(f"Error during diagnostic: {e}")
    import traceback
    traceback.print_exc()

print("\n=== DIAGNOSTIC COMPLETE ===")
