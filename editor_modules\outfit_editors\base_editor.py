"""
Base Editor for Outfit Categories
Provides common functionality for all outfit editors
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import json

class BaseOutfitEditor:
    """Base class for all outfit editors."""
    
    def __init__(self, parent, data_manager, category_name):
        self.parent = parent
        self.data_manager = data_manager
        self.category_name = category_name
        self.current_outfit = None
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        
        # Setup UI
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the basic UI structure."""
        # Create horizontal layout
        paned = ttk.PanedWindow(self.frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Item list
        left_frame = ttk.LabelFrame(paned, text=f"{self.category_name.title()} Library")
        paned.add(left_frame, weight=1)
        
        # Item listbox
        self.listbox = tk.Listbox(left_frame)
        self.listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))
        self.listbox.bind("<<ListboxSelect>>", self.on_item_select)
        
        # Buttons for item management
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text=f"New {self.category_name.title()}", 
                  command=self.create_new_item).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Delete", 
                  command=self.delete_item).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Export", 
                  command=self.export_items).pack(side=tk.LEFT, padx=2)
        
        # Right panel - Editor
        right_frame = ttk.LabelFrame(paned, text=f"{self.category_name.title()} Editor")
        paned.add(right_frame, weight=2)
        
        # Setup specific editor UI (to be implemented by subclasses)
        self.setup_editor_ui(right_frame)
    
    def setup_editor_ui(self, parent):
        """Setup the specific editor UI. To be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement setup_editor_ui")
    
    def load_data(self):
        """Load items into the listbox."""
        if hasattr(self, 'listbox'):
            self.listbox.delete(0, tk.END)
            
            items = self.data_manager.get_outfits_by_category(self.category_name)
            for outfit_id, outfit in items.items():
                display_name = outfit.name
                self.listbox.insert(tk.END, display_name)
    
    def on_item_select(self, event=None):
        """Handle item selection from list."""
        selection = self.listbox.curselection()
        if not selection:
            return
        
        # Get selected item
        items = list(self.data_manager.get_outfits_by_category(self.category_name).values())
        
        if selection[0] < len(items):
            item = items[selection[0]]
            self.load_item_into_editor(item)
    
    def load_item_into_editor(self, item):
        """Load item data into the editor. To be implemented by subclasses."""
        self.current_outfit = item
        # Subclasses should implement specific loading logic
    
    def save_item(self):
        """Save the current item. To be implemented by subclasses."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", f"No {self.category_name} selected to save")
            return
        
        # Subclasses should implement specific saving logic
        messagebox.showinfo("Success", f"Saved {self.category_name}: {self.current_outfit.name}")
        self.load_data()  # Refresh the list
        self.data_manager.auto_save()  # Auto-save changes
    
    def create_new_item(self):
        """Create a new item."""
        item_id = simpledialog.askstring(f"New {self.category_name.title()}", 
                                        f"Enter a unique ID for the new {self.category_name}:")
        if not item_id:
            return
        
        if item_id in self.data_manager.outfits_registry:
            messagebox.showerror("Error", f"A {self.category_name} with ID '{item_id}' already exists")
            return
        
        try:
            # Create new item (to be implemented by subclasses)
            new_item = self.create_new_item_instance(item_id)
            
            self.data_manager.outfits_registry[item_id] = new_item
            self.load_data()
            messagebox.showinfo("Success", f"Created new {self.category_name}: {new_item.name}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create {self.category_name}: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new item instance. To be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement create_new_item_instance")
    
    def delete_item(self):
        """Delete the selected item."""
        selection = self.listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", f"No {self.category_name} selected to delete")
            return
        
        # Get selected item
        items = list(self.data_manager.get_outfits_by_category(self.category_name).items())
        
        if selection[0] < len(items):
            item_id, item = items[selection[0]]
            
            result = messagebox.askyesno("Confirm Delete",
                                       f"Are you sure you want to delete '{item.name}'?")
            if result:
                del self.data_manager.outfits_registry[item_id]
                self.load_data()
                messagebox.showinfo("Success", f"Deleted {self.category_name}: {item.name}")
    
    def export_items(self):
        """Export items to JSON file."""
        filename = filedialog.asksaveasfilename(
            title=f"Export {self.category_name.title()}",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                items_data = {}
                items = self.data_manager.get_outfits_by_category(self.category_name)
                
                for outfit_id, outfit in items.items():
                    items_data[outfit_id] = self.data_manager._outfit_to_dict(outfit)
                
                with open(filename, 'w') as f:
                    json.dump(items_data, f, indent=2)
                
                messagebox.showinfo("Success", f"Exported {len(items_data)} {self.category_name} to {filename}")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export {self.category_name}: {e}")
    
    def browse_image(self, var):
        """Browse for an image file and set the variable."""
        filename = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]
        )
        if filename:
            try:
                import os
                relative_path = os.path.relpath(filename, os.getcwd())
                var.set(relative_path)
            except ValueError:
                var.set(filename)
