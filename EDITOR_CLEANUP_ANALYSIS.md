# Editor Cleanup Analysis

## Critical Issues Found:

### 1. **Massive Code Duplication** 
- Each editor repeats the same UI patterns:
  - Basic properties grid (name, cost, space, tech level)
  - Image browse functionality  
  - Load/save logic with hasattr() checks
  - create_new_item_instance() with identical fallback patterns

### 2. **Inconsistent Parameter Handling**
- Every editor manually maps variables to attributes
- No validation or error handling standardization
- Missing support for new JSON schema parameters

### 3. **Data Manager Complexity**
- Multiple loading systems (_update_outfit_from_data, standardized vs hardcoded)
- Complex fallback logic in data_manager.py

## Cleanup Priority Order:

### **IMMEDIATE (Critical)**
1. Create StandardParameterGrid class for common UI patterns
2. Create ParameterValidator for input validation  
3. Consolidate load/save logic in base editor

### **NEXT (Important)**  
4. Add support for new JSON parameters (power_cost, projectile, etc.)
5. Simplify data manager loading logic
6. Add missing error handling

### **LATER (Polish)**
7. Add parameter tooltips and help text
8. Create outfit templates/presets
9. Add real-time validation feedback