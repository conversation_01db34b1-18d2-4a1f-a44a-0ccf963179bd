"""
Outfitter module for Escape Velocity Py.
This module handles the outfitter interface for buying and selling outfits.
"""

import pygame as pg
import math
from game_objects.outfits import get_outfit_by_id, get_available_outfits_for_purchase, CATEGORY_WEAPON, CATEGORY_ENGINE, CATEGORY_SHIELD, CATEGORY_ARMOR, CATEGORY_UTILITY, CATEGORY_SPECIAL, CATEGORY_AMMO

# --- Constants ---
PANEL_COLOR = (30, 30, 50)
TITLE_COLOR = (220, 220, 255)
TEXT_COLOR = (200, 200, 200)
HIGHLIGHT_COLOR = (100, 100, 160)
SELECTED_COLOR = (80, 80, 140)
BUTTON_COLOR = (60, 60, 100)
BUTTON_HOVER_COLOR = (80, 80, 120)
BUTTON_TEXT_COLOR = (220, 220, 220)
BUTTON_DISABLED_COLOR = (40, 40, 60)
BUTTON_DISABLED_TEXT_COLOR = (120, 120, 120)

# Stat colors
STAT_POSITIVE_COLOR = (100, 255, 100)
STAT_NEGATIVE_COLOR = (255, 100, 100)
STAT_NEUTRAL_COLOR = (200, 200, 200)

# Category colors
CATEGORY_COLORS = {
    CATEGORY_WEAPON: (255, 100, 100),
    CATEGORY_ENGINE: (100, 255, 100),
    CATEGORY_SHIELD: (100, 100, 255),
    CATEGORY_ARMOR: (200, 200, 200),
    CATEGORY_UTILITY: (255, 255, 100),
    CATEGORY_SPECIAL: (255, 100, 255),
    CATEGORY_AMMO: (255, 180, 100)
}

class Outfitter:
    """Outfitter interface for buying and selling outfits."""
    
    def __init__(self, game):
        """Initialize the outfitter."""
        self.game = game
        self.available_outfits = []
        self.selected_outfit = None
        self.selected_outfit_index = -1
        self.scroll_offset = 0
        self.max_items_per_page = 10
        self.current_tab = "buy"  # "buy" or "sell"
        
        # UI elements
        self.buy_button_rect = None
        self.sell_button_rect = None
        self.back_button_rect = None
        self.next_page_rect = None
        self.prev_page_rect = None
        self.outfit_rects = []
        self.tab_buy_rect = None
        self.tab_sell_rect = None
        
    def update_available_outfits(self):
        """Update the list of available outfits based on the planet's tech level."""
        if not self.game.docked_planet:
            return
            
        tech_level = self.game.docked_planet.tech_level
        ship_size = self.game.player.ship.size
        
        self.available_outfits = get_available_outfits_for_purchase(tech_level, ship_size)
        
    def show_outfitter_screen(self, screen):
        """Show the outfitter screen."""
        self.update_available_outfits()
        
        waiting_at_outfitter = True
        while waiting_at_outfitter and self.game.running:
            self.game.clock.tick(self.game.FPS)
            
            # Handle events
            for event in pg.event.get():
                if event.type == pg.QUIT:
                    waiting_at_outfitter = False
                    self.game.running = False
                    self.game.state = "QUIT"
                    
                if event.type == pg.KEYDOWN:
                    if event.key == pg.K_ESCAPE:
                        waiting_at_outfitter = False
                        self.game.state = "DOCKED"
                        return "DOCKED"
                        
                if event.type == pg.MOUSEBUTTONDOWN and event.button == 1:
                    mouse_pos = pg.mouse.get_pos()
                    
                    # Check tab buttons
                    if self.tab_buy_rect and self.tab_buy_rect.collidepoint(mouse_pos):
                        self.current_tab = "buy"
                        self.selected_outfit = None
                        self.selected_outfit_index = -1
                        
                    if self.tab_sell_rect and self.tab_sell_rect.collidepoint(mouse_pos):
                        self.current_tab = "sell"
                        self.selected_outfit = None
                        self.selected_outfit_index = -1
                        
                    # Check outfit selection
                    for i, rect in enumerate(self.outfit_rects):
                        if rect.collidepoint(mouse_pos):
                            if self.current_tab == "buy":
                                if i + self.scroll_offset < len(self.available_outfits):
                                    self.selected_outfit = self.available_outfits[i + self.scroll_offset]
                                    self.selected_outfit_index = i + self.scroll_offset
                            else:  # sell tab
                                outfit_items = list(self.game.player.outfits.items())
                                if i + self.scroll_offset < len(outfit_items):
                                    outfit_id, count = outfit_items[i + self.scroll_offset]
                                    self.selected_outfit = get_outfit_by_id(outfit_id)
                                    self.selected_outfit_index = i + self.scroll_offset
                            break
                            
                    # Check buy/sell button
                    if self.buy_button_rect and self.buy_button_rect.collidepoint(mouse_pos) and self.current_tab == "buy":
                        if self.selected_outfit:
                            self.buy_outfit(self.selected_outfit)
                            
                    if self.sell_button_rect and self.sell_button_rect.collidepoint(mouse_pos) and self.current_tab == "sell":
                        if self.selected_outfit:
                            self.sell_outfit(self.selected_outfit)
                            
                    # Check back button
                    if self.back_button_rect and self.back_button_rect.collidepoint(mouse_pos):
                        waiting_at_outfitter = False
                        self.game.state = "DOCKED"
                        return "DOCKED"
                        
                    # Check pagination buttons
                    if self.next_page_rect and self.next_page_rect.collidepoint(mouse_pos):
                        if self.current_tab == "buy":
                            if self.scroll_offset + self.max_items_per_page < len(self.available_outfits):
                                self.scroll_offset += self.max_items_per_page
                                self.selected_outfit = None
                                self.selected_outfit_index = -1
                        else:  # sell tab
                            if self.scroll_offset + self.max_items_per_page < len(self.game.player.outfits):
                                self.scroll_offset += self.max_items_per_page
                                self.selected_outfit = None
                                self.selected_outfit_index = -1
                                
                    if self.prev_page_rect and self.prev_page_rect.collidepoint(mouse_pos):
                        if self.scroll_offset > 0:
                            self.scroll_offset -= self.max_items_per_page
                            self.selected_outfit = None
                            self.selected_outfit_index = -1
                            
                if event.type == pg.MOUSEWHEEL:
                    # Scroll through outfits
                    if event.y > 0:  # Scroll up
                        if self.scroll_offset > 0:
                            self.scroll_offset -= 1
                    elif event.y < 0:  # Scroll down
                        if self.current_tab == "buy":
                            if self.scroll_offset + self.max_items_per_page < len(self.available_outfits):
                                self.scroll_offset += 1
                        else:  # sell tab
                            if self.scroll_offset + self.max_items_per_page < len(self.game.player.outfits):
                                self.scroll_offset += 1
                                
            # Draw the screen
            self.draw_outfitter_screen(screen)
            pg.display.flip()
            
        return "DOCKED"
        
    def draw_outfitter_screen(self, screen):
        """Draw the outfitter screen."""
        # Clear the screen
        screen.fill((0, 0, 0))
        
        width, height = screen.get_width(), screen.get_height()
        
        # Draw title
        self.game.draw_text(f"Outfitter - {self.game.docked_planet.name}", 36, TITLE_COLOR, width // 2, 30, align="center")
        
        # Draw credits
        self.game.draw_text(f"Credits: {self.game.player.credits}", 24, STAT_NEUTRAL_COLOR, width - 20, 30, align="right")
        
        # Draw tabs
        tab_width = 150
        tab_height = 40
        tab_y = 70
        
        self.tab_buy_rect = pg.Rect(width // 2 - tab_width - 10, tab_y, tab_width, tab_height)
        self.tab_sell_rect = pg.Rect(width // 2 + 10, tab_y, tab_width, tab_height)
        
        # Draw buy tab
        tab_color = SELECTED_COLOR if self.current_tab == "buy" else BUTTON_COLOR
        pg.draw.rect(screen, tab_color, self.tab_buy_rect)
        pg.draw.rect(screen, TEXT_COLOR, self.tab_buy_rect, 2)
        self.game.draw_text("Buy Outfits", 24, BUTTON_TEXT_COLOR, self.tab_buy_rect.centerx, self.tab_buy_rect.centery, align="center")
        
        # Draw sell tab
        tab_color = SELECTED_COLOR if self.current_tab == "sell" else BUTTON_COLOR
        pg.draw.rect(screen, tab_color, self.tab_sell_rect)
        pg.draw.rect(screen, TEXT_COLOR, self.tab_sell_rect, 2)
        self.game.draw_text("Sell Outfits", 24, BUTTON_TEXT_COLOR, self.tab_sell_rect.centerx, self.tab_sell_rect.centery, align="center")
        
        # Draw main panel
        panel_rect = pg.Rect(50, 120, width - 100, height - 220)
        pg.draw.rect(screen, PANEL_COLOR, panel_rect)
        pg.draw.rect(screen, TEXT_COLOR, panel_rect, 2)
        
        # Draw outfit list
        self.draw_outfit_list(screen, panel_rect)
        
        # Draw outfit details
        self.draw_outfit_details(screen, panel_rect)
        
        # Draw back button
        back_button_width = 150
        back_button_height = 40
        self.back_button_rect = pg.Rect(width // 2 - back_button_width // 2, height - 60, back_button_width, back_button_height)
        pg.draw.rect(screen, BUTTON_COLOR, self.back_button_rect)
        pg.draw.rect(screen, TEXT_COLOR, self.back_button_rect, 2)
        self.game.draw_text("Back", 24, BUTTON_TEXT_COLOR, self.back_button_rect.centerx, self.back_button_rect.centery, align="center")
        
    def draw_outfit_list(self, screen, panel_rect):
        """Draw the list of outfits."""
        list_width = panel_rect.width // 2 - 20
        list_rect = pg.Rect(panel_rect.left + 10, panel_rect.top + 10, list_width, panel_rect.height - 20)
        pg.draw.rect(screen, (40, 40, 60), list_rect)
        pg.draw.rect(screen, TEXT_COLOR, list_rect, 1)
        
        # Draw title
        title = "Available Outfits" if self.current_tab == "buy" else "Installed Outfits"
        self.game.draw_text(title, 24, TITLE_COLOR, list_rect.centerx, list_rect.top + 20, align="center")
        
        # Draw outfits
        item_height = 50
        self.outfit_rects = []
        
        if self.current_tab == "buy":
            print(f"[Outfitter DEBUG] Buy Tab: Total available_outfits: {len(self.available_outfits)}, scroll_offset: {self.scroll_offset}")
            outfits_to_display = self.available_outfits[self.scroll_offset:self.scroll_offset + self.max_items_per_page]
            print(f"[Outfitter DEBUG] Buy Tab: outfits_to_display count: {len(outfits_to_display)}")
            
            for i, outfit in enumerate(outfits_to_display):
                print(f"[Outfitter DEBUG] Buy Tab: Displaying outfit: {outfit.name}")
                item_y = list_rect.top + 60 + i * item_height
                item_rect = pg.Rect(list_rect.left + 5, item_y, list_width - 10, item_height - 5)
                
                # Highlight selected outfit
                if i + self.scroll_offset == self.selected_outfit_index:
                    pg.draw.rect(screen, SELECTED_COLOR, item_rect)
                else:
                    pg.draw.rect(screen, (50, 50, 70), item_rect)
                    
                pg.draw.rect(screen, TEXT_COLOR, item_rect, 1)
                self.outfit_rects.append(item_rect)
                
                # Draw outfit name and category
                category_color = CATEGORY_COLORS.get(outfit.category, TEXT_COLOR)
                self.game.draw_text(outfit.name, 20, TEXT_COLOR, item_rect.left + 10, item_rect.top + 15, align="left")
                self.game.draw_text(f"{outfit.category.capitalize()} - {outfit.cost} cr", 16, category_color, item_rect.left + 10, item_rect.top + 35, align="left")
                
        else:  # sell tab
            outfit_items = list(self.game.player.outfits.items())
            print(f"[Outfitter DEBUG] Sell Tab: Total player outfits: {len(outfit_items)}, scroll_offset: {self.scroll_offset}")
            outfits_to_display = outfit_items[self.scroll_offset:self.scroll_offset + self.max_items_per_page]
            print(f"[Outfitter DEBUG] Sell Tab: outfits_to_display count: {len(outfits_to_display)}")
            
            for i, (outfit_id, count) in enumerate(outfits_to_display):
                outfit = get_outfit_by_id(outfit_id)
                if not outfit:
                    print(f"[Outfitter DEBUG] Sell Tab: Outfit ID {outfit_id} not found in OUTFITS.")
                    continue
                print(f"[Outfitter DEBUG] Sell Tab: Displaying outfit: {outfit.name} x{count}")
                item_y = list_rect.top + 60 + i * item_height
                item_rect = pg.Rect(list_rect.left + 5, item_y, list_width - 10, item_height - 5)
                
                # Highlight selected outfit
                if i + self.scroll_offset == self.selected_outfit_index:
                    pg.draw.rect(screen, SELECTED_COLOR, item_rect)
                else:
                    pg.draw.rect(screen, (50, 50, 70), item_rect)
                    
                pg.draw.rect(screen, TEXT_COLOR, item_rect, 1)
                self.outfit_rects.append(item_rect)
                
                # Draw outfit name and category
                category_color = CATEGORY_COLORS.get(outfit.category, TEXT_COLOR)
                self.game.draw_text(f"{outfit.name} x{count}", 20, TEXT_COLOR, item_rect.left + 10, item_rect.top + 15, align="left")
                self.game.draw_text(f"{outfit.category.capitalize()} - {outfit.cost // 2} cr", 16, category_color, item_rect.left + 10, item_rect.top + 35, align="left")
                
        # Draw pagination controls
        if (self.current_tab == "buy" and len(self.available_outfits) > self.max_items_per_page) or \
           (self.current_tab == "sell" and len(self.game.player.outfits) > self.max_items_per_page):
            # Next page button
            self.next_page_rect = pg.Rect(list_rect.right - 80, list_rect.bottom - 30, 70, 25)
            pg.draw.rect(screen, BUTTON_COLOR, self.next_page_rect)
            pg.draw.rect(screen, TEXT_COLOR, self.next_page_rect, 1)
            self.game.draw_text("Next >", 16, BUTTON_TEXT_COLOR, self.next_page_rect.centerx, self.next_page_rect.centery, align="center")
            
            # Previous page button
            self.prev_page_rect = pg.Rect(list_rect.left + 10, list_rect.bottom - 30, 70, 25)
            pg.draw.rect(screen, BUTTON_COLOR, self.prev_page_rect)
            pg.draw.rect(screen, TEXT_COLOR, self.prev_page_rect, 1)
            self.game.draw_text("< Prev", 16, BUTTON_TEXT_COLOR, self.prev_page_rect.centerx, self.prev_page_rect.centery, align="center")
            
    def draw_outfit_details(self, screen, panel_rect):
        """Draw the details of the selected outfit."""
        details_width = panel_rect.width // 2 - 20
        details_rect = pg.Rect(panel_rect.left + panel_rect.width // 2 + 10, panel_rect.top + 10, details_width, panel_rect.height - 20)
        pg.draw.rect(screen, (40, 40, 60), details_rect)
        pg.draw.rect(screen, TEXT_COLOR, details_rect, 1)
        
        # Draw title
        self.game.draw_text("Outfit Details", 24, TITLE_COLOR, details_rect.centerx, details_rect.top + 20, align="center")
        
        if self.selected_outfit:
            outfit = self.selected_outfit
            
            # Draw outfit name
            self.game.draw_text(outfit.name, 28, TEXT_COLOR, details_rect.centerx, details_rect.top + 60, align="center")
            
            # Draw outfit category
            category_color = CATEGORY_COLORS.get(outfit.category, TEXT_COLOR)
            self.game.draw_text(f"Category: {outfit.category.capitalize()}", 20, category_color, details_rect.left + 20, details_rect.top + 100, align="left")
            
            # Draw outfit cost
            cost_text = f"Cost: {outfit.cost} cr" if self.current_tab == "buy" else f"Sell Value: {outfit.cost // 2} cr"
            self.game.draw_text(cost_text, 20, TEXT_COLOR, details_rect.left + 20, details_rect.top + 130, align="left")
            
            # Draw outfit space
            self.game.draw_text(f"Space Required: {outfit.space_required} tons", 20, TEXT_COLOR, details_rect.left + 20, details_rect.top + 160, align="left")
            
            # Draw outfit description
            description_rect = pg.Rect(details_rect.left + 20, details_rect.top + 190, details_width - 40, 100)
            self.game.draw_text_wrapped(outfit.description, 18, TEXT_COLOR, description_rect)
            
            # Draw outfit stats based on category
            stats_y = description_rect.bottom + 20
            
            if outfit.category == CATEGORY_WEAPON:
                self.game.draw_text(f"Damage: {outfit.damage}", 18, TEXT_COLOR, details_rect.left + 20, stats_y, align="left")
                self.game.draw_text(f"Fire Rate: {outfit.fire_rate}/sec", 18, TEXT_COLOR, details_rect.left + 20, stats_y + 25, align="left")
                self.game.draw_text(f"Range: {outfit.range}", 18, TEXT_COLOR, details_rect.left + 20, stats_y + 50, align="left")
                self.game.draw_text(f"Energy: {outfit.energy_usage}", 18, TEXT_COLOR, details_rect.left + 20, stats_y + 75, align="left")
                
                if hasattr(outfit, 'ammo_type'):
                    self.game.draw_text(f"Ammo Type: {outfit.ammo_type}", 18, TEXT_COLOR, details_rect.left + 20, stats_y + 100, align="left")
                    self.game.draw_text(f"Max Ammo: {outfit.max_ammo}", 18, TEXT_COLOR, details_rect.left + 20, stats_y + 125, align="left")
            
            # Draw buy/sell button
            button_width = 150
            button_height = 40
            button_y = details_rect.bottom - 60
            
            if self.current_tab == "buy":
                self.buy_button_rect = pg.Rect(details_rect.centerx - button_width // 2, button_y, button_width, button_height)
                
                # Check if player can afford and has space
                can_afford = self.game.player.credits >= outfit.cost
                has_space = self.game.player.used_outfit_space + outfit.space_required <= self.game.player.outfit_space
                
                button_color = BUTTON_COLOR if can_afford and has_space else BUTTON_DISABLED_COLOR
                text_color = BUTTON_TEXT_COLOR if can_afford and has_space else BUTTON_DISABLED_TEXT_COLOR
                
                pg.draw.rect(screen, button_color, self.buy_button_rect)
                pg.draw.rect(screen, TEXT_COLOR, self.buy_button_rect, 2)
                self.game.draw_text("Buy", 24, text_color, self.buy_button_rect.centerx, self.buy_button_rect.centery, align="center")
                
                # Draw error message if needed
                if not can_afford:
                    self.game.draw_text("Not enough credits!", 16, STAT_NEGATIVE_COLOR, details_rect.centerx, button_y + 50, align="center")
                elif not has_space:
                    self.game.draw_text("Not enough outfit space!", 16, STAT_NEGATIVE_COLOR, details_rect.centerx, button_y + 50, align="center")
                
            else:  # sell tab
                self.sell_button_rect = pg.Rect(details_rect.centerx - button_width // 2, button_y, button_width, button_height)
                pg.draw.rect(screen, BUTTON_COLOR, self.sell_button_rect)
                pg.draw.rect(screen, TEXT_COLOR, self.sell_button_rect, 2)
                self.game.draw_text("Sell", 24, BUTTON_TEXT_COLOR, self.sell_button_rect.centerx, self.sell_button_rect.centery, align="center")
        
    def buy_outfit(self, outfit):
        """Buy an outfit."""
        # Check if player can afford it
        if self.game.player.credits < outfit.cost:
            self.game.set_status_message("Not enough credits!", (255, 100, 100))
            return False
            
        # Check if player has enough space
        if self.game.player.used_outfit_space + outfit.space_required > self.game.player.outfit_space:
            self.game.set_status_message("Not enough outfit space!", (255, 100, 100))
            return False
            
        # Add outfit to player
        if self.game.player.try_add_outfit(outfit.name.lower().replace(" ", "_")):
            # Deduct credits
            self.game.player.credits -= outfit.cost
            self.game.set_status_message(f"Purchased {outfit.name}!", (100, 255, 100))
            return True
        else:
            self.game.set_status_message(f"Failed to add {outfit.name}!", (255, 100, 100))
            return False
            
    def sell_outfit(self, outfit):
        """Sell an outfit."""
        outfit_id = outfit.name.lower().replace(" ", "_")
        
        # Check if player has the outfit
        if outfit_id not in self.game.player.outfits or self.game.player.outfits[outfit_id] <= 0:
            self.game.set_status_message(f"You don't have {outfit.name}!", (255, 100, 100))
            return False
            
        # Remove outfit from player
        if self.game.player.remove_outfit(outfit_id):
            # Add credits (half the purchase price)
            self.game.player.credits += outfit.cost // 2
            self.game.set_status_message(f"Sold {outfit.name}!", (100, 255, 100))
            
            # Update selected outfit if it was the last one
            if outfit_id not in self.game.player.outfits:
                self.selected_outfit = None
                self.selected_outfit_index = -1
                
            return True
        else:
            self.game.set_status_message(f"Failed to sell {outfit.name}!", (255, 100, 100))
            return False
