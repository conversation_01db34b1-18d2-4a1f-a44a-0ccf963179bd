"""
Utility Editor for the Enhanced Content Editor
Handles editing of utility outfits
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .base_editor import BaseOutfitEditor
from .ui_components import StandardParameterGrid, ParameterLoader

class UtilityEditor(BaseOutfitEditor):
    """Editor for utility outfits."""
    
    def __init__(self, parent, data_manager):
        super().__init__(parent, data_manager, "utility")
    
    def setup_editor_ui(self, parent):
        """Setup the utility editor interface."""
        # Create scrollable frame
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Basic Properties Grid
        self.basic_grid = StandardParameterGrid(scrollable_frame, "Basic Properties")
        self.basic_grid.add_string_field("Name", "name")
        self.basic_grid.add_int_field("Cost", "cost", 100, 100000)
        self.basic_grid.add_int_field("Space Required", "space_required", 1, 50)
        self.basic_grid.add_combo_field("Utility Type", "subcategory", ["cargo", "fuel", "power", "crew", "life_support", "processing", "storage"])
        self.basic_grid.add_int_field("Min Tech Level", "min_tech_level", 1, 10)
        self.basic_grid.add_file_field("Outfitter Icon", "outfitter_icon")
        self.basic_grid.add_file_field("Outfitter Image", "outfitter_image")
        self.basic_grid.pack(fill=tk.X, padx=5, pady=5)

        # Utility Type Selection
        type_frame = ttk.LabelFrame(scrollable_frame, text="Utility System Type")
        type_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.utility_type_var = tk.StringVar(value="cargo")
        ttk.Radiobutton(type_frame, text="Cargo & Storage", 
                       variable=self.utility_type_var, value="cargo",
                       command=self.on_utility_type_change).pack(anchor=tk.W, padx=5, pady=2)
        ttk.Radiobutton(type_frame, text="Power & Energy", 
                       variable=self.utility_type_var, value="power",
                       command=self.on_utility_type_change).pack(anchor=tk.W, padx=5, pady=2)
        ttk.Radiobutton(type_frame, text="Life Support & Crew", 
                       variable=self.utility_type_var, value="life_support",
                       command=self.on_utility_type_change).pack(anchor=tk.W, padx=5, pady=2)
        ttk.Radiobutton(type_frame, text="Processing & Systems", 
                       variable=self.utility_type_var, value="processing",
                       command=self.on_utility_type_change).pack(anchor=tk.W, padx=5, pady=2)

        # Cargo & Storage Properties Grid
        self.cargo_grid = StandardParameterGrid(scrollable_frame, "Cargo & Storage Properties")
        self.cargo_grid.add_int_field("Cargo Space Boost", "cargo_space_boost", 0, 1000)
        self.cargo_grid.add_int_field("Fuel Capacity Boost", "fuel_capacity_boost", 0, 2000)
        self.cargo_grid.add_int_field("Ammunition Storage", "ammo_storage_boost", 0, 500)
        self.cargo_grid.add_float_field("Cargo Efficiency", "cargo_efficiency", 1.0, 3.0, 0.1)
        self.cargo_grid.add_combo_field("Storage Type", "storage_type", ["general", "liquid", "gas", "hazardous", "specialized"])

        # Power & Energy Properties Grid
        self.power_grid = StandardParameterGrid(scrollable_frame, "Power & Energy Properties")
        self.power_grid.add_float_field("Energy Generation", "energy_generation", 0.0, 100.0, 0.1)
        self.power_grid.add_int_field("Battery Capacity", "battery_capacity", 0, 1000)
        self.power_grid.add_float_field("Power Efficiency", "power_efficiency", 0.5, 2.0, 0.01)
        self.power_grid.add_float_field("Solar Collection", "solar_collection", 0.0, 50.0, 0.1)
        self.power_grid.add_float_field("Fusion Rate", "fusion_rate", 0.0, 20.0, 0.1)
        
        # Life Support & Crew Properties Grid
        self.life_support_grid = StandardParameterGrid(scrollable_frame, "Life Support & Crew Properties")
        self.life_support_grid.add_int_field("Crew Capacity Boost", "crew_capacity_boost", 0, 200)
        self.life_support_grid.add_float_field("Life Support Efficiency", "life_support_efficiency", 0.5, 3.0, 0.1)
        self.life_support_grid.add_float_field("Oxygen Generation", "oxygen_generation", 0.0, 10.0, 0.1)
        self.life_support_grid.add_float_field("Water Recycling", "water_recycling", 0.0, 1.0, 0.01)
        self.life_support_grid.add_int_field("Emergency Reserves", "emergency_reserves", 0, 100)
        
        # Processing & Systems Properties Grid
        self.processing_grid = StandardParameterGrid(scrollable_frame, "Processing & Systems Properties")
        self.processing_grid.add_float_field("Processing Power", "processing_power", 1.0, 20.0, 0.1)
        self.processing_grid.add_int_field("Data Storage", "data_storage", 100, 10000)
        self.processing_grid.add_float_field("Research Efficiency", "research_efficiency", 1.0, 5.0, 0.1)
        self.processing_grid.add_float_field("Manufacturing Speed", "manufacturing_speed", 1.0, 10.0, 0.1)
        self.processing_grid.add_combo_field("AI Level", "ai_level", ["none", "basic", "advanced", "quantum"])
        
        # Operational Properties Grid (Common to all types)
        self.operational_grid = StandardParameterGrid(scrollable_frame, "Operational Properties")
        self.operational_grid.add_float_field("Maintenance Requirement", "maintenance_requirement", 0.1, 2.0, 0.1)
        self.operational_grid.add_float_field("Reliability", "reliability", 0.5, 1.0, 0.01)
        self.operational_grid.add_float_field("Automation Level", "automation_level", 0.0, 1.0, 0.01)
        self.operational_grid.add_float_field("Upgrade Capacity", "upgrade_capacity", 0.0, 3.0, 0.1)
        self.operational_grid.pack(fill=tk.X, padx=5, pady=5)
        
        # Description
        desc_frame = ttk.LabelFrame(scrollable_frame, text="Description")
        desc_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.description_text = tk.Text(desc_frame, height=3, wrap=tk.WORD)
        self.description_text.pack(fill=tk.X, padx=5, pady=5)

        # Save button
        save_frame = ttk.Frame(scrollable_frame)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        ttk.Button(save_frame, text="Save Utility", command=self.save_item).pack(side=tk.RIGHT, padx=5)
        
        # Initialize UI state
        self.on_utility_type_change()
    
    def on_utility_type_change(self):
        """Show/hide UI sections based on utility type."""
        utility_type = self.utility_type_var.get()
        
        # Hide all specialized grids first
        if hasattr(self, 'cargo_grid'):
            self.cargo_grid.frame.pack_forget()
        if hasattr(self, 'power_grid'):
            self.power_grid.frame.pack_forget()
        if hasattr(self, 'life_support_grid'):
            self.life_support_grid.frame.pack_forget()
        if hasattr(self, 'processing_grid'):
            self.processing_grid.frame.pack_forget()
            
        # Show relevant grids based on type
        if utility_type == "cargo":
            if hasattr(self, 'cargo_grid'):
                self.cargo_grid.pack(fill=tk.X, padx=5, pady=5)
        elif utility_type == "power":
            if hasattr(self, 'power_grid'):
                self.power_grid.pack(fill=tk.X, padx=5, pady=5)
        elif utility_type == "life_support":
            if hasattr(self, 'life_support_grid'):
                self.life_support_grid.pack(fill=tk.X, padx=5, pady=5)
        elif utility_type == "processing":
            if hasattr(self, 'processing_grid'):
                self.processing_grid.pack(fill=tk.X, padx=5, pady=5)
    
    def load_item_into_editor(self, utility):
        """Load utility data into the editor."""
        super().load_item_into_editor(utility)
        
        # Load using standardized parameter loader
        ParameterLoader.load_outfit_parameters(utility, self.basic_grid)
        ParameterLoader.load_outfit_parameters(utility, self.cargo_grid)
        ParameterLoader.load_outfit_parameters(utility, self.power_grid)
        ParameterLoader.load_outfit_parameters(utility, self.life_support_grid)
        ParameterLoader.load_outfit_parameters(utility, self.processing_grid)
        ParameterLoader.load_outfit_parameters(utility, self.operational_grid)
        
        # Set utility type based on subcategory and properties
        subcategory = getattr(utility, 'subcategory', 'cargo')
        if subcategory in ['cargo', 'fuel', 'storage']:
            self.utility_type_var.set('cargo')
        elif subcategory in ['power']:
            self.utility_type_var.set('power')
        elif subcategory in ['crew', 'life_support']:
            self.utility_type_var.set('life_support')
        elif subcategory in ['processing']:
            self.utility_type_var.set('processing')
        else:
            self.utility_type_var.set('cargo')  # Default
            
        # Load description
        self.description_text.delete(1.0, tk.END)
        self.description_text.insert(1.0, getattr(utility, 'description', ''))
        
        # Update UI visibility
        self.on_utility_type_change()
    
    def save_item(self):
        """Save the current utility with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No utility selected to save")
            return
        
        try:
            # Save using standardized parameter loader
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.basic_grid)
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.operational_grid)
            
            # Save type-specific parameters based on utility type
            utility_type = self.utility_type_var.get()
            if utility_type == "cargo":
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.cargo_grid)
            elif utility_type == "power":
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.power_grid)
            elif utility_type == "life_support":
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.life_support_grid)
            elif utility_type == "processing":
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.processing_grid)
            
            # Save description
            self.current_outfit.description = self.description_text.get(1.0, tk.END).strip()
            
            super().save_item()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save utility: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new utility instance."""
        class SimpleUtility:
            def __init__(self, id, name):
                # Basic properties
                self.id = id
                self.name = name
                self.category = "utility"
                self.subcategory = "cargo"
                self.cost = 3000
                self.space_required = 4
                self.min_tech_level = 1
                self.outfitter_icon = ""
                self.outfitter_image = ""
                
                # Cargo & Storage Properties
                self.cargo_space_boost = 50
                self.fuel_capacity_boost = 0
                self.ammo_storage_boost = 0
                self.cargo_efficiency = 1.0
                self.storage_type = "general"
                
                # Power & Energy Properties
                self.energy_generation = 0.0
                self.battery_capacity = 0
                self.power_efficiency = 1.0
                self.solar_collection = 0.0
                self.fusion_rate = 0.0
                
                # Life Support & Crew Properties
                self.crew_capacity_boost = 0
                self.life_support_efficiency = 1.0
                self.oxygen_generation = 0.0
                self.water_recycling = 0.0
                self.emergency_reserves = 0
                
                # Processing & Systems Properties
                self.processing_power = 1.0
                self.data_storage = 1000
                self.research_efficiency = 1.0
                self.manufacturing_speed = 1.0
                self.ai_level = "none"
                
                # Operational Properties
                self.maintenance_requirement = 1.0
                self.reliability = 0.9
                self.automation_level = 0.5
                self.upgrade_capacity = 1.0
                
                self.description = ""

        return SimpleUtility(item_id, item_id.replace('_', ' ').title())
