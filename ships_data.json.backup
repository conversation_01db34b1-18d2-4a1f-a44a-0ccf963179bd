{"scout": {"id": "scout", "name": "Scout", "ship_class": "fighter", "size": "small", "cost": 15000, "outfit_space": 20, "cargo_space": 5, "mass": 1.0, "min_tech_level": 1, "max_speed": 4.0, "acceleration": 0.6, "turn_rate": 1.75, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "description": "A fast, agile scout ship with minimal cargo space.", "game_sprite": "scout_spritesheet.png", "shipyard_sprite": "scout_spritesheet.png", "animation_frames": 32, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30}, "light_fighter": {"id": "light_fighter", "name": "Light Fighter", "ship_class": "fighter", "size": "small", "cost": 18000, "outfit_space": 25, "cargo_space": 3, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.75, "acceleration": 0.5, "turn_rate": 1.5, "shields": 60, "armor": 40, "shield_recharge_rate": 1.0, "description": "A nimble fighter designed for dogfighting.", "game_sprite": "light_fighter_spritesheet.png", "shipyard_sprite": "light_fighter_spritesheet.png", "animation_frames": 32, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30}, "freighter": {"id": "freighter", "name": "Freighter", "ship_class": "freighter", "size": "medium", "cost": 45000, "outfit_space": 35, "cargo_space": 120, "mass": 1.0, "min_tech_level": 2, "max_speed": 2.25, "acceleration": 0.25, "turn_rate": 0.5, "shields": 80, "armor": 100, "shield_recharge_rate": 1.0, "description": "A standard cargo ship with good capacity.", "game_sprite": "freighter_spritesheet.png", "shipyard_sprite": "freighter_spritesheet.png", "animation_frames": 32, "sprite_size": 48, "animation_type": "rotation", "frame_rate": 30}, "kraken": {"id": "kraken", "name": "<PERSON><PERSON><PERSON>", "ship_class": "fighter", "size": "small", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "description": "", "game_sprite": "", "shipyard_sprite": "", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30}, "corvette": {"id": "corvette", "name": "Corvette", "ship_class": "corvette", "size": "small", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "description": "", "game_sprite": "corvette_spritesheet.png", "shipyard_sprite": "corvette_spritesheet.png", "animation_frames": 32, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30}, "heavyfighter": {"id": "heavyfighter", "name": "Heavyfighter", "ship_class": "fighter", "size": "medium", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "description": "", "game_sprite": "heavy_fighter_spritesheet.png", "shipyard_sprite": "heavy_fighter_spritesheet.png", "animation_frames": 32, "sprite_size": 48, "animation_type": "rotation", "frame_rate": 30}, "gunship": {"id": "gunship", "name": "Gunship", "ship_class": "corvette", "size": "medium", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "description": "", "game_sprite": "gunship_spritesheet.png", "shipyard_sprite": "gunship_spritesheet.png", "animation_frames": 32, "sprite_size": 48, "animation_type": "rotation", "frame_rate": 30}, "passengerliner": {"id": "passengerliner", "name": "Passengerliner", "ship_class": "transport", "size": "medium", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "description": "", "game_sprite": "passenger_liner_spritesheet.png", "shipyard_sprite": "passenger_liner_spritesheet.png", "animation_frames": 32, "sprite_size": 48, "animation_type": "rotation", "frame_rate": 30}, "bulkcarrier": {"id": "bulkcarrier", "name": "Bulkcarrier", "ship_class": "transport", "size": "large", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "description": "", "game_sprite": "bulk_carrier_spritesheet.png", "shipyard_sprite": "bulk_carrier_spritesheet.png", "animation_frames": 32, "sprite_size": 64, "animation_type": "rotation", "frame_rate": 30}, "frigate": {"id": "frigate", "name": "Frigate", "ship_class": "frigate", "size": "large", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "description": "", "game_sprite": "frigate_spritesheet.png", "shipyard_sprite": "frigate_spritesheet.png", "animation_frames": 32, "sprite_size": 64, "animation_type": "rotation", "frame_rate": 30}, "heavyfreighter": {"id": "heavyfreighter", "name": "<PERSON>freighter", "ship_class": "freighter", "size": "large", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "description": "", "game_sprite": "heavy_freighter_spritesheet.png", "shipyard_sprite": "heavy_freighter_spritesheet.png", "animation_frames": 32, "sprite_size": 64, "animation_type": "rotation", "frame_rate": 30}, "battleship": {"id": "battleship", "name": "Battleship", "ship_class": "battleship", "size": "capital", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "description": "", "game_sprite": "battleship_spritesheet.png", "shipyard_sprite": "battleship_spritesheet.png", "animation_frames": 32, "sprite_size": 256, "animation_type": "rotation", "frame_rate": 30}, "carrier": {"id": "carrier", "name": "Carrier", "ship_class": "carrier", "size": "capital", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "description": "", "game_sprite": "carrier_spritesheet.png", "shipyard_sprite": "carrier_spritesheet.png", "animation_frames": 32, "sprite_size": 256, "animation_type": "rotation", "frame_rate": 30}, "cruiser": {"id": "cruiser", "name": "Cruiser", "ship_class": "cruiser", "size": "capital", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "description": "", "game_sprite": "cruiser_spritesheet.png", "shipyard_sprite": "cruiser_spritesheet.png", "animation_frames": 32, "sprite_size": 256, "animation_type": "rotation", "frame_rate": 30}, "destroyer": {"id": "destroyer", "name": "Destroyer", "ship_class": "destroyer", "size": "capital", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "description": "", "game_sprite": "destroyer_spritesheet.png", "shipyard_sprite": "destroyer_spritesheet.png", "animation_frames": 32, "sprite_size": 256, "animation_type": "rotation", "frame_rate": 30}, "courier": {"id": "courier", "name": "Courier", "ship_class": "freighter", "size": "small", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "description": "", "game_sprite": "courier_spritesheet.png", "shipyard_sprite": "courier_spritesheet.png", "animation_frames": 32, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30}}