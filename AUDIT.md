# AUDIT.md - EscapeVelocityPy Game Engine vs Editor Parameter Analysis

**Last Updated:** December 2024  
**Status:** Focused Parameter Audit  
**Purpose:** Document gaps between hardcoded behaviors vs editable parameters

---

## 📋 **DESIGN PHILOSOPHY CLARIFICATION**

### **🏗️ Game Engine = Rules of the Universe (SHOULD be hardcoded)**
- **HOW** missile guidance works (the tracking logic)
- **HOW** AI states function (patrol/attack/flee behaviors)
- **HOW** trading calculations work (price formulas)
- **HOW** physics and combat work (collision detection, movement)
- **HOW** power/fuel systems work (consumption mechanics)

### **🎨 Editor = Parameters of the Universe (SHOULD be data-driven)**
- **WHAT** ships exist and their power/fuel/speed stats
- **WHAT** outfits exist and their damage/cost/guidance parameters
- **WHICH** guidance level each missile has (0-100%)
- **HOW MUCH** power each weapon/thruster costs
- **HOW MUCH** damage, range, fire rate each weapon has

### **💡 Perfect Example: Missile System**
**Game Engine (Hardcoded Behavior):**
```python
if guidance_level > 0.7:  # Smart missile behavior
    turn_rate = 120 * guidance_level
    track_perfectly()
elif guidance_level > 0.3:  # Dumb missile behavior  
    turn_rate = 60 * guidance_level
    track_poorly()
else:  # Unguided behavior
    fly_straight()
```

**Editor (Data-driven Parameters):**
```json
"smart_missile": {
  "guidance_level": 0.9,  // 90% - uses smart behavior
  "damage": 50,
  "power_cost": 15
},
"dumb_missile": {
  "guidance_level": 0.4,  // 40% - uses dumb behavior
  "damage": 40, 
  "power_cost": 8
}
```

---

## 🚀 **SHIP PARAMETER GAPS ANALYSIS**

### **✅ What's Working Well (Keep Hardcoded)**
- Ship movement physics (thrust, drag, collision)
- Power consumption mechanics (how power is used)
- Fuel consumption mechanics (how fuel is used)
- Shield recharge behavior (how shields regenerate)

### **❌ Critical Parameter Gaps (Need Editor Control)**

| Parameter Category | Game Uses | JSON Has | Editor Has | Impact |
|-------------------|-----------|----------|------------|--------|
| **Power Stats** | ✅ Yes | ❌ No | ❌ No | Can't create power-hungry vs efficient ships |
| **Fuel Stats** | ✅ Yes | ❌ No | ❌ No | Can't create long-range vs short-range ships |
| **Power Costs** | ✅ Yes | ❌ No | ❌ No | All ships use same thruster power |
| **Physics Constants** | ✅ Yes | ❌ No | ❌ No | Can't fine-tune ship handling per ship |

### **🔧 Missing Ship Parameters for Editor**
```json
// What ships_data.json SHOULD have:
"scout": {
  // ... existing parameters ...
  "power_capacity": 60,
  "power_regen_rate": 3.0,
  "fuel_capacity": 80,
  "thruster_power_cost": 5.0,  // Per second when thrusting
  "turn_power_cost": 1.0,      // Per second when turning
  "shield_regen_power_cost": 2.0,
  "physics": {
    "drag_factor": 0.002,
    "shield_recharge_delay": 180
  }
}
```

---

## ⚙️ **OUTFIT PARAMETER GAPS ANALYSIS**

### **✅ What's Working Well (Keep Hardcoded)**
- Weapon firing behaviors (how lasers/missiles work)
- Projectile physics (collision detection, movement)
- Outfit installation logic (space requirements, tech levels)
- Shield/armor boost application

### **❌ Critical Parameter Gaps (Need Editor Control)**

| Parameter Category | Game Uses | JSON Has | Editor Has | Impact |
|-------------------|-----------|----------|------------|--------|
| **Weapon Power Costs** | ✅ Yes | ❌ No | ❌ No | All weapons use default power |
| **Guidance Levels** | ❌ No | ❌ No | ❌ No | Can't create dumb vs smart missiles |
| **Projectile Parameters** | ✅ Yes | ❌ No | ❌ No | All lasers same speed/color |
| **Mount Type Effects** | ✅ Yes | ✅ Yes | ✅ Yes | ✅ Working well |

### **🔧 Missing Outfit Parameters for Editor**
```json
// What outfits_data.json SHOULD have:
"laser_cannon": {
  // ... existing parameters ...
  "power_cost": 8.0,           // Power per shot
  "projectile_speed": 800,     // Laser bolt speed
  "projectile_color": [255, 0, 0],  // Red laser
  "projectile_size": [8, 2]    // Width x Height
},
"smart_missile": {
  // ... existing parameters ...
  "guidance_level": 0.9,       // 90% tracking ability
  "turn_rate": 120,            // Degrees per second
  "projectile_speed": 300,
  "acceleration": 100
}
```

---

## 🎯 **FOCUSED IMPLEMENTATION STRATEGY**

### **Phase 1: Core Parameter Control (HIGH IMPACT)**
**Goal:** Enable creation of ship/weapon varieties through editor

**Ship Parameters to Add:**
1. **Power System**: `power_capacity`, `power_regen_rate`, `fuel_capacity`
2. **Power Costs**: `thruster_power_cost`, `turn_power_cost`, `shield_regen_power_cost`
3. **Physics Tweaks**: `drag_factor`, `shield_recharge_delay`

**Outfit Parameters to Add:**
1. **Weapon Power**: `power_cost` for all weapons
2. **Projectile Variety**: `projectile_speed`, `projectile_color`, `projectile_size`
3. **Missile Guidance**: `guidance_level`, `turn_rate`, `acceleration`

### **Phase 2: Editor UI Expansion**
**Ship Editor Additions:**
- Power System section (capacity, regen, costs)
- Fuel System section (capacity, efficiency)
- Physics Tweaks section (optional advanced settings)

**Outfit Editor Additions:**
- Power Cost settings for all weapon types
- Projectile Customization for weapons
- Guidance Settings for missile-type weapons

### **Phase 3: Game Engine Integration**
**Modify Loading Code:**
```python
# Instead of hardcoded values, load from JSON with fallbacks
self.thruster_power_cost = data.get('thruster_power_cost', 5.0)
self.power_capacity = data.get('power_capacity', self._calculate_base_power())
```

**Update Editor Save Code:**
- Save new parameters to JSON files
- Ensure backwards compatibility with existing ships/outfits

---

## 🛠️ **CONTENT CREATION POSSIBILITIES**

### **Ships You Could Create:**
- **Power-Efficient Scout**: Low thruster costs, high regen
- **Power-Hungry Battleship**: High capacity, high costs
- **Long-Range Explorer**: High fuel capacity, efficient engines
- **Agile Fighter**: Low turn costs, quick shield recharge

### **Weapons You Could Create:**
- **Efficient Laser**: Low power cost, standard damage
- **Heavy Laser**: High power cost, high damage  
- **Fast Laser**: High speed projectile, standard power
- **Dumb Missile**: Low guidance (0.3), cheap, low damage
- **Smart Missile**: High guidance (0.9), expensive, high damage
- **Unguided Rocket**: No guidance (0.0), very cheap, moderate damage

### **Weapon Systems You Could Build:**
- **Laser Family**: Red/Green/Blue with different speeds/costs
- **Missile Family**: Dumb/Smart/Guided with varying parameters
- **Mass Driver Family**: Different projectile speeds/sizes
- **Energy Weapons**: Various power costs and effects

---

## 📝 **IMPLEMENTATION NOTES**

### **Keep These Behaviors Hardcoded (Good as-is):**
- AI state logic (patrol/attack/flee decision making)
- Trading price calculations (supply/demand formulas)
- Mission generation logic (how missions are created)
- Projectile physics (collision detection, movement)
- Combat mechanics (damage application, shield interactions)

### **Make These Parameters Editable:**
- Ship power/fuel stats and costs
- Weapon power costs and projectile properties
- Missile guidance levels and turn rates
- Ship-specific physics constants (optional)

### **Priority Order:**
1. **Ship power/fuel parameters** - Enables ship variety creation
2. **Weapon power costs** - Enables weapon balance through editor
3. **Projectile customization** - Enables weapon visual/behavior variety
4. **Missile guidance system** - Enables missile type variety

---

## 🎯 **SUCCESS METRICS**

**Phase 1 Complete When:**
- Can create power-efficient vs power-hungry ships in editor
- Can balance weapon power costs through editor
- Ships load power/fuel stats from JSON instead of calculations

**Phase 2 Complete When:**
- Can create different laser colors/speeds in editor
- Can create dumb/smart missile variants in editor
- All parameters save properly to JSON files

**Editor Success When:**
- Can create "Efficient Scout" with low power costs
- Can create "Power Laser" with high damage/cost
- Can create "Smart Missile" with 90% guidance
- Can create "Dumb Rocket" with 0% guidance
- All content works in-game without code changes

---

**🎯 CONCLUSION:** Focus on making ship/outfit **parameters** editable while keeping **behaviors** hardcoded. This achieves the goal of content creation flexibility without overcomplicating the architecture.
