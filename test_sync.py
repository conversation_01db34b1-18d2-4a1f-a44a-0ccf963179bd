"""
Test the editor/game sync
This will test that both systems can see the same outfits
"""

import sys
import os
from pathlib import Path

print("=== TESTING EDITOR/GAME SYNC ===")

# Test 1: Check what the game sees
print("\n1. Testing GAME outfit loading:")
try:
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
    
    from game_objects.standardized_outfits import OUTFITS_REGISTRY
    OUTFITS_REGISTRY.clear()  # Start fresh
    
    from game_objects.example_outfits import *  # This triggers game loading
    
    game_outfits = len(OUTFITS_REGISTRY)
    game_outfit_list = list(OUTFITS_REGISTRY.keys())[:5]
    print(f"   Game sees: {game_outfits} outfits")
    print(f"   Sample: {game_outfit_list}")
    
except Exception as e:
    print(f"   ❌ Game loading failed: {e}")
    game_outfits = 0
    game_outfit_list = []

# Test 2: Check what the editor sees
print("\n2. Testing EDITOR outfit loading:")
try:
    # Import editor data manager
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'editor_modules'))
    
    from data_manager import DataManager
    
    # Create data manager (this should trigger the same loading)
    dm = DataManager()
    
    editor_outfits = len(dm.outfits_registry)
    editor_outfit_list = list(dm.outfits_registry.keys())[:5]
    print(f"   Editor sees: {editor_outfits} outfits")
    print(f"   Sample: {editor_outfit_list}")
    
except Exception as e:
    print(f"   ❌ Editor loading failed: {e}")
    import traceback
    traceback.print_exc()
    editor_outfits = 0
    editor_outfit_list = []

# Test 3: Compare results
print("\n3. Comparing results:")
if game_outfits > 0 and editor_outfits > 0:
    if game_outfits == editor_outfits:
        print(f"   ✅ SUCCESS: Both see {game_outfits} outfits")
        
        # Check if they're the same outfits
        if set(game_outfit_list) == set(editor_outfit_list):
            print("   ✅ SUCCESS: Same outfits in both systems")
        else:
            print("   ⚠️  WARNING: Different outfits in each system")
            print(f"      Game: {game_outfit_list}")
            print(f"      Editor: {editor_outfit_list}")
    else:
        print(f"   ❌ MISMATCH: Game sees {game_outfits}, Editor sees {editor_outfits}")
elif game_outfits > 0:
    print(f"   ⚠️  Game works ({game_outfits} outfits) but Editor failed")
elif editor_outfits > 0:
    print(f"   ⚠️  Editor works ({editor_outfits} outfits) but Game failed")
else:
    print("   ❌ Both systems failed to load outfits")

# Test 4: Check if JSON file exists
print("\n4. Checking JSON file:")
json_file = Path(__file__).parent / "outfits_data.json"
if json_file.exists():
    try:
        import json
        with open(json_file, 'r') as f:
            data = json.load(f)
        print(f"   ✅ JSON file exists with {len(data)} outfits")
    except Exception as e:
        print(f"   ❌ JSON file exists but can't be read: {e}")
else:
    print("   ⚠️  No JSON file found")

print("\n=== TEST COMPLETE ===")
