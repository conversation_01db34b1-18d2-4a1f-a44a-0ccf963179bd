"""
Engines Editor for the Enhanced Content Editor
Handles editing of engine outfits
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .base_editor import BaseOutfitEditor
from .ui_components import StandardParameterGrid, ParameterLoader

class EnginesEditor(BaseOutfitEditor):
    """Editor for engine outfits."""
    
    def __init__(self, parent, data_manager):
        super().__init__(parent, data_manager, "engines")
    
    def setup_editor_ui(self, parent):
        """Setup the engines editor interface."""
        # Create scrollable frame
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Basic Properties Grid
        self.basic_grid = StandardParameterGrid(scrollable_frame, "Basic Properties")
        self.basic_grid.add_string_field("Name", "name")
        self.basic_grid.add_int_field("Cost", "cost", 100, 100000)
        self.basic_grid.add_int_field("Space Required", "space_required", 1, 50)
        self.basic_grid.add_combo_field("Engine Type", "subcategory", ["thruster", "steering", "afterburner", "jump"])
        self.basic_grid.add_int_field("Min Tech Level", "min_tech_level", 1, 10)
        self.basic_grid.add_file_field("Outfitter Icon", "outfitter_icon")
        self.basic_grid.add_file_field("Outfitter Image", "outfitter_image")
        self.basic_grid.pack(fill=tk.X, padx=5, pady=5)

        # Engine Performance Grid
        self.performance_grid = StandardParameterGrid(scrollable_frame, "Engine Performance")
        self.performance_grid.add_float_field("Acceleration Boost", "acceleration_boost", 0.0, 10.0, 0.1)
        self.performance_grid.add_float_field("Max Speed Boost", "max_speed_boost", 0.0, 10.0, 0.1)
        self.performance_grid.add_float_field("Turn Rate Boost", "turn_rate_boost", 0.0, 5.0, 0.1)
        self.performance_grid.add_float_field("Fuel Efficiency", "fuel_efficiency", 0.1, 3.0, 0.1)
        self.performance_grid.add_float_field("Energy Drain", "energy_drain", 0.0, 50.0, 0.1)
        self.performance_grid.pack(fill=tk.X, padx=5, pady=5)

        # Advanced Engine Features Grid
        self.advanced_grid = StandardParameterGrid(scrollable_frame, "Advanced Features")
        self.advanced_grid.add_combo_field("Reverse Thrust", "reverse_thrust_capable", ["False", "True"])
        self.advanced_grid.add_combo_field("Thrust Vectoring", "thrust_vectoring", ["False", "True"])
        self.advanced_grid.add_float_field("Afterburner Thrust", "afterburner_thrust", 0.0, 20.0, 0.1)
        self.advanced_grid.add_float_field("Afterburner Speed", "afterburner_speed", 0.0, 15.0, 0.1)
        self.advanced_grid.add_float_field("Afterburner Fuel Cost", "afterburner_fuel_cost", 1.0, 20.0, 0.1)
        self.advanced_grid.add_float_field("Afterburner Power Cost", "afterburner_power_cost", 5.0, 100.0, 1.0)
        self.advanced_grid.pack(fill=tk.X, padx=5, pady=5)

        # Operational Properties Grid
        self.operational_grid = StandardParameterGrid(scrollable_frame, "Operational Properties")
        self.operational_grid.add_float_field("Heat Generation", "heat_generation", 0.0, 50.0, 0.1)
        self.operational_grid.add_float_field("Overload Threshold", "overload_threshold", 50.0, 200.0, 1.0)
        self.operational_grid.add_float_field("Cooling Rate", "cooling_rate", 1.0, 50.0, 0.1)
        self.operational_grid.add_float_field("Startup Time", "startup_time", 0.0, 10.0, 0.1)
        self.operational_grid.add_float_field("Shutdown Time", "shutdown_time", 0.0, 10.0, 0.1)
        self.operational_grid.pack(fill=tk.X, padx=5, pady=5)

        # Engine Type Specific Grid
        self.specific_grid = StandardParameterGrid(scrollable_frame, "Type Specific Properties")
        self.specific_grid.add_combo_field("Thrust Direction", "thrust_direction", ["forward", "reverse", "omni"])
        self.specific_grid.add_float_field("Gimbal Range", "gimbal_range", 0.0, 90.0, 1.0)
        self.specific_grid.add_float_field("Reliability", "reliability", 0.5, 1.0, 0.01)
        self.specific_grid.add_float_field("Wear Rate", "wear_rate", 0.0001, 0.01, 0.0001)
        self.specific_grid.add_float_field("Maintenance Interval", "maintenance_interval", 10.0, 1000.0, 10.0)
        self.specific_grid.pack(fill=tk.X, padx=5, pady=5)

        # Visual Effects Grid
        self.visual_grid = StandardParameterGrid(scrollable_frame, "Visual Effects")
        self.visual_grid.add_file_field("Exhaust Sprite", "exhaust_sprite", 
                                      [("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")])
        self.visual_grid.add_file_field("Afterburner Sprite", "afterburner_sprite", 
                                      [("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")])
        self.visual_grid.add_file_field("Engine Sound", "engine_sound", 
                                      [("Audio files", "*.mp3 *.wav *.ogg"), ("All files", "*.*")])
        self.visual_grid.add_string_field("Exhaust Color (R,G,B)", "exhaust_color", 15)
        self.visual_grid.pack(fill=tk.X, padx=5, pady=5)
        
        # Description
        desc_frame = ttk.LabelFrame(scrollable_frame, text="Description")
        desc_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.description_text = tk.Text(desc_frame, height=3, wrap=tk.WORD)
        self.description_text.pack(fill=tk.X, padx=5, pady=5)

        # Save button
        save_frame = ttk.Frame(scrollable_frame)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        ttk.Button(save_frame, text="Save Engine", command=self.save_item).pack(side=tk.RIGHT, padx=5)
    
    def load_item_into_editor(self, engine):
        """Load engine data into the editor."""
        super().load_item_into_editor(engine)
        
        # Load using standardized parameter loader
        ParameterLoader.load_outfit_parameters(engine, self.basic_grid)
        ParameterLoader.load_outfit_parameters(engine, self.performance_grid)
        ParameterLoader.load_outfit_parameters(engine, self.advanced_grid)
        ParameterLoader.load_outfit_parameters(engine, self.operational_grid)
        ParameterLoader.load_outfit_parameters(engine, self.specific_grid)
        ParameterLoader.load_outfit_parameters(engine, self.visual_grid)
        
        # Load description
        self.description_text.delete(1.0, tk.END)
        self.description_text.insert(1.0, getattr(engine, 'description', ''))
    
    def save_item(self):
        """Save the current engine with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No engine selected to save")
            return
        
        try:
            # Save using standardized parameter loader
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.basic_grid)
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.performance_grid)
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.advanced_grid)
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.operational_grid)
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.specific_grid)
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.visual_grid)
            
            # Save description
            self.current_outfit.description = self.description_text.get(1.0, tk.END).strip()
            
            super().save_item()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save engine: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new engine instance."""
        class SimpleEngine:
            def __init__(self, id, name):
                # Basic properties
                self.id = id
                self.name = name
                self.category = "engines"
                self.subcategory = "thruster"
                self.cost = 5000
                self.space_required = 3
                self.min_tech_level = 1
                self.outfitter_icon = ""
                self.outfitter_image = ""
                
                # Engine Performance Properties
                self.acceleration_boost = 2.0
                self.max_speed_boost = 1.5
                self.turn_rate_boost = 0.5
                self.fuel_efficiency = 1.0
                self.energy_drain = 5.0
                
                # Advanced Engine Features
                self.reverse_thrust_capable = "False"
                self.thrust_vectoring = "False"
                self.afterburner_thrust = 0.0
                self.afterburner_speed = 0.0
                self.afterburner_fuel_cost = 5.0
                self.afterburner_power_cost = 20.0
                
                # Operational Properties
                self.heat_generation = 10.0
                self.overload_threshold = 100.0
                self.cooling_rate = 5.0
                self.startup_time = 1.0
                self.shutdown_time = 0.5
                
                # Type Specific Properties
                self.thrust_direction = "forward"
                self.gimbal_range = 15.0
                self.reliability = 0.95
                self.wear_rate = 0.001
                self.maintenance_interval = 100.0
                
                # Visual Effects
                self.exhaust_sprite = ""
                self.afterburner_sprite = ""
                self.engine_sound = ""
                self.exhaust_color = "0, 150, 255"
                
                self.description = ""

        return SimpleEngine(item_id, item_id.replace('_', ' ').title())
