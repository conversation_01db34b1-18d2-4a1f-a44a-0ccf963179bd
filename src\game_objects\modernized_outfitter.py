"""
Modernized Outfitter for the JSON-based Outfit System
Now reads outfit data from editor-created JSON files and displays images properly
"""

import pygame as pg
import os
from pathlib import Path
from game_objects.standardized_outfits import (
    get_outfit_by_id, OUTFITS_REGISTRY,
    OUTFIT_CATEGORY_WEAPONS, OUTFIT_CATEGORY_DEFENSE, OUTFIT_CATEGORY_ENGINES,
    OUTFIT_CATEGORY_ELECTRONICS, OUTFIT_CATEGORY_UTILITY, OUTFIT_CATEGORY_AMMUNITION,
    OUTFIT_CATEGORY_SPECIAL, Weapon, Ammunition
)

# UI Colors
BG_COLOR = (15, 15, 25)
PANEL_COLOR = (25, 25, 40)
PANEL_BORDER = (60, 60, 80)
TITLE_COLOR = (220, 220, 255)
TEXT_COLOR = (200, 200, 220)
SELECTED_COLOR = (120, 80, 200)
BUTTON_COLOR = (50, 70, 120)
BUTTON_TEXT_COLOR = (255, 255, 255)
BUTTON_DISABLED_COLOR = (40, 40, 50)
BUTTON_DISABLED_TEXT_COLOR = (120, 120, 120)

SUCCESS_COLOR = (100, 255, 100)
WARNING_COLOR = (255, 255, 100)
ERROR_COLOR = (255, 100, 100)
NEUTRAL_COLOR = (200, 200, 200)

# Category colors - FIXED FOR OUTFIT DISPLAY
CATEGORY_COLORS = {
    OUTFIT_CATEGORY_WEAPONS: (255, 120, 120),
    OUTFIT_CATEGORY_AMMUNITION: (255, 180, 100), 
    OUTFIT_CATEGORY_DEFENSE: (120, 150, 255),
    OUTFIT_CATEGORY_ENGINES: (120, 255, 120),
    OUTFIT_CATEGORY_ELECTRONICS: (255, 120, 255),
    OUTFIT_CATEGORY_UTILITY: (255, 255, 120),
    OUTFIT_CATEGORY_SPECIAL: (200, 255, 200),
    # String versions for outfit display compatibility
    "weapons": (255, 120, 120),
    "ammunition": (255, 180, 100),
    "defense": (120, 150, 255),
    "engines": (120, 255, 120),
    "electronics": (255, 120, 255),
    "utility": (255, 255, 120),
    "special": (200, 255, 200)
}

class ModernizedOutfitter:
    """Modern outfitter interface with JSON data and image support."""

    def __init__(self, game):
        self.game = game
        self.planet = None
        self.available_outfits = []
        self.player_outfits = []
        self.current_tab = "buy"
        self.current_category = OUTFIT_CATEGORY_WEAPONS
        self.selected_outfit = None
        self.scroll_offset = 0
        self.max_visible_items = 6
        self.ui_rects = {}
        self.outfit_item_rects = []
        self.outfit_images = {}  # Cache for loaded images
        self.ammo_quantity = 1  # For ammo purchases

    def open(self, planet):
        """Open outfitter on planet."""
        self.planet = planet
        if planet.tech_level == 0:
            self.game.set_status_message("This planet has no outfitter.", ERROR_COLOR)
            return False
        self._update_available_outfits()
        self._update_player_outfits()
        return True

    def _update_available_outfits(self):
        """Update available outfits based on tech level."""
        self.available_outfits = []
        print(f"🔍 Checking {len(OUTFITS_REGISTRY)} outfits from JSON...")
        
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            tech_level_req = getattr(outfit, 'min_tech_level', 1)
            if tech_level_req <= self.planet.tech_level:
                # Check ship size compatibility if the outfit has restrictions
                if hasattr(outfit, 'ship_size_restrictions') and outfit.ship_size_restrictions:
                    if self.game.player.ship.size not in outfit.ship_size_restrictions:
                        continue
                self.available_outfits.append(outfit)
                print(f"  ✅ {outfit.name} (Tech {tech_level_req})")
            else:
                print(f"  ❌ {outfit.name} (Tech {tech_level_req} > {self.planet.tech_level})")
        
        # Sort by category, then by cost
        self.available_outfits.sort(key=lambda x: (getattr(x, 'category', 'unknown'), getattr(x, 'cost', 0)))
        print(f"🎯 Total available: {len(self.available_outfits)} outfits")

    def _update_player_outfits(self):
        """Update player's installed outfits."""
        self.player_outfits = []
        for outfit_id, quantity in self.game.player.installed_outfits.items():
            outfit = get_outfit_by_id(outfit_id)
            if outfit and quantity > 0:
                self.player_outfits.append((outfit, quantity))
        self.player_outfits.sort(key=lambda x: (getattr(x[0], 'category', 'unknown'), x[0].name))

    def _get_outfits_in_category(self, category):
        """Get outfits in current category for current tab."""
        if self.current_tab == "buy":
            return [o for o in self.available_outfits if getattr(o, 'category', 'unknown') == category]
        else:
            return [(o, q) for o, q in self.player_outfits if getattr(o, 'category', 'unknown') == category]

    def _load_outfit_image(self, outfit, image_type="icon"):
        """Load outfit image with caching."""
        cache_key = f"{outfit.id}_{image_type}"
        if cache_key in self.outfit_images:
            return self.outfit_images[cache_key]

        image_path = ""
        if image_type == "icon":
            image_path = getattr(outfit, 'outfitter_icon', '')
        elif image_type == "image":
            image_path = getattr(outfit, 'outfitter_image', '')

        if not image_path:
            # Return None if no image path specified
            self.outfit_images[cache_key] = None
            return None

        try:
            # Try to load the image
            if not os.path.isabs(image_path):
                # Convert relative path to absolute
                full_path = os.path.join(os.getcwd(), image_path)
            else:
                full_path = image_path

            if os.path.exists(full_path):
                image = pg.image.load(full_path).convert_alpha()
                self.outfit_images[cache_key] = image
                return image
            else:
                # File doesn't exist - return None for default placeholder
                self.outfit_images[cache_key] = None
                return None

        except Exception as e:
            # Error loading - return None for default placeholder
            self.outfit_images[cache_key] = None
            return None

    def run(self, screen):
        """Main outfitter loop."""
        clock = pg.time.Clock()
        running = True

        while running and self.game.running:
            clock.tick(60)

            for event in pg.event.get():
                if event.type == pg.QUIT:
                    self.game.running = False
                    return "QUIT"
                elif event.type == pg.KEYDOWN:
                    if event.key == pg.K_ESCAPE:
                        print("[OUTFITTER] ESCAPE pressed - exiting loop")
                        self.game.status_message = ""
                        self.game.status_message_timer = 0
                        running = False  # Exit the loop
                        return "DOCKED"
                elif event.type == pg.MOUSEBUTTONDOWN:
                    if event.button == 1:
                        result = self._handle_click(pg.mouse.get_pos())
                        if result == "DOCKED":
                            print("[OUTFITTER] Exiting via back button")
                            running = False  # Exit the loop
                            return "DOCKED"
                elif event.type == pg.MOUSEWHEEL:
                    self._handle_scroll(event.y)

            # Update status message timer
            if hasattr(self.game, 'status_message_timer') and self.game.status_message_timer > 0:
                self.game.status_message_timer -= 1
                if self.game.status_message_timer <= 0:
                    self.game.status_message = ""

            self._draw(screen)
            pg.display.flip()

        return "DOCKED"  # Default return if loop exits

    def _handle_click(self, mouse_pos):
        """Handle mouse clicks."""
        # Tab buttons
        if 'tab_buy' in self.ui_rects and self.ui_rects['tab_buy'].collidepoint(mouse_pos):
            self.current_tab = "buy"
            self.selected_outfit = None
            self.scroll_offset = 0
        elif 'tab_sell' in self.ui_rects and self.ui_rects['tab_sell'].collidepoint(mouse_pos):
            self.current_tab = "sell"
            self.selected_outfit = None
            self.scroll_offset = 0

        # Category buttons
        for category in CATEGORY_COLORS.keys():
            if f'cat_{category}' in self.ui_rects and self.ui_rects[f'cat_{category}'].collidepoint(mouse_pos):
                self.current_category = category
                self.selected_outfit = None
                self.scroll_offset = 0

        # Outfit list items
        for i, rect in enumerate(self.outfit_item_rects):
            if rect.collidepoint(mouse_pos):
                self._select_outfit_at_index(i)
                break

        # Action button
        if 'action_button' in self.ui_rects and self.ui_rects['action_button'].collidepoint(mouse_pos):
            if self.selected_outfit:
                if self.current_tab == "buy":
                    self._buy_outfit()
                else:
                    self._sell_outfit()
                    
        # Quantity buttons for ammo
        if self.selected_outfit and hasattr(self.selected_outfit, 'category') and self.selected_outfit.category == OUTFIT_CATEGORY_AMMUNITION:
            if 'qty_minus' in self.ui_rects and self.ui_rects['qty_minus'].collidepoint(mouse_pos):
                self.ammo_quantity = max(1, self.ammo_quantity - 1)
            elif 'qty_plus' in self.ui_rects and self.ui_rects['qty_plus'].collidepoint(mouse_pos):
                self.ammo_quantity = min(99, self.ammo_quantity + 1)

        # Back button - DEBUG VERSION
        if 'back_button' in self.ui_rects and self.ui_rects['back_button'].collidepoint(mouse_pos):
            print("[OUTFITTER] Back button clicked - returning to DOCKED")
            self.game.status_message = ""
            self.game.status_message_timer = 0
            return "DOCKED"
        else:
            # Debug: Check if back button rect exists
            if 'back_button' not in self.ui_rects:
                print("[OUTFITTER DEBUG] Back button rect not found in ui_rects!")
            elif self.ui_rects['back_button']:
                print(f"[OUTFITTER DEBUG] Back button exists but click missed. Mouse: {mouse_pos}, Rect: {self.ui_rects['back_button']}")

    def _draw(self, screen):
        """Draw the outfitter interface."""
        screen.fill(BG_COLOR)
        width, height = screen.get_size()

        self._draw_title(screen, width)
        self._draw_credits(screen, width)
        self._draw_tabs(screen, width)
        self._draw_categories(screen, width)
        self._draw_main_panels(screen, width, height)
        self._draw_ship_stats(screen, width, height)
        self._draw_instructions(screen, width, height)
        self._draw_status_messages(screen, width, height)

    def _draw_title(self, screen, width):
        """Draw title."""
        title = f"Outfitter - {self.planet.name} (Tech Level {self.planet.tech_level})"
        font = pg.font.Font(None, 40)
        text_surf = font.render(title, True, TITLE_COLOR)
        text_rect = text_surf.get_rect(center=(width // 2, 30))
        screen.blit(text_surf, text_rect)

    def _draw_categories(self, screen, width):
        """Draw category buttons."""
        categories = [OUTFIT_CATEGORY_WEAPONS, OUTFIT_CATEGORY_AMMUNITION, OUTFIT_CATEGORY_DEFENSE,
                     OUTFIT_CATEGORY_ENGINES, OUTFIT_CATEGORY_ELECTRONICS, OUTFIT_CATEGORY_UTILITY]

        cat_width, cat_height, cat_y = 90, 30, 110
        total_width = len(categories) * cat_width + (len(categories) - 1) * 5
        start_x = (width - total_width) // 2
        font = pg.font.Font(None, 18)

        for i, category in enumerate(categories):
            cat_x = start_x + i * (cat_width + 5)
            cat_rect = pg.Rect(cat_x, cat_y, cat_width, cat_height)

            cat_color = CATEGORY_COLORS[category] if category == self.current_category else BUTTON_COLOR
            pg.draw.rect(screen, cat_color, cat_rect)
            pg.draw.rect(screen, PANEL_BORDER, cat_rect, 1)
            self.ui_rects[f'cat_{category}'] = cat_rect

            text_surf = font.render(category.title(), True, BUTTON_TEXT_COLOR)
            text_rect = text_surf.get_rect(center=cat_rect.center)
            screen.blit(text_surf, text_rect)

    def _draw_main_panels(self, screen, width, height):
        """Draw main outfit list and details panels."""
        panel_y = 160
        panel_height = height - panel_y - 100

        # Three-panel layout: [List] [Ship Stats] [Details + Image]
        list_width = width * 0.35
        ship_width = width * 0.25
        details_width = width * 0.35

        # Outfit list (left)
        list_rect = pg.Rect(10, panel_y, list_width, panel_height)
        pg.draw.rect(screen, PANEL_COLOR, list_rect)
        pg.draw.rect(screen, PANEL_BORDER, list_rect, 2)
        self._draw_outfit_list(screen, list_rect)

        # Ship stats (middle) - handled in _draw_ship_stats

        # Details with image (right)  
        details_rect = pg.Rect(list_width + ship_width + 20, panel_y, details_width, panel_height)
        pg.draw.rect(screen, PANEL_COLOR, details_rect)
        pg.draw.rect(screen, PANEL_BORDER, details_rect, 2)
        self._draw_outfit_details(screen, details_rect)

    def _draw_outfit_list(self, screen, list_rect):
        """Draw outfit list with icons."""
        font = pg.font.Font(None, 20)
        small_font = pg.font.Font(None, 16)

        # Title
        title = f"{self.current_category.title()}"
        title_surf = font.render(title, True, TITLE_COLOR)
        screen.blit(title_surf, (list_rect.left + 10, list_rect.top + 10))

        # Get outfits
        outfits = self._get_outfits_in_category(self.current_category)
        if not outfits:
            no_items = "No items available" if self.current_tab == "buy" else "No items installed"
            text_surf = small_font.render(no_items, True, TEXT_COLOR)
            screen.blit(text_surf, (list_rect.left + 10, list_rect.top + 40))
            return

        # Draw outfit items
        self.outfit_item_rects = []
        item_height, start_y = 60, list_rect.top + 40
        visible_outfits = outfits[self.scroll_offset:self.scroll_offset + self.max_visible_items]

        for i, outfit_data in enumerate(visible_outfits):
            item_y = start_y + i * item_height
            item_rect = pg.Rect(list_rect.left + 5, item_y, list_rect.width - 10, item_height - 2)

            # Check if selected
            is_selected = (self.selected_outfit and
                          ((self.current_tab == "buy" and outfit_data == self.selected_outfit) or
                           (self.current_tab == "sell" and outfit_data[0] == self.selected_outfit)))

            # Draw background
            bg_color = SELECTED_COLOR if is_selected else (40, 40, 55)
            pg.draw.rect(screen, bg_color, item_rect)
            pg.draw.rect(screen, PANEL_BORDER, item_rect, 1)
            self.outfit_item_rects.append(item_rect)

            # Extract outfit info
            if self.current_tab == "buy":
                outfit = outfit_data
                quantity_text = ""
            else:
                outfit, quantity = outfit_data
                quantity_text = f" x{quantity}"

            # Draw outfit icon
            icon_rect = pg.Rect(item_rect.left + 5, item_rect.top + 5, 50, 50)
            icon_image = self._load_outfit_image(outfit, "icon")
            if icon_image:
                # Scale icon to fit
                scaled_icon = pg.transform.scale(icon_image, (50, 50))
                screen.blit(scaled_icon, icon_rect)
            else:
                # Default colored square with X if no icon
                pg.draw.rect(screen, CATEGORY_COLORS[getattr(outfit, 'category', 'unknown')], icon_rect)
                pg.draw.rect(screen, PANEL_BORDER, icon_rect, 1)
                # Draw simple X
                pg.draw.line(screen, (255, 255, 255), (icon_rect.left + 10, icon_rect.top + 10), (icon_rect.right - 10, icon_rect.bottom - 10), 2)
                pg.draw.line(screen, (255, 255, 255), (icon_rect.right - 10, icon_rect.top + 10), (icon_rect.left + 10, icon_rect.bottom - 10), 2)

            # Draw outfit name
            name_text = f"{outfit.name}{quantity_text}"
            name_surf = font.render(name_text, True, TEXT_COLOR)
            text_rect = pg.Rect(item_rect.left + 60, item_rect.top + 5, item_rect.width - 65, 20)
            if name_surf.get_width() > text_rect.width:
                # Truncate if too long
                name_text = name_text[:20] + "..."
                name_surf = font.render(name_text, True, TEXT_COLOR)
            screen.blit(name_surf, text_rect)

            # Draw price and space
            price_text = f"{getattr(outfit, 'cost', 0)} cr" if self.current_tab == "buy" else f"{getattr(outfit, 'cost', 0) // 2} cr"
            price_surf = small_font.render(price_text, True, CATEGORY_COLORS[getattr(outfit, 'category', 'unknown')])
            screen.blit(price_surf, (item_rect.left + 60, item_rect.top + 25))

            space_text = f"{getattr(outfit, 'space_required', 0)} tons"
            space_surf = small_font.render(space_text, True, NEUTRAL_COLOR)
            space_rect = space_surf.get_rect(right=item_rect.right - 5, top=item_rect.top + 25)
            screen.blit(space_surf, space_rect)

    def _draw_ship_stats(self, screen, width, height):
        """Draw ship stats panel (middle)."""
        panel_y = 160
        panel_height = height - panel_y - 100
        list_width = width * 0.35
        ship_width = width * 0.25

        ship_rect = pg.Rect(list_width + 10, panel_y, ship_width, panel_height)
        pg.draw.rect(screen, PANEL_COLOR, ship_rect)
        pg.draw.rect(screen, PANEL_BORDER, ship_rect, 2)

        font = pg.font.Font(None, 20)
        small_font = pg.font.Font(None, 16)

        # Title
        title_surf = font.render("Your Ship", True, TITLE_COLOR)
        screen.blit(title_surf, (ship_rect.left + 10, ship_rect.top + 10))

        # Ship name and class
        ship = self.game.player.ship
        name_surf = small_font.render(f"{ship.name}", True, TEXT_COLOR)
        screen.blit(name_surf, (ship_rect.left + 10, ship_rect.top + 35))

        class_surf = small_font.render(f"{ship.ship_class.title()} ({ship.size.title()})", True, NEUTRAL_COLOR)
        screen.blit(class_surf, (ship_rect.left + 10, ship_rect.top + 50))

        # Stats
        y = ship_rect.top + 75
        line_height = 18

        stats = [
            ("Power", f"{int(self.game.player.power)}/{int(self.game.player.power_capacity)}"),
            ("Fuel", f"{int(self.game.player.fuel)}/{int(self.game.player.fuel_capacity)}"),
            ("Shields", f"{int(self.game.player.shields)}/{self.game.player.max_shields}"),
            ("Armor", f"{int(self.game.player.armor)}/{self.game.player.max_armor}"),
            ("Outfit Space", f"{self.game.player.used_outfit_space}/{self.game.player.outfit_space}"),
            ("Cargo", f"{sum(self.game.player.cargo.values())}/{self.game.player.cargo_space}"),
        ]

        for label, value in stats:
            label_surf = small_font.render(f"{label}:", True, TEXT_COLOR)
            screen.blit(label_surf, (ship_rect.left + 10, y))
            
            value_surf = small_font.render(value, True, NEUTRAL_COLOR)
            value_rect = value_surf.get_rect(right=ship_rect.right - 10, top=y)
            screen.blit(value_surf, value_rect)
            
            y += line_height

        # Space usage bar if outfit selected
        if self.selected_outfit and self.current_tab == "buy":
            y += 10
            space_req = getattr(self.selected_outfit, 'space_required', 0)
            current_space = self.game.player.used_outfit_space
            total_space = self.game.player.outfit_space
            
            # Preview bar
            bar_width = ship_rect.width - 20
            bar_height = 15
            bar_rect = pg.Rect(ship_rect.left + 10, y, bar_width, bar_height)
            
            # Background
            pg.draw.rect(screen, (30, 30, 30), bar_rect)
            pg.draw.rect(screen, PANEL_BORDER, bar_rect, 1)
            
            # Current usage
            if total_space > 0:
                current_width = int((current_space / total_space) * bar_width)
                if current_width > 0:
                    current_rect = pg.Rect(bar_rect.left, bar_rect.top, current_width, bar_height)
                    pg.draw.rect(screen, (100, 150, 255), current_rect)
                
                # Preview addition
                preview_width = int((space_req / total_space) * bar_width)
                if current_width + preview_width <= bar_width:
                    preview_rect = pg.Rect(bar_rect.left + current_width, bar_rect.top, preview_width, bar_height)
                    pg.draw.rect(screen, WARNING_COLOR, preview_rect)
                else:
                    # Would exceed capacity
                    exceed_rect = pg.Rect(bar_rect.left + current_width, bar_rect.top, bar_width - current_width, bar_height)
                    pg.draw.rect(screen, ERROR_COLOR, exceed_rect)
            
            preview_text = f"Preview: +{space_req} tons"
            preview_surf = small_font.render(preview_text, True, TEXT_COLOR)
            screen.blit(preview_surf, (ship_rect.left + 10, y + bar_height + 5))
            y += bar_height + 25

        # Installed Outfits Section
        if y < ship_rect.bottom - 100:  # Only show if space available
            y += 10
            outfit_title_surf = small_font.render("Installed Outfits:", True, TITLE_COLOR)
            screen.blit(outfit_title_surf, (ship_rect.left + 10, y))
            y += line_height
            
            # Get outfits by category
            from game_objects.standardized_outfits import get_outfit_by_id
            outfit_categories = {"weapons": [], "ammunition": [], "defense": [], "engines": [], "electronics": [], "utility": []}
            
            for outfit_id, quantity in self.game.player.installed_outfits.items():
                outfit_template = get_outfit_by_id(outfit_id)
                if outfit_template and quantity > 0:
                    category = getattr(outfit_template, 'category', 'utility')
                    if category in outfit_categories:
                        outfit_categories[category].append((outfit_template, quantity))
            
            # Display outfits by category
            for category, outfits in outfit_categories.items():
                if outfits and y < ship_rect.bottom - 30:
                    # Category header
                    cat_color = CATEGORY_COLORS.get(category, NEUTRAL_COLOR)
                    cat_surf = small_font.render(f"{category.title()}:", True, cat_color)
                    screen.blit(cat_surf, (ship_rect.left + 15, y))
                    y += line_height - 2
                    
                    # List outfits in category
                    for outfit, qty in outfits[:3]:  # Show max 3 per category
                        if y >= ship_rect.bottom - 15:
                            break
                        qty_text = f"x{qty}" if qty > 1 else ""
                        outfit_name = outfit.name if len(outfit.name) <= 15 else outfit.name[:12] + "..."
                        outfit_surf = small_font.render(f"  {outfit_name} {qty_text}", True, NEUTRAL_COLOR)
                        screen.blit(outfit_surf, (ship_rect.left + 20, y))
                        y += line_height - 4
                    
                    if len(outfits) > 3:
                        more_surf = small_font.render(f"  ... +{len(outfits)-3} more", True, (120, 120, 120))
                        screen.blit(more_surf, (ship_rect.left + 20, y))
                        y += line_height - 2

    def _draw_outfit_details(self, screen, details_rect):
        """Draw outfit details with large image."""
        font = pg.font.Font(None, 24)
        medium_font = pg.font.Font(None, 20)
        small_font = pg.font.Font(None, 16)

        # Title
        title_surf = font.render("Outfit Details", True, TITLE_COLOR)
        screen.blit(title_surf, (details_rect.left + 10, details_rect.top + 10))

        if not self.selected_outfit:
            hint_surf = small_font.render("Select an outfit to view details", True, TEXT_COLOR)
            screen.blit(hint_surf, (details_rect.left + 10, details_rect.top + 40))
            return

        outfit = self.selected_outfit
        y = details_rect.top + 45

        # Outfit name
        name_surf = font.render(outfit.name, True, TEXT_COLOR)
        screen.blit(name_surf, (details_rect.left + 10, y))
        y += 30

        # Large outfit image - try outfitter_image first, fallback to outfitter_icon
        image_rect = pg.Rect(details_rect.left + 10, y, details_rect.width - 20, 120)
        outfit_image = self._load_outfit_image(outfit, "image")
        if not outfit_image:
            # Fallback to icon if no large image
            outfit_image = self._load_outfit_image(outfit, "icon")
            
        if outfit_image:
            # Scale image to fit while maintaining aspect ratio
            img_rect = outfit_image.get_rect()
            scale_factor = min(image_rect.width / img_rect.width, image_rect.height / img_rect.height)
            new_size = (int(img_rect.width * scale_factor), int(img_rect.height * scale_factor))
            scaled_image = pg.transform.scale(outfit_image, new_size)
            
            # Center the image
            img_x = image_rect.centerx - scaled_image.get_width() // 2
            img_y = image_rect.centery - scaled_image.get_height() // 2
            screen.blit(scaled_image, (img_x, img_y))
        else:
            # Default colored rectangle with X
            pg.draw.rect(screen, CATEGORY_COLORS[getattr(outfit, 'category', 'unknown')], image_rect)
            pg.draw.rect(screen, PANEL_BORDER, image_rect, 2)
            
            # Draw large X
            pg.draw.line(screen, (255, 255, 255), (image_rect.left + 20, image_rect.top + 20), (image_rect.right - 20, image_rect.bottom - 20), 4)
            pg.draw.line(screen, (255, 255, 255), (image_rect.right - 20, image_rect.top + 20), (image_rect.left + 20, image_rect.bottom - 20), 4)

        y = image_rect.bottom + 15

        # Cost and space
        if self.current_tab == "buy":
            cost = getattr(outfit, 'cost', 0)
            cost_text = f"Cost: {cost} credits"
            cost_color = ERROR_COLOR if self.game.player.credits < cost else SUCCESS_COLOR
        else:
            cost = getattr(outfit, 'cost', 0) // 2
            cost_text = f"Sell Value: {cost} credits"
            cost_color = SUCCESS_COLOR

        cost_surf = medium_font.render(cost_text, True, cost_color)
        screen.blit(cost_surf, (details_rect.left + 10, y))
        y += 25

        space_req = getattr(outfit, 'space_required', 0)
        space_text = f"Space: {space_req} tons"
        space_available = self.game.player.outfit_space - self.game.player.used_outfit_space
        space_color = ERROR_COLOR if (space_req > space_available and self.current_tab == "buy") else NEUTRAL_COLOR
        space_surf = medium_font.render(space_text, True, space_color)
        screen.blit(space_surf, (details_rect.left + 10, y))
        y += 25

        # Tech Level
        tech_level = getattr(outfit, 'min_tech_level', 1)
        tech_surf = medium_font.render(f"Tech Level: {tech_level}", True, NEUTRAL_COLOR)
        screen.blit(tech_surf, (details_rect.left + 10, y))
        y += 30

        # Category-specific stats
        if hasattr(outfit, 'category') and outfit.category == "weapons":
            damage = getattr(outfit, 'damage', 0)
            if not damage:
                damage = getattr(outfit, 'shield_damage', 0)  # Try alternate naming
            
            fire_rate = getattr(outfit, 'fire_rate', 0)
            weapon_range = getattr(outfit, 'range', 0)
            energy_usage = getattr(outfit, 'energy_usage', 0)
            
            weapon_stats = [
                f"Damage: {damage}",
                f"Fire Rate: {fire_rate:.1f}/sec",
                f"Range: {weapon_range}",
                f"Energy: {energy_usage}"
            ]
            
            for stat in weapon_stats:
                stat_surf = small_font.render(stat, True, NEUTRAL_COLOR)
                screen.blit(stat_surf, (details_rect.left + 10, y))
                y += 18
                
        elif hasattr(outfit, 'category') and outfit.category == "ammunition":
            # Show ammo stats and compatibility
            shield_damage = getattr(outfit, 'shield_damage', 0)
            armor_damage = getattr(outfit, 'armor_damage', 0)
            quantity = getattr(outfit, 'quantity', 10)
            behavior = getattr(outfit, 'projectile_behavior', 'dumbfire')
            
            ammo_stats = [
                f"Shield Damage: {shield_damage}",
                f"Armor Damage: {armor_damage}",
                f"Quantity: {quantity}",
                f"Behavior: {behavior.title()}"
            ]
            
            for stat in ammo_stats:
                stat_surf = small_font.render(stat, True, NEUTRAL_COLOR)
                screen.blit(stat_surf, (details_rect.left + 10, y))
                y += 18
            
            # Show compatible weapons - ENHANCED DEBUG
            compatible_launchers = getattr(outfit, 'compatible_launchers', [])
            print(f"🔍 DEBUG: Ammo {outfit.name} compatible_launchers: {compatible_launchers}")
            compatible_weapons = []
            print(f"🔍 DEBUG: Player weapons: {[(w.id, getattr(w, 'uses_ammo', 'NO_ATTR')) for w in self.game.player.weapons]}")
            for weapon in self.game.player.weapons:
                uses_ammo = getattr(weapon, 'uses_ammo', False)
                weapon_id = getattr(weapon, 'id', 'NO_ID')
                print(f"🔍 DEBUG: Checking weapon {weapon_id}, uses_ammo={uses_ammo}, in_list={weapon_id in compatible_launchers}")
                if (hasattr(weapon, 'uses_ammo') and weapon.uses_ammo and 
                    weapon.id in compatible_launchers):
                    compatible_weapons.append(weapon)
                    print(f"✅ DEBUG: Added compatible weapon: {weapon.id}")
                else:
                    print(f"❌ DEBUG: Weapon {weapon_id} not compatible: uses_ammo={uses_ammo}, in_list={weapon_id in compatible_launchers}")
            
            if compatible_weapons:
                y += 5
                compat_surf = small_font.render("Compatible with:", True, SUCCESS_COLOR)
                screen.blit(compat_surf, (details_rect.left + 10, y))
                y += 16
                
                for weapon in compatible_weapons[:3]:  # Show max 3
                    current = getattr(weapon, 'current_ammo', 0)
                    maximum = getattr(weapon, 'max_ammo', 20)
                    weapon_surf = small_font.render(f"  {weapon.name} ({current}/{maximum})", True, NEUTRAL_COLOR)
                    screen.blit(weapon_surf, (details_rect.left + 10, y))
                    y += 16
            else:
                y += 5
                no_compat_surf = small_font.render("No compatible weapons!", True, WARNING_COLOR)
                screen.blit(no_compat_surf, (details_rect.left + 10, y))
                y += 16

        # Description
        description = getattr(outfit, 'description', '')
        if description:
            y += 5
            desc_lines = description.split('. ')  # Simple line breaking
            for line in desc_lines[:3]:  # Limit to 3 lines
                if line.strip():
                    desc_surf = small_font.render(line.strip(), True, TEXT_COLOR)
                    screen.blit(desc_surf, (details_rect.left + 10, y))
                    y += 16

        # Buy/Sell button
        button_width, button_height = 120, 35
        button_x = details_rect.centerx - button_width // 2
        button_y = details_rect.bottom - 80  # Move up to make room for quantity controls
        button_rect = pg.Rect(button_x, button_y, button_width, button_height)

        # Quantity selector for ammo
        if (hasattr(outfit, 'category') and outfit.category == OUTFIT_CATEGORY_AMMUNITION and 
            self.current_tab == "buy"):
            
            # Quantity controls
            qty_y = button_y + button_height + 10
            
            # Minus button
            minus_rect = pg.Rect(button_x - 30, qty_y, 25, 25)
            pg.draw.rect(screen, BUTTON_COLOR, minus_rect)
            pg.draw.rect(screen, PANEL_BORDER, minus_rect, 1)
            self.ui_rects['qty_minus'] = minus_rect
            
            minus_font = pg.font.Font(None, 20)
            minus_text = minus_font.render("-", True, BUTTON_TEXT_COLOR)
            minus_text_rect = minus_text.get_rect(center=minus_rect.center)
            screen.blit(minus_text, minus_text_rect)
            
            # Quantity display
            qty_rect = pg.Rect(button_x, qty_y, button_width, 25)
            pg.draw.rect(screen, PANEL_COLOR, qty_rect)
            pg.draw.rect(screen, PANEL_BORDER, qty_rect, 1)
            
            qty_text = f"Qty: {self.ammo_quantity}"
            qty_surf = pg.font.Font(None, 20).render(qty_text, True, TEXT_COLOR)
            qty_text_rect = qty_surf.get_rect(center=qty_rect.center)
            screen.blit(qty_surf, qty_text_rect)
            
            # Plus button
            plus_rect = pg.Rect(button_x + button_width + 5, qty_y, 25, 25)
            pg.draw.rect(screen, BUTTON_COLOR, plus_rect)
            pg.draw.rect(screen, PANEL_BORDER, plus_rect, 1)
            self.ui_rects['qty_plus'] = plus_rect
            
            plus_text = minus_font.render("+", True, BUTTON_TEXT_COLOR)
            plus_text_rect = plus_text.get_rect(center=plus_rect.center)
            screen.blit(plus_text, plus_text_rect)
            
            # Update cost display for multiple items
            total_cost = getattr(outfit, 'cost', 0) * self.ammo_quantity
            button_text = f"Buy ({total_cost} cr)"
        else:
            button_text = "Buy" if self.current_tab == "buy" else "Sell"

        if self.current_tab == "buy":
            if hasattr(outfit, 'category') and outfit.category == OUTFIT_CATEGORY_AMMUNITION:
                total_cost = getattr(outfit, 'cost', 0) * self.ammo_quantity
                can_afford = self.game.player.credits >= total_cost
            else:
                outfit_cost = getattr(outfit, 'cost', 0)
                can_afford = self.game.player.credits >= outfit_cost
                
            outfit_space = getattr(outfit, 'space_required', 0)
            has_space = (self.game.player.used_outfit_space + outfit_space <= self.game.player.outfit_space)
            can_buy = can_afford and (has_space or outfit.category == OUTFIT_CATEGORY_AMMUNITION)
            
            button_color = BUTTON_COLOR if can_buy else BUTTON_DISABLED_COLOR
            text_color = BUTTON_TEXT_COLOR if can_buy else BUTTON_DISABLED_TEXT_COLOR
        else:
            button_color = BUTTON_COLOR
            text_color = BUTTON_TEXT_COLOR
            button_text = "Sell"

        pg.draw.rect(screen, button_color, button_rect)
        pg.draw.rect(screen, PANEL_BORDER, button_rect, 2)
        self.ui_rects['action_button'] = button_rect

        text_surf = pg.font.Font(None, 20).render(button_text, True, text_color)
        text_rect = text_surf.get_rect(center=button_rect.center)
        screen.blit(text_surf, text_rect)

    def _draw_credits(self, screen, width):
        """Draw player credits."""
        credits_text = f"Credits: {self.game.player.credits:,}"
        font = pg.font.Font(None, 28)
        text_surf = font.render(credits_text, True, SUCCESS_COLOR)
        text_rect = text_surf.get_rect(topright=(width - 20, 15))
        screen.blit(text_surf, text_rect)

    def _draw_tabs(self, screen, width):
        """Draw buy/sell tabs."""
        tab_width, tab_height, tab_y = 100, 35, 70

        # Buy tab
        buy_rect = pg.Rect(width // 2 - tab_width - 10, tab_y, tab_width, tab_height)
        buy_color = SELECTED_COLOR if self.current_tab == "buy" else BUTTON_COLOR
        pg.draw.rect(screen, buy_color, buy_rect)
        pg.draw.rect(screen, PANEL_BORDER, buy_rect, 2)
        self.ui_rects['tab_buy'] = buy_rect

        font = pg.font.Font(None, 24)
        text_surf = font.render("Buy", True, BUTTON_TEXT_COLOR)
        text_rect = text_surf.get_rect(center=buy_rect.center)
        screen.blit(text_surf, text_rect)

        # Sell tab
        sell_rect = pg.Rect(width // 2 + 10, tab_y, tab_width, tab_height)
        sell_color = SELECTED_COLOR if self.current_tab == "sell" else BUTTON_COLOR
        pg.draw.rect(screen, sell_color, sell_rect)
        pg.draw.rect(screen, PANEL_BORDER, sell_rect, 2)
        self.ui_rects['tab_sell'] = sell_rect

        text_surf = font.render("Sell", True, BUTTON_TEXT_COLOR)
        text_rect = text_surf.get_rect(center=sell_rect.center)
        screen.blit(text_surf, text_rect)

    def _draw_instructions(self, screen, width, height):
        """Draw instructions at the bottom."""
        instructions = "Mouse wheel to scroll • Click outfits to view details • ESCAPE or Back button to return"
        font = pg.font.Font(None, 16)
        text_surf = font.render(instructions, True, TEXT_COLOR)
        text_rect = text_surf.get_rect(center=(width // 2, height - 35))
        screen.blit(text_surf, text_rect)

        # Back button - FIXED POSITIONING
        back_width, back_height = 100, 35
        back_rect = pg.Rect(width - back_width - 20, height - back_height - 10, back_width, back_height)
        pg.draw.rect(screen, BUTTON_COLOR, back_rect)
        pg.draw.rect(screen, PANEL_BORDER, back_rect, 1)
        self.ui_rects['back_button'] = back_rect
        
        back_font = pg.font.Font(None, 20)
        back_text = back_font.render("Back", True, BUTTON_TEXT_COLOR)
        back_text_rect = back_text.get_rect(center=back_rect.center)
        screen.blit(back_text, back_text_rect)
        
    def _draw_status_messages(self, screen, width, height):
        """Draw status messages from the game."""
        if hasattr(self.game, 'status_message') and self.game.status_message and self.game.status_message_timer > 0:
            # Draw status message at the bottom - simple and non-blocking
            msg_font = pg.font.Font(None, 24)
            msg_surf = msg_font.render(self.game.status_message, True, self.game.status_message_color)
            msg_rect = msg_surf.get_rect(center=(width // 2, height - 60))
            
            # Simple dark background - no alpha issues
            bg_rect = msg_rect.inflate(20, 10)
            pg.draw.rect(screen, (40, 40, 40), bg_rect)
            pg.draw.rect(screen, (100, 100, 100), bg_rect, 1)
            
            screen.blit(msg_surf, msg_rect)

    def _handle_scroll(self, scroll_y):
        """Handle mouse wheel scrolling."""
        outfits = self._get_outfits_in_category(self.current_category)
        max_scroll = max(0, len(outfits) - self.max_visible_items)

        if scroll_y > 0:  # Scroll up
            self.scroll_offset = max(0, self.scroll_offset - 1)
        elif scroll_y < 0:  # Scroll down
            self.scroll_offset = min(max_scroll, self.scroll_offset + 1)

    def _select_outfit_at_index(self, index):
        """Select outfit at given index."""
        outfits = self._get_outfits_in_category(self.current_category)
        actual_index = index + self.scroll_offset

        if 0 <= actual_index < len(outfits):
            if self.current_tab == "buy":
                self.selected_outfit = outfits[actual_index]
            else:
                self.selected_outfit = outfits[actual_index][0]

    def _buy_outfit(self):
        """Buy the selected outfit with visual feedback - FIXED."""
        outfit = self.selected_outfit
        player = self.game.player

        # Handle ammunition purchases
        if hasattr(outfit, 'category') and outfit.category == OUTFIT_CATEGORY_AMMUNITION:
            # Check if player has compatible launchers - ENHANCED DEBUG
            compatible_launchers = getattr(outfit, 'compatible_launchers', [])
            print(f"💰 BUY DEBUG: Ammo {outfit.name} compatible_launchers: {compatible_launchers}")
            compatible_weapons = []
            for weapon in player.weapons:
                weapon_id = getattr(weapon, 'id', 'NO_ID')
                uses_ammo = getattr(weapon, 'uses_ammo', False)
                print(f"💰 BUY DEBUG: Checking weapon {weapon_id}, uses_ammo={uses_ammo}")
                if (hasattr(weapon, 'uses_ammo') and weapon.uses_ammo and 
                    weapon.id in compatible_launchers):
                    compatible_weapons.append(weapon)
                    print(f"✅ BUY DEBUG: Found compatible weapon: {weapon.id}")
            
            print(f"💰 BUY DEBUG: Found {len(compatible_weapons)} compatible weapons")
            if not compatible_weapons:
                self.game.set_status_message(f"No compatible launchers for {outfit.name}!", WARNING_COLOR, 120)
                return

            # Calculate cost for exact quantity
            unit_cost = getattr(outfit, 'cost', 0)
            total_cost = unit_cost * self.ammo_quantity
            
            # Check affordability
            if player.credits < total_cost:
                self.game.set_status_message("Insufficient credits!", ERROR_COLOR, 90)
                return

            # Check outfit space (each missile/round takes space)
            space_per_unit = getattr(outfit, 'space_required', 1)
            total_space_needed = space_per_unit * self.ammo_quantity
            
            if player.used_outfit_space + total_space_needed > player.outfit_space:
                self.game.set_status_message("Insufficient outfit space!", ERROR_COLOR, 90)
                return

            # Find space in launchers
            total_launcher_capacity = sum(getattr(w, 'max_ammo', 0) - getattr(w, 'current_ammo', 0) for w in compatible_weapons)
            
            if self.ammo_quantity > total_launcher_capacity:
                self.game.set_status_message(f"Launchers can only hold {total_launcher_capacity} more rounds!", WARNING_COLOR, 120)
                return

            # Purchase and load ammunition
            player.credits -= total_cost
            player.used_outfit_space += total_space_needed
            
            # CRITICAL FIX: Store ammo in player inventory first
            if outfit.id in player.available_ammo_types:
                player.available_ammo_types[outfit.id].quantity += self.ammo_quantity
            else:
                player.available_ammo_types[outfit.id] = outfit.clone()
                player.available_ammo_types[outfit.id].quantity = self.ammo_quantity
            
            # Load into launchers from inventory
            remaining_ammo = self.ammo_quantity
            for weapon in compatible_weapons:
                if remaining_ammo <= 0:
                    break
                current_ammo = getattr(weapon, 'current_ammo', 0)
                max_ammo = getattr(weapon, 'max_ammo', 20)
                can_load = min(remaining_ammo, max_ammo - current_ammo)
                
                if can_load > 0:
                    weapon.current_ammo = current_ammo + can_load
                    weapon.loaded_ammo_type = outfit.id  # Track what ammo type is loaded
                    remaining_ammo -= can_load
                    # Reduce from inventory
                    player.available_ammo_types[outfit.id].quantity -= can_load
            
            self.game.set_status_message(f"✅ Purchased {self.ammo_quantity}x {outfit.name}!", SUCCESS_COLOR, 120)
            self.ammo_quantity = 1  # Reset quantity
                
        else:
            # Handle regular outfits
            outfit_cost = getattr(outfit, 'cost', 0)
            outfit_space = getattr(outfit, 'space_required', 0)
            
            # Check affordability
            if player.credits < outfit_cost:
                self.game.set_status_message("Insufficient credits!", ERROR_COLOR, 90)
                return

            # Check outfit space
            if player.used_outfit_space + outfit_space > player.outfit_space:
                self.game.set_status_message("Insufficient outfit space!", ERROR_COLOR, 90)
                return

            # Install regular outfit
            if player.install_outfit(outfit.id):
                player.credits -= outfit_cost
                self.game.set_status_message(f"✅ Purchased {outfit.name}!", SUCCESS_COLOR, 120)
                self._update_player_outfits()
                self.selected_outfit = None

    def _sell_outfit(self):
        """Sell the selected outfit."""
        outfit = self.selected_outfit
        player = self.game.player

        # Check ownership
        if outfit.id not in player.installed_outfits or player.installed_outfits[outfit.id] <= 0:
            self.game.set_status_message(f"You don't own {outfit.name}!", ERROR_COLOR, 90)
            return

        # Remove outfit
        if player.remove_outfit(outfit.id):
            sell_price = getattr(outfit, 'cost', 0) // 2
            player.credits += sell_price
            self.game.set_status_message(f"✅ Sold {outfit.name} for {sell_price} credits!", SUCCESS_COLOR, 120)
            self._update_player_outfits()

            # Clear selection if no more left
            if outfit.id not in player.installed_outfits:
                self.selected_outfit = None
