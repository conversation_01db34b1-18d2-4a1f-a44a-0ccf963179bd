# Sprite Naming Convention for Escape Velocity Py

This document outlines the naming conventions and organization for sprite assets in the Escape Velocity Py game.

## Directory Structure

```
EscapeVelocityPy/
└── assets/
    └── images/
        └── sprites/
            ├── ships/
            │   ├── small/
            │   ├── medium/
            │   ├── large/
            │   └── capital/
            ├── planets/
            ├── stations/
            ├── projectiles/
            └── effects/
```

## File Naming Conventions

### Ships

Ships are organized by size category (small, medium, large, capital) and use the following naming convention:

```
[ship_id]_spritesheet.png
[ship_id]_metadata.json
```

Where `ship_id` matches the ID used in the `ships.py` module (e.g., "scout", "light_fighter", etc.).

Examples:
- `scout_spritesheet.png`
- `scout_metadata.json`
- `heavy_fighter_spritesheet.png`
- `heavy_fighter_metadata.json`

### Planets

Planets use the following naming convention:

```
[planet_type]_[variant]_spritesheet.png
[planet_type]_[variant]_metadata.json
```

Where:
- `planet_type` is the type of planet (e.g., "terrestrial", "gas_giant", "ice", "desert", etc.)
- `variant` is an optional variant number (e.g., "1", "2", "3") for different appearances of the same type

Examples:
- `terrestrial_1_spritesheet.png`
- `gas_giant_2_spritesheet.png`
- `ice_3_spritesheet.png`

### Space Stations

Space stations use the following naming convention:

```
[station_type]_[variant]_spritesheet.png
[station_type]_[variant]_metadata.json
```

Where:
- `station_type` is the type of station (e.g., "outpost", "starport", "military", "research", etc.)
- `variant` is an optional variant number for different appearances of the same type

Examples:
- `outpost_1_spritesheet.png`
- `starport_2_spritesheet.png`
- `military_1_spritesheet.png`

### Projectiles

Projectiles use the following naming convention:

```
[projectile_type]_spritesheet.png
[projectile_type]_metadata.json
```

Examples:
- `laser_spritesheet.png`
- `missile_spritesheet.png`
- `plasma_spritesheet.png`

### Effects

Effects use the following naming convention:

```
[effect_type]_spritesheet.png
[effect_type]_metadata.json
```

Examples:
- `explosion_spritesheet.png`
- `shield_hit_spritesheet.png`
- `engine_trail_spritesheet.png`

## Spritesheet Format

Spritesheets can be organized in three layouts:

1. **Horizontal**: Frames arranged in a single row
2. **Vertical**: Frames arranged in a single column
3. **Square**: Frames arranged in a grid (rows x columns)

For rotating objects like ships, the frames should represent different rotation angles, with the first frame (index 0) typically pointing to the right (0 degrees) and subsequent frames rotating counter-clockwise.

## Metadata JSON Format

Each spritesheet should have an accompanying metadata JSON file with the following structure:

```json
{
  "ship_name": "Scout",
  "frame_count": 32,
  "sprite_size": 512,
  "sheet_width": 4096,
  "sheet_height": 4096,
  "layout": "square",
  "frames": [
    {
      "frame": 0,
      "angle": 0,
      "x": 0,
      "y": 0,
      "width": 512,
      "height": 512
    },
    {
      "frame": 1,
      "angle": 11.25,
      "x": 512,
      "y": 0,
      "width": 512,
      "height": 512
    },
    // ... more frames
  ]
}
```

For non-rotating objects like planets, the "angle" property can be omitted or set to 0 for all frames.

## Creating Spritesheets

Use the `generate_sprite_sheet_improved.py` tool to create spritesheets from individual images. This tool will automatically generate the metadata JSON file.

Example usage:
1. Prepare a base image for your ship/planet/station
2. Run the spritesheet generator tool
3. Select the appropriate number of frames (8, 16, 32, or 64 for rotating objects)
4. Choose the sprite size and layout
5. Save the output to the appropriate directory based on the entity type and size

## Recommended Sprite Sizes

- **Small Ships**: 128x128 or 256x256 pixels per frame
- **Medium Ships**: 256x256 or 512x512 pixels per frame
- **Large Ships**: 512x512 pixels per frame
- **Capital Ships**: 512x512 or 1024x1024 pixels per frame
- **Planets**: 256x256 to 1024x1024 pixels per frame (depending on importance)
- **Stations**: 256x256 to 1024x1024 pixels per frame
- **Projectiles**: 64x64 to 128x128 pixels per frame
- **Effects**: 128x128 to 512x512 pixels per frame (depending on effect size)

## Frame Count Recommendations

- **Ships**: 32 frames (for smooth rotation)
- **Planets**: 1 frame (static) or 8-16 frames (for rotation/animation)
- **Stations**: 1 frame (static) or 8-16 frames (for rotation/animation)
- **Projectiles**: 1-8 frames (depending on animation needs)
- **Effects**: 8-32 frames (depending on animation complexity)
