#!/usr/bin/env python3
"""
Simple test to create ships_data.json
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_ships_json():
    print("Creating ships_data.json...")
    
    try:
        # Import ship system - this should trigger the JSON creation
        print("Importing ships module...")
        from game_objects.ships import SHIPS
        print(f"Ships loaded: {len(SHIPS)} ships")
        
        # List first few ships to verify
        ship_ids = list(SHIPS.keys())[:5]
        for ship_id in ship_ids:
            ship = SHIPS[ship_id]
            print(f"  {ship_id}: {ship.name}")
            
        # Check if JSON file was created
        json_path = "ships_data.json"
        if os.path.exists(json_path):
            print(f"✓ {json_path} created successfully!")
            return True
        else:
            print(f"✗ {json_path} was not created")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_ships_json()
    print(f"\nResult: {'SUCCESS' if success else 'FAILED'}")
