#!/usr/bin/env python3
"""
Fix Ship Sprites - Update ships_data.json with correct sprite information
This script adds missing sprite metadata to the ships data file.
"""

import json
import os
import sys

# Add the src directory to the path so we can import game modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Update ships_data.json with correct sprite information."""
    
    # Import the sprite mapper
    try:
        from game_objects.ship_sprite_mapper import SHIP_SPRITE_MAPPER
        print("Loaded sprite mapper successfully")
    except Exception as e:
        print(f"Error loading sprite mapper: {e}")
        return
    
    # Load the current ships data
    ships_data_file = "ships_data.json"
    if not os.path.exists(ships_data_file):
        print(f"Error: {ships_data_file} not found")
        return
    
    print(f"Loading {ships_data_file}...")
    with open(ships_data_file, 'r') as f:
        ships_data = json.load(f)
    
    print(f"Found {len(ships_data)} ships in data file")
    
    # Update each ship with sprite information
    updated_count = 0
    for ship_id, ship_info in ships_data.items():
        ship_size = ship_info.get('size', 'small')
        
        # Get sprite metadata from the mapper
        sprite_metadata = SHIP_SPRITE_MAPPER.get_sprite_metadata_for_ship(ship_id, ship_size)
        
        # Check if we need to update this ship
        needs_update = False
        for key in ['game_sprite', 'shipyard_sprite', 'animation_frames', 'sprite_size']:
            if key not in ship_info or ship_info[key] != sprite_metadata[key]:
                needs_update = True
                break
        
        if needs_update:
            # Update the ship info with sprite metadata
            ship_info.update(sprite_metadata)
            updated_count += 1
            print(f"Updated {ship_id}: {sprite_metadata['game_sprite']}")
        else:
            print(f"Skipped {ship_id}: already has correct sprite info")
    
    if updated_count > 0:
        # Create a backup of the original file
        backup_file = f"{ships_data_file}.backup"
        print(f"Creating backup: {backup_file}")
        with open(backup_file, 'w') as f:
            json.dump(ships_data, f, indent=2)
        
        # Save the updated data
        print(f"Saving updated {ships_data_file}...")
        with open(ships_data_file, 'w') as f:
            json.dump(ships_data, f, indent=2)
        
        print(f"Successfully updated {updated_count} ships with sprite information")
    else:
        print("No updates needed - all ships already have sprite information")
    
    # Print a summary of sprite mappings
    print("\nSprite mapping summary:")
    for ship_id in ships_data.keys():
        sprite_name = SHIP_SPRITE_MAPPER.get_sprite_name_for_ship(ship_id)
        if sprite_name != ship_id:
            print(f"  {ship_id} -> {sprite_name}")
    
    print("\nDone!")

if __name__ == "__main__":
    main()
