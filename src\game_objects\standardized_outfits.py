"""
Standardized Outfit System for Escape Velocity Py
Complete rewrite with modular, editor-friendly design
"""

import pygame as pg
import json
import os
from enum import Enum

# Outfit Categories (Editor Tabs)
OUTFIT_CATEGORY_WEAPONS = "weapons"
OUTFIT_CATEGORY_DEFENSE = "defense"
OUTFIT_CATEGORY_ENGINES = "engines"
OUTFIT_CATEGORY_ELECTRONICS = "electronics"
OUTFIT_CATEGORY_UTILITY = "utility"
OUTFIT_CATEGORY_AMMUNITION = "ammunition"
OUTFIT_CATEGORY_SPECIAL = "special"

# Weapon Subcategories
WEAPON_ENERGY = "energy"
WEAPON_PROJECTILE = "projectile"
WEAPON_LAUNCHER = "launcher"
WEAPON_BEAM = "beam"

# Defense Subcategories
DEFENSE_SHIELDS = "shields"
DEFENSE_ARMOR = "armor"
DEFENSE_POINT_DEFENSE = "point_defense"
DEFENSE_ECM = "ecm"
DEFENSE_REACTIVE = "reactive"

# Defense Mount Types (from rules engine)
DEFENSE_MOUNT_PASSIVE = "passive"
DEFENSE_MOUNT_FIXED = "fixed"
DEFENSE_MOUNT_TURRET = "turret"
DEFENSE_MOUNT_OMNIDIRECTIONAL = "omni"

# Engine Subcategories
ENGINE_THRUSTER = "thruster"
ENGINE_STEERING = "steering"
ENGINE_AFTERBURNER = "afterburner"

# Mount Types
MOUNT_TYPE_FIXED = "fixed"
MOUNT_TYPE_TURRET = "turret"

# Damage Types
DAMAGE_TYPE_ENERGY = "energy"
DAMAGE_TYPE_KINETIC = "kinetic"
DAMAGE_TYPE_EXPLOSIVE = "explosive"
DAMAGE_TYPE_EMP = "emp"
DAMAGE_TYPE_ION = "ion"

# Projectile Behaviors
BEHAVIOR_INSTANT = "instant"           # Lasers - instant hit
BEHAVIOR_DUMBFIRE = "dumbfire"         # Flies straight
BEHAVIOR_GUIDED = "guided"             # Homes in on target
BEHAVIOR_DELAYED = "delayed"           # Coasts then activates
BEHAVIOR_PROXIMITY = "proximity"       # Explodes near target
BEHAVIOR_BEAM = "beam"                 # Continuous beam
BEHAVIOR_MINE = "mine"                 # Deploys and waits

class StandardizedOutfit:
    """Base class for all outfits in the game."""

    def __init__(self, outfit_id, name, category, subcategory=""):
        # Core Identity
        self.id = outfit_id
        self.name = name
        self.category = category
        self.subcategory = subcategory

        # Physical Properties
        self.space_required = 1
        self.mass = 0.0
        self.cost = 1000
        self.min_tech_level = 1

        # Effects System (flexible key-value pairs)
        self.effects = {}

        # Visual Assets
        self.outfitter_icon = ""
        self.description = ""
        self.sprite_assets = {}

        # Compatibility
        self.ship_size_restrictions = []  # Empty = all sizes allowed
        self.incompatible_with = []
        self.requires = []

        # State tracking
        self.cooldown_timer = 0.0

    def can_install_on_ship(self, ship):
        """Check if this outfit can be installed on the given ship."""
        if self.ship_size_restrictions and ship.size not in self.ship_size_restrictions:
            return False
        return True

    def apply_effects(self, ship):
        """Apply this outfit's effects to a ship. Override in subclasses."""
        pass

    def update(self, dt):
        """Update outfit state. Override in subclasses."""
        if self.cooldown_timer > 0:
            self.cooldown_timer -= dt

    def clone(self):
        """Create a copy of this outfit for ship installation."""
        if isinstance(self, Weapon):
            new_outfit = Weapon(self.id, self.name, self.subcategory)
        elif isinstance(self, Ammunition):
            new_outfit = Ammunition(self.id, self.name, self.ammo_type)
        elif isinstance(self, DefenseOutfit):
            new_outfit = DefenseOutfit(self.id, self.name, self.subcategory)
        elif isinstance(self, EngineOutfit):
            new_outfit = EngineOutfit(self.id, self.name, self.subcategory)
        elif isinstance(self, ElectronicsOutfit):
            new_outfit = ElectronicsOutfit(self.id, self.name, self.subcategory)
        elif isinstance(self, UtilityOutfit):
            new_outfit = UtilityOutfit(self.id, self.name, self.subcategory)
        else:
            new_outfit = StandardizedOutfit(self.id, self.name, self.category, self.subcategory)

        # Copy all attributes
        new_outfit.__dict__.update(self.__dict__)
        return new_outfit

class Weapon(StandardizedOutfit):
    """Weapon outfit with combat capabilities."""

    def __init__(self, outfit_id, name, subcategory):
        super().__init__(outfit_id, name, OUTFIT_CATEGORY_WEAPONS, subcategory)

        # Combat Properties
        self.shield_damage = 10
        self.armor_damage = 10
        self.fire_rate = 1.0            # shots per second
        self.range = 300
        self.accuracy = 1.0             # 0.0 to 1.0
        self.power_cost = 5             # energy per shot
        self.energy_usage = 1           # continuous energy drain
        self.mount_type = MOUNT_TYPE_FIXED
        self.damage_type = DAMAGE_TYPE_ENERGY

        # Projectile Properties
        self.projectile_speed = 10
        self.projectile_behavior = BEHAVIOR_INSTANT
        self.tracking_strength = 0.0    # 0.0 to 1.0 for guided weapons
        self.proximity_radius = 0
        self.delay_time = 0.0

        # Ammo System (for launchers)
        self.uses_ammo = False
        self.ammo_type = ""
        self.max_ammo = 0
        self.current_ammo = 0

        # Special Effects
        self.spread = 0.0               # Random firing angle variation
        self.burst_count = 1            # Shots per trigger pull
        self.burst_delay = 0.1          # Time between burst shots

        # Visual Effects
        self.muzzle_flash_sprite = ""
        self.projectile_sprite = ""
        self.impact_effect = ""
        self.beam_color = (255, 0, 0)   # For beam weapons
        self.beam_size = (8, 2)         # For beam weapons

    def can_fire(self):
        """Check if weapon can fire right now."""
        if self.cooldown_timer > 0:
            return False
        if self.uses_ammo and self.current_ammo <= 0:
            return False
        return True

    def fire(self):
        """Fire the weapon, returns True if successful."""
        if not self.can_fire():
            return False

        # Set cooldown
        self.cooldown_timer = 1.0 / self.fire_rate

        # Consume ammo if applicable
        if self.uses_ammo:
            self.current_ammo -= 1

        return True

    def reload_ammo(self, ammo_outfit):
        """Reload ammo from an ammunition outfit."""
        if not self.uses_ammo:
            return False
        if ammo_outfit.ammo_type != self.ammo_type:
            return False

        # Add ammo up to maximum
        ammo_to_add = min(ammo_outfit.quantity, self.max_ammo - self.current_ammo)
        self.current_ammo += ammo_to_add
        return ammo_to_add > 0

class Ammunition(StandardizedOutfit):
    """Ammunition for launcher weapons."""

    def __init__(self, outfit_id, name, ammo_type):
        super().__init__(outfit_id, name, OUTFIT_CATEGORY_AMMUNITION)

        # Ammo Properties
        self.ammo_type = ammo_type       # Legacy compatibility
        self.compatible_launchers = []   # List of launcher IDs that can use this ammo
        self.quantity = 10               # How many shots in this purchase
        self.shield_damage = 40
        self.armor_damage = 60           # Missiles typically better vs armor
        self.projectile_speed = 8
        self.range = 600                 # Maximum travel distance
        self.projectile_behavior = BEHAVIOR_DUMBFIRE
        self.tracking_strength = 0.0
        self.explosion_radius = 20
        self.damage_type = DAMAGE_TYPE_EXPLOSIVE
        self.delay_time = 0.0            # For delayed launch behavior

        # Visual
        self.projectile_sprite = ""
        self.explosion_sprite = ""
        self.trail_effect = ""

class DefenseOutfit(StandardizedOutfit):
    """Defense outfit for shields, armor, point defense, etc."""

    def __init__(self, outfit_id, name, subcategory):
        super().__init__(outfit_id, name, OUTFIT_CATEGORY_DEFENSE, subcategory)

        # Passive Defense Properties
        self.shield_boost = 0
        self.shield_recharge_boost = 0.0
        self.armor_boost = 0
        self.armor_repair_rate = 0.0     # Armor repair per second
        self.damage_reduction = 0.0      # Percentage damage reduction
        self.energy_drain = 0.0          # Energy consumption per second

        # Active Defense Properties (Point Defense)
        self.mount_type = DEFENSE_MOUNT_PASSIVE  # How the defense system is mounted
        self.defense_range = 200         # Range for point defense systems
        self.fire_rate = 2.0            # Shots per second for active defenses
        self.accuracy = 0.7             # Base accuracy for point defense
        self.power_cost = 5.0           # Power cost per shot
        self.target_types = ["projectiles"]  # What this defense can target
        self.firing_arc = 45            # Degrees for fixed defenses
        
        # Electronic Warfare Properties
        self.jam_strength = 0.0         # ECM jamming strength
        self.jam_resistance = 0.0       # ECCM resistance to jamming
        
        # Reactive Defense Properties
        self.activation_chance = 0.0    # Chance to activate on damage
        self.activation_threshold = 0   # Minimum damage to trigger
        
        # Visual/Audio Effects
        self.defense_sprite = ""
        self.activation_sound = ""
        self.impact_effect = ""

    def apply_effects(self, ship):
        """Apply defense effects to ship."""
        ship.max_shields += self.shield_boost
        ship.shields += self.shield_boost
        ship.max_armor += self.armor_boost
        ship.armor += self.armor_boost

class EngineOutfit(StandardizedOutfit):
    """Engine outfit for propulsion and maneuvering."""

    def __init__(self, outfit_id, name, subcategory):
        super().__init__(outfit_id, name, OUTFIT_CATEGORY_ENGINES, subcategory)

        # Basic Engine Properties
        self.acceleration_boost = 0.0
        self.max_speed_boost = 0.0
        self.turn_rate_boost = 0.0
        self.fuel_efficiency = 1.0       # Multiplier for fuel consumption
        self.energy_drain = 0.0
        
        # Advanced Engine Properties
        self.reverse_thrust_capable = False  # Can provide reverse thrust
        self.thrust_vectoring = False    # Omnidirectional thrust capability
        self.afterburner_thrust = 0.0   # Additional thrust when afterburning
        self.afterburner_speed = 0.0    # Additional max speed when afterburning
        self.afterburner_fuel_cost = 5.0  # Fuel consumption multiplier
        self.afterburner_power_cost = 25.0  # Power consumption per second
        
        # Engine Operational Properties
        self.heat_generation = 0.0      # Heat generated per second of use
        self.overload_threshold = 100.0  # Heat level that causes overload
        self.cooling_rate = 10.0        # Heat dissipation per second
        self.startup_time = 0.0         # Time to reach full power
        self.shutdown_time = 0.0        # Time to shut down safely
        
        # Engine Type Specific
        self.engine_type = "main"       # main, maneuvering, afterburner, jump
        self.thrust_direction = "forward"  # forward, reverse, omni
        self.gimbal_range = 0.0         # Degrees of thrust vectoring
        
        # Maintenance Properties
        self.reliability = 1.0          # Chance of successful operation
        self.wear_rate = 0.001          # Degradation per hour of use
        self.maintenance_interval = 100.0  # Hours between maintenance
        
        # Visual/Audio Effects
        self.exhaust_sprite = ""
        self.afterburner_sprite = ""
        self.engine_sound = ""
        self.exhaust_color = (100, 150, 255)

    def apply_effects(self, ship):
        """Apply engine effects to ship."""
        ship.acceleration += self.acceleration_boost
        ship.max_speed += self.max_speed_boost
        ship.turn_rate += self.turn_rate_boost

class ElectronicsOutfit(StandardizedOutfit):
    """Electronics outfit for sensors, targeting, communication, etc."""

    def __init__(self, outfit_id, name, subcategory):
        super().__init__(outfit_id, name, OUTFIT_CATEGORY_ELECTRONICS, subcategory)

        # Sensor Properties
        self.sensor_range_boost = 0.0    # Increase detection range
        self.sensor_resolution_boost = 0.0  # Better object classification
        self.sensor_sweep_rate = 1.0     # How fast sensors update
        self.stealth_detection = 0.0     # Ability to detect stealth ships
        
        # Targeting Properties
        self.targeting_boost = 0.0       # Improve weapon accuracy
        self.targeting_speed_boost = 0.0 # Faster target lock acquisition
        self.multi_target_capacity = 1   # Number of simultaneous targets
        self.target_prediction = 0.0     # Lead calculation assistance
        
        # Electronic Warfare Properties
        self.jamming_strength = 0.0      # ECM jamming power
        self.jam_resistance = 0.0        # ECCM resistance to jamming
        self.jam_range = 500.0          # Range of jamming effects
        self.frequency_agility = 0.0     # Resistance to frequency jamming
        
        # Communication Properties
        self.communication_range = 0.0   # Long-range communication
        self.encryption_strength = 0.0   # Secure communications
        self.bandwidth = 1.0            # Data transmission rate
        self.relay_capability = False    # Can relay other ship's signals
        
        # Navigation Properties
        self.navigation_accuracy = 0.0   # Improved jump accuracy
        self.hyperspace_detection = 0.0  # Detect ships in hyperspace
        self.nav_computer_speed = 1.0    # Route calculation speed
        
        # Scanning Properties
        self.scan_speed_boost = 0.0      # Faster cargo/ship scanning
        self.scan_detail_boost = 0.0     # More detailed scan results
        self.scan_penetration = 0.0      # See through shielding/armor
        self.biological_scan = False     # Detect life forms
        
        # System Properties
        self.energy_drain = 0.0          # Energy consumption per second
        self.heat_generation = 0.0       # Heat produced during operation
        self.processing_power = 1.0      # Computational capability
        self.data_storage = 100.0        # Information storage capacity
        
        # Electronic Countermeasures
        self.decoy_strength = 0.0        # Ability to generate false targets
        self.signal_analysis = 0.0       # Ability to analyze enemy signals
        self.crypto_breaking = 0.0       # Ability to break enemy encryption
        
        # Visual/Interface Properties
        self.display_quality = 1.0       # Screen resolution/clarity
        self.interface_speed = 1.0       # UI responsiveness
        self.holographic_display = False # 3D display capability

    def apply_effects(self, ship):
        """Apply electronics effects to ship."""
        # These would be applied to ship's electronics systems
        # For now, we'll store them as attributes that other systems can read
        if not hasattr(ship, 'sensor_range'):
            ship.sensor_range = 1000.0  # Base sensor range
        if not hasattr(ship, 'targeting_accuracy'):
            ship.targeting_accuracy = 1.0  # Base targeting accuracy
        if not hasattr(ship, 'jamming_resistance'):
            ship.jamming_resistance = 0.0  # Base jamming resistance

        ship.sensor_range += self.sensor_range_boost
        ship.targeting_accuracy += self.targeting_boost
        ship.jamming_resistance += self.jamming_strength

class UtilityOutfit(StandardizedOutfit):
    """Utility outfit for cargo, fuel, life support, etc."""

    def __init__(self, outfit_id, name, subcategory):
        super().__init__(outfit_id, name, OUTFIT_CATEGORY_UTILITY, subcategory)

        # Utility Properties
        self.cargo_space_boost = 0       # Additional cargo space
        self.fuel_capacity_boost = 0     # Additional fuel capacity
        self.crew_capacity_boost = 0     # Additional crew capacity
        self.life_support_boost = 0.0    # Life support efficiency
        self.energy_generation = 0.0     # Energy generation per second
        self.energy_storage_boost = 0.0  # Additional energy storage

    def apply_effects(self, ship):
        """Apply utility effects to ship."""
        # Apply cargo space boost
        if hasattr(ship, 'cargo_space'):
            ship.cargo_space += self.cargo_space_boost

        # Apply fuel capacity boost
        if not hasattr(ship, 'fuel_capacity'):
            ship.fuel_capacity = 100.0  # Base fuel capacity
        ship.fuel_capacity += self.fuel_capacity_boost

        # Apply crew capacity boost
        if not hasattr(ship, 'crew_capacity'):
            ship.crew_capacity = 1  # Base crew capacity
        ship.crew_capacity += self.crew_capacity_boost

        # Apply energy storage boost
        if not hasattr(ship, 'energy_capacity'):
            ship.energy_capacity = 100.0  # Base energy capacity
        ship.energy_capacity += self.energy_storage_boost

# Global outfit registry
OUTFITS_REGISTRY = {}

# Initialize outfits from JSON data on module load
def _initialize_outfits():
    """Load outfits from JSON data files."""
    try:
        from .outfit_data_loader import load_outfits_from_data_files
        loaded_count = load_outfits_from_data_files()
        print(f"✅ Standardized outfits: Loaded {loaded_count} outfits from JSON")
        return loaded_count
    except Exception as e:
        print(f"❌ Failed to load outfits from JSON: {e}")
        return 0

# Load outfits on module import
_initialize_outfits()

def register_outfit(outfit):
    """Register an outfit in the global registry."""
    OUTFITS_REGISTRY[outfit.id] = outfit

def get_outfit_by_id(outfit_id):
    """Get an outfit by its ID."""
    return OUTFITS_REGISTRY.get(outfit_id)

def get_outfits_by_category(category):
    """Get all outfits in a specific category."""
    return [outfit for outfit in OUTFITS_REGISTRY.values() if outfit.category == category]

def save_outfit_to_json(outfit, filepath):
    """Save an outfit to a JSON file for the editor."""
    data = {
        'id': outfit.id,
        'name': outfit.name,
        'category': outfit.category,
        'subcategory': outfit.subcategory,
        'space_required': outfit.space_required,
        'mass': outfit.mass,
        'cost': outfit.cost,
        'min_tech_level': outfit.min_tech_level,
        'effects': outfit.effects,
        'description': outfit.description
    }

    # Add category-specific properties
    if isinstance(outfit, Weapon):
        data.update({
            'damage': outfit.damage,
            'fire_rate': outfit.fire_rate,
            'range': outfit.range,
            'accuracy': outfit.accuracy,
            'energy_usage': outfit.energy_usage,
            'mount_type': outfit.mount_type,
            'damage_type': outfit.damage_type,
            'projectile_behavior': outfit.projectile_behavior,
            'uses_ammo': outfit.uses_ammo,
            'ammo_type': outfit.ammo_type,
            'max_ammo': outfit.max_ammo
        })

    with open(filepath, 'w') as f:
        json.dump(data, f, indent=2)

def load_outfit_from_json(filepath):
    """Load an outfit from a JSON file."""
    with open(filepath, 'r') as f:
        data = json.load(f)

    category = data['category']

    # Create appropriate outfit type
    if category == OUTFIT_CATEGORY_WEAPONS:
        outfit = Weapon(data['id'], data['name'], data.get('subcategory', ''))
        # Load weapon-specific properties
        for prop in ['damage', 'fire_rate', 'range', 'accuracy', 'energy_usage',
                     'mount_type', 'damage_type', 'projectile_behavior', 'uses_ammo',
                     'ammo_type', 'max_ammo']:
            if prop in data:
                setattr(outfit, prop, data[prop])

    elif category == OUTFIT_CATEGORY_DEFENSE:
        outfit = DefenseOutfit(data['id'], data['name'], data.get('subcategory', ''))

    elif category == OUTFIT_CATEGORY_ENGINES:
        outfit = EngineOutfit(data['id'], data['name'], data.get('subcategory', ''))

    elif category == OUTFIT_CATEGORY_ELECTRONICS:
        outfit = ElectronicsOutfit(data['id'], data['name'], data.get('subcategory', ''))

    elif category == OUTFIT_CATEGORY_UTILITY:
        outfit = UtilityOutfit(data['id'], data['name'], data.get('subcategory', ''))

    elif category == OUTFIT_CATEGORY_AMMUNITION:
        outfit = Ammunition(data['id'], data['name'], data.get('ammo_type', ''))

    else:
        outfit = StandardizedOutfit(data['id'], data['name'], category, data.get('subcategory', ''))

    # Load common properties
    for prop in ['space_required', 'mass', 'cost', 'min_tech_level', 'description']:
        if prop in data:
            setattr(outfit, prop, data[prop])

    return outfit
