"""
Trading system for Escape Velocity Py.
This module handles the trading mechanics between the player and planets.
"""

import pygame as pg
from game_objects.commodities import COMMODITIES, get_available_commodities

# Constants
MAX_TRADE_QUANTITY = 999
BUTTON_COLOR = (100, 100, 180)
BUTTON_HOVER_COLOR = (120, 120, 200)
BUTTON_TEXT_COLOR = (255, 255, 255)
BUTTON_DISABLED_COLOR = (80, 80, 80)

class MarketData:
    """Class to store market data for a specific system."""

    def __init__(self, system_id, faction_id, tech_level):
        """
        Initialize market data for a system.

        Args:
            system_id (str): The ID of the system
            faction_id (str): The faction controlling the system
            tech_level (int): The tech level of the system
        """
        self.system_id = system_id
        self.faction_id = faction_id
        self.tech_level = tech_level
        self.buy_prices = {}  # What the market buys from player
        self.sell_prices = {}  # What the market sells to player
        self.available_commodities = []
        self.update_prices()

    def update_prices(self):
        """Update the prices of all commodities in this market."""
        # Determine which commodities are available based on tech level
        include_illegal = self.faction_id == "pirates"  # Pirates sell illegal goods
        self.available_commodities = get_available_commodities(self.tech_level, include_illegal)

        # Calculate buy and sell prices for each commodity
        for commodity in self.available_commodities:
            commodity_id = next((cid for cid, c in COMMODITIES.items() if c.name == commodity.name), None)
            if commodity_id:
                self.buy_prices[commodity_id] = commodity.get_price_at_system(
                    self.system_id, self.faction_id, self.tech_level, is_buying=True)
                self.sell_prices[commodity_id] = commodity.get_price_at_system(
                    self.system_id, self.faction_id, self.tech_level, is_buying=False)

    def get_buy_price(self, commodity_id):
        """Get the price at which the market buys a commodity from the player."""
        return self.buy_prices.get(commodity_id, 0)

    def get_sell_price(self, commodity_id):
        """Get the price at which the market sells a commodity to the player."""
        return self.sell_prices.get(commodity_id, 0)


class TradingSystem:
    """Class to handle trading between the player and planets."""

    def __init__(self, game):
        """
        Initialize the trading system.

        Args:
            game: The main game object
        """
        self.game = game
        self.market_data = {}  # System ID -> MarketData
        self.selected_commodity = None
        self.trade_quantity = 1
        self.is_buying = True  # True if player is buying, False if selling

        # UI elements
        self.commodity_list_rect = None
        self.quantity_input_rect = None
        self.buy_button_rect = None
        self.sell_button_rect = None
        self.increase_button_rect = None
        self.decrease_button_rect = None
        self.max_button_rect = None
        self.back_button_rect = None

        # Scrolling
        self.scroll_offset = 0
        self.max_visible_items = 10
        self.scroll_up_rect = None
        self.scroll_down_rect = None

    def get_market_data(self, system_id, faction_id, tech_level):
        """
        Get market data for a specific system, creating it if it doesn't exist.

        Args:
            system_id (str): The ID of the system
            faction_id (str): The faction controlling the system
            tech_level (int): The tech level of the system

        Returns:
            MarketData: The market data for the system
        """
        if system_id not in self.market_data:
            self.market_data[system_id] = MarketData(system_id, faction_id, tech_level)
        return self.market_data[system_id]

    def show_trading_screen(self, screen, planet):
        """
        Display the trading screen for a planet.

        Args:
            screen: The pygame screen to draw on
            planet: The planet to trade with

        Returns:
            str: The next state to transition to
        """
        # Get the current system's market data
        system_id = self.game.current_system_id
        market = self.get_market_data(system_id, planet.faction, planet.tech_level)

        # Set up UI elements
        self._setup_ui(screen)

        # Main trading loop
        running = True
        while running and self.game.running:
            screen.fill((30, 30, 50))  # Dark blue background

            # Draw title
            self.game.draw_text(f"Trading at {planet.name}", 48, (255, 255, 255),
                               screen.get_width() / 2, 50, align="center")

            # Draw player credits
            self.game.draw_text(f"Credits: {self.game.player.credits}", 24, (255, 255, 0),
                               screen.get_width() / 2, 100, align="center")

            # Draw cargo space
            cargo_used = sum(self.game.player.cargo.values())
            self.game.draw_text(f"Cargo: {cargo_used}/{self.game.player.cargo_space} tons", 24, (255, 255, 255),
                               screen.get_width() / 2, 130, align="center")

            # Draw commodity list
            self._draw_commodity_list(screen, market)

            # Draw trading interface
            self._draw_trading_interface(screen, market)

            # Handle events
            for event in pg.event.get():
                if event.type == pg.QUIT:
                    self.game.running = False
                    running = False

                elif event.type == pg.KEYDOWN:
                    if event.key == pg.K_ESCAPE:
                        running = False

                elif event.type == pg.MOUSEBUTTONDOWN:
                    mouse_pos = pg.mouse.get_pos()

                    # Check if a commodity was clicked
                    clicked_commodity = self._handle_commodity_click(mouse_pos, market)
                    if clicked_commodity:
                        self.selected_commodity = clicked_commodity
                        self.trade_quantity = 1

                    # Check if a button was clicked
                    self._handle_button_click(mouse_pos, market)

                    # Check scroll buttons
                    if self.scroll_up_rect and self.scroll_up_rect.collidepoint(mouse_pos):
                        self.scroll_offset = max(0, self.scroll_offset - 1)
                    elif self.scroll_down_rect and self.scroll_down_rect.collidepoint(mouse_pos):
                        max_offset = max(0, len(market.available_commodities) - self.max_visible_items)
                        self.scroll_offset = min(max_offset, self.scroll_offset + 1)

            pg.display.flip()
            self.game.clock.tick(60)  # Use a default FPS of 60

        return "DOCKED"

    def _setup_ui(self, screen):
        """Set up UI elements for the trading screen."""
        width, height = screen.get_width(), screen.get_height()

        # Commodity list (left side)
        list_width = width * 0.4
        self.commodity_list_rect = pg.Rect(50, 180, list_width, height - 250)

        # Scroll buttons
        button_size = 30
        self.scroll_up_rect = pg.Rect(50 + list_width - button_size, 180, button_size, button_size)
        self.scroll_down_rect = pg.Rect(50 + list_width - button_size, 180 + self.commodity_list_rect.height - button_size,
                                       button_size, button_size)

        # Trading interface (right side)
        interface_x = 50 + list_width + 50
        interface_width = width - interface_x - 50

        # Quantity input
        self.quantity_input_rect = pg.Rect(interface_x + interface_width/2 - 50, 300, 100, 40)

        # Quantity adjustment buttons
        button_width = 40
        self.decrease_button_rect = pg.Rect(interface_x + interface_width/2 - 50 - button_width - 10, 300, button_width, 40)
        self.increase_button_rect = pg.Rect(interface_x + interface_width/2 + 50 + 10, 300, button_width, 40)
        self.max_button_rect = pg.Rect(interface_x + interface_width/2 + 50 + button_width + 20, 300, 60, 40)

        # Buy/Sell buttons
        self.buy_button_rect = pg.Rect(interface_x + interface_width/4 - 50, 400, 100, 50)
        self.sell_button_rect = pg.Rect(interface_x + 3*interface_width/4 - 50, 400, 100, 50)

        # Back button
        self.back_button_rect = pg.Rect(width - 150, height - 80, 100, 50)

    def _draw_commodity_list(self, screen, market):
        """Draw the list of available commodities."""
        # Draw list background
        pg.draw.rect(screen, (50, 50, 70), self.commodity_list_rect)
        pg.draw.rect(screen, (100, 100, 120), self.commodity_list_rect, 2)

        # Draw column headers
        header_y = self.commodity_list_rect.top - 30
        self.game.draw_text("Commodity", 20, (200, 200, 200),
                           self.commodity_list_rect.left + 10, header_y)
        self.game.draw_text("Buy", 20, (200, 200, 200),
                           self.commodity_list_rect.left + self.commodity_list_rect.width * 0.6, header_y)
        self.game.draw_text("Sell", 20, (200, 200, 200),
                           self.commodity_list_rect.left + self.commodity_list_rect.width * 0.8, header_y)

        # Draw scroll buttons
        pg.draw.rect(screen, BUTTON_COLOR, self.scroll_up_rect)
        pg.draw.rect(screen, BUTTON_COLOR, self.scroll_down_rect)
        self.game.draw_text("▲", 20, BUTTON_TEXT_COLOR,
                           self.scroll_up_rect.centerx, self.scroll_up_rect.centery, align="center")
        self.game.draw_text("▼", 20, BUTTON_TEXT_COLOR,
                           self.scroll_down_rect.centerx, self.scroll_down_rect.centery, align="center")

        # Draw commodities
        visible_commodities = market.available_commodities[self.scroll_offset:self.scroll_offset + self.max_visible_items]
        for i, commodity in enumerate(visible_commodities):
            commodity_id = next((cid for cid, c in COMMODITIES.items() if c.name == commodity.name), None)
            if not commodity_id:
                continue

            y = self.commodity_list_rect.top + 10 + i * 30

            # Highlight selected commodity
            if self.selected_commodity == commodity_id:
                highlight_rect = pg.Rect(self.commodity_list_rect.left, y - 5,
                                        self.commodity_list_rect.width, 30)
                pg.draw.rect(screen, (70, 70, 100), highlight_rect)

            # Draw commodity name
            name_color = (255, 255, 255)
            if commodity.illegal:
                name_color = (255, 100, 100)  # Red for illegal goods
            self.game.draw_text(commodity.name, 18, name_color,
                               self.commodity_list_rect.left + 10, y)

            # Draw buy/sell prices
            buy_price = market.get_buy_price(commodity_id)
            sell_price = market.get_sell_price(commodity_id)

            self.game.draw_text(str(buy_price), 18, (150, 255, 150),
                               self.commodity_list_rect.left + self.commodity_list_rect.width * 0.6, y)
            self.game.draw_text(str(sell_price), 18, (255, 150, 150),
                               self.commodity_list_rect.left + self.commodity_list_rect.width * 0.8, y)

    def _draw_trading_interface(self, screen, market):
        """Draw the trading interface (right side)."""
        if not self.selected_commodity:
            self.game.draw_text("Select a commodity to trade", 24, (200, 200, 200),
                               self.commodity_list_rect.right + 150, 250, align="center")
            return

        # Get selected commodity info
        commodity = COMMODITIES.get(self.selected_commodity)
        if not commodity:
            return

        # Draw commodity info
        self.game.draw_text(f"Selected: {commodity.name}", 24, (255, 255, 255),
                           self.commodity_list_rect.right + 150, 200, align="center")

        # Draw rarity and category
        rarity_color = {
            "common": (200, 200, 200),
            "uncommon": (150, 255, 150),
            "rare": (100, 100, 255),
            "very_rare": (255, 100, 255),
            "exotic": (255, 215, 0)
        }.get(commodity.rarity, (255, 255, 255))

        self.game.draw_text(f"Rarity: {commodity.rarity.capitalize()}", 18, rarity_color,
                           self.commodity_list_rect.right + 150, 230, align="center")
        self.game.draw_text(f"Category: {commodity.category.capitalize()}", 18, (200, 200, 200),
                           self.commodity_list_rect.right + 150, 255, align="center")

        # Draw quantity selector
        pg.draw.rect(screen, (70, 70, 90), self.quantity_input_rect)
        pg.draw.rect(screen, (100, 100, 120), self.quantity_input_rect, 2)
        self.game.draw_text(str(self.trade_quantity), 24, (255, 255, 255),
                           self.quantity_input_rect.centerx, self.quantity_input_rect.centery, align="center")

        # Draw quantity adjustment buttons
        pg.draw.rect(screen, BUTTON_COLOR, self.decrease_button_rect)
        pg.draw.rect(screen, BUTTON_COLOR, self.increase_button_rect)
        pg.draw.rect(screen, BUTTON_COLOR, self.max_button_rect)

        self.game.draw_text("-", 24, BUTTON_TEXT_COLOR,
                           self.decrease_button_rect.centerx, self.decrease_button_rect.centery, align="center")
        self.game.draw_text("+", 24, BUTTON_TEXT_COLOR,
                           self.increase_button_rect.centerx, self.increase_button_rect.centery, align="center")
        self.game.draw_text("Max", 18, BUTTON_TEXT_COLOR,
                           self.max_button_rect.centerx, self.max_button_rect.centery, align="center")

        # Draw buy/sell buttons
        buy_price = market.get_sell_price(self.selected_commodity)
        sell_price = market.get_buy_price(self.selected_commodity)
        total_buy_cost = buy_price * self.trade_quantity
        total_sell_value = sell_price * self.trade_quantity

        # Determine if buttons should be enabled
        can_buy = (self.game.player.credits >= total_buy_cost and
                  sum(self.game.player.cargo.values()) + self.trade_quantity <= self.game.player.cargo_space)
        can_sell = self.game.player.cargo.get(self.selected_commodity, 0) >= self.trade_quantity

        # Draw buy button
        buy_color = BUTTON_COLOR if can_buy else BUTTON_DISABLED_COLOR
        pg.draw.rect(screen, buy_color, self.buy_button_rect)
        self.game.draw_text("Buy", 24, BUTTON_TEXT_COLOR,
                           self.buy_button_rect.centerx, self.buy_button_rect.centery, align="center")
        self.game.draw_text(f"Cost: {total_buy_cost}", 18, (255, 255, 255),
                           self.buy_button_rect.centerx, self.buy_button_rect.bottom + 15, align="center")

        # Draw sell button
        sell_color = BUTTON_COLOR if can_sell else BUTTON_DISABLED_COLOR
        pg.draw.rect(screen, sell_color, self.sell_button_rect)
        self.game.draw_text("Sell", 24, BUTTON_TEXT_COLOR,
                           self.sell_button_rect.centerx, self.sell_button_rect.centery, align="center")
        self.game.draw_text(f"Value: {total_sell_value}", 18, (255, 255, 255),
                           self.sell_button_rect.centerx, self.sell_button_rect.bottom + 15, align="center")

        # Draw back button
        pg.draw.rect(screen, BUTTON_COLOR, self.back_button_rect)
        self.game.draw_text("Back", 24, BUTTON_TEXT_COLOR,
                           self.back_button_rect.centerx, self.back_button_rect.centery, align="center")

        # Draw commodity description
        description_rect = pg.Rect(self.commodity_list_rect.right + 50, 500,
                                  screen.get_width() - self.commodity_list_rect.right - 100, 100)
        pg.draw.rect(screen, (50, 50, 70), description_rect)
        pg.draw.rect(screen, (100, 100, 120), description_rect, 2)

        # Wrap text to fit in the description box
        font = pg.font.Font(self.game.font_name, 16)
        words = commodity.description.split(' ')
        lines = []
        current_line = words[0]

        for word in words[1:]:
            test_line = current_line + " " + word
            if font.size(test_line)[0] < description_rect.width - 20:
                current_line = test_line
            else:
                lines.append(current_line)
                current_line = word
        lines.append(current_line)

        for i, line in enumerate(lines):
            self.game.draw_text(line, 16, (200, 200, 200),
                               description_rect.left + 10, description_rect.top + 10 + i * 20)

    def _handle_commodity_click(self, mouse_pos, market):
        """Handle clicks on the commodity list."""
        if not self.commodity_list_rect.collidepoint(mouse_pos):
            return None

        # Calculate which commodity was clicked
        y_offset = mouse_pos[1] - self.commodity_list_rect.top - 10
        index = self.scroll_offset + y_offset // 30

        if 0 <= index < len(market.available_commodities):
            commodity = market.available_commodities[index]
            commodity_id = next((cid for cid, c in COMMODITIES.items() if c.name == commodity.name), None)
            return commodity_id

        return None

    def _handle_button_click(self, mouse_pos, market):
        """Handle clicks on the trading interface buttons."""
        if not self.selected_commodity:
            return

        # Quantity adjustment buttons
        if self.decrease_button_rect.collidepoint(mouse_pos):
            self.trade_quantity = max(1, self.trade_quantity - 1)
        elif self.increase_button_rect.collidepoint(mouse_pos):
            self.trade_quantity = min(MAX_TRADE_QUANTITY, self.trade_quantity + 1)
        elif self.max_button_rect.collidepoint(mouse_pos):
            if self.is_buying:
                # Max we can buy based on credits and cargo space
                max_by_credits = self.game.player.credits // market.get_sell_price(self.selected_commodity)
                max_by_space = self.game.player.cargo_space - sum(self.game.player.cargo.values())
                self.trade_quantity = min(MAX_TRADE_QUANTITY, max_by_credits, max_by_space)
            else:
                # Max we can sell based on inventory
                self.trade_quantity = min(MAX_TRADE_QUANTITY,
                                         self.game.player.cargo.get(self.selected_commodity, 0))

        # Buy button
        elif self.buy_button_rect.collidepoint(mouse_pos):
            self.is_buying = True
            self._execute_trade(market, is_buying=True)

        # Sell button
        elif self.sell_button_rect.collidepoint(mouse_pos):
            self.is_buying = False
            self._execute_trade(market, is_buying=False)

        # Back button
        elif self.back_button_rect.collidepoint(mouse_pos):
            return "DOCKED"

    def _execute_trade(self, market, is_buying):
        """Execute a trade transaction."""
        if not self.selected_commodity:
            return

        if is_buying:
            # Player is buying from the market
            price = market.get_sell_price(self.selected_commodity)
            total_cost = price * self.trade_quantity

            # Check if player has enough credits and cargo space
            if self.game.player.credits < total_cost:
                self.game.set_status_message("Not enough credits!", (255, 100, 100), 120)
                return

            if sum(self.game.player.cargo.values()) + self.trade_quantity > self.game.player.cargo_space:
                self.game.set_status_message("Not enough cargo space!", (255, 100, 100), 120)
                return

            # Execute the purchase
            self.game.player.credits -= total_cost
            if self.selected_commodity in self.game.player.cargo:
                self.game.player.cargo[self.selected_commodity] += self.trade_quantity
            else:
                self.game.player.cargo[self.selected_commodity] = self.trade_quantity

            self.game.set_status_message(f"Bought {self.trade_quantity} {COMMODITIES[self.selected_commodity].name} for {total_cost} credits",
                                       (100, 255, 100), 120)
        else:
            # Player is selling to the market
            price = market.get_buy_price(self.selected_commodity)
            total_value = price * self.trade_quantity

            # Check if player has enough of the commodity
            if self.game.player.cargo.get(self.selected_commodity, 0) < self.trade_quantity:
                self.game.set_status_message("Not enough of this commodity!", (255, 100, 100), 120)
                return

            # Execute the sale
            self.game.player.credits += total_value
            self.game.player.cargo[self.selected_commodity] -= self.trade_quantity

            # Remove the commodity from cargo if quantity is 0
            if self.game.player.cargo[self.selected_commodity] <= 0:
                del self.game.player.cargo[self.selected_commodity]

            self.game.set_status_message(f"Sold {self.trade_quantity} {COMMODITIES[self.selected_commodity].name} for {total_value} credits",
                                       (100, 255, 100), 120)
