"""
Fixed planet.py with correct planet type mapping and available variants
"""
import pygame as pg
import random
import os
from game_objects.sprite_manager import load_sprite

# --- Constants ---
PLANET_COLOR = (0, 0, 255)  # Blue (fallback color)
PLANET_RADIUS_MIN = 30
PLANET_RADIUS_MAX = 80
PLANET_NAMES = ["Terra Prime", "Mars Alpha", "Jupiter Station", "Xylos", "New Earth", "Kepler-186f", "Gliese 581g"]
FACTIONS = ["Federation", "Klingon Empire", "Romulan Star Empire", "Independent"] # Example factions

# Planet types for sprite selection - UPDATED to match available assets
PLANET_TYPES = [
    "terrestrial",  # Earth-like planets
    "barren",       # Rocky, lifeless planets (replaces "desert")
    "ice",          # Frozen planets
    "toxic"         # Poisonous atmosphere planets
]

# Available variants for each planet type - UPDATED to match actual files
PLANET_VARIANTS = {
    "terrestrial": [1],           # Only terrestrial_1 available
    "barren": [1, 2, 3],         # barren_1, barren_2, barren_3 available
    "ice": [1, 2],               # ice_1, ice_2 available (note: ice_1 has naming issue)
    "toxic": [1, 2, 3]           # toxic_1, toxic_2, toxic_3 available
}

# For simplicity, reputation is a number. Could be more complex later.
# e.g., -100 (hostile) to +100 (allied), 0 (neutral)
DEFAULT_PLAYER_REPUTATION = 0

# Tech Levels:
# 0: Uninhabited, not landable (or special conditions for landing)
# 1: Outpost (maybe only refuel, basic commodities, no shipyard/outfitter)
# 2: Basic Settlement (limited commodities, basic outfitter, small ships)
# 3: Developed Colony (good commodities, decent outfitter/shipyard)
# 4: Advanced System (best ships/outfits, wide commodities)
# 5: Hi-Tech Hub (special tech, unique items)
TECH_LEVEL_MIN = 0
TECH_LEVEL_MAX = 5


class Planet(pg.sprite.Sprite):
    def __init__(self, game, world_x, world_y, name=None, faction=None, tech_level=None, planet_type=None, variant=None):
        super().__init__()
        self.game = game
        if name is None:
            self.name = random.choice(PLANET_NAMES)
        else:
            self.name = name

        if faction is None:
            self.faction = random.choice(FACTIONS)
        else:
            self.faction = faction

        if tech_level is None:
            self.tech_level = random.randint(TECH_LEVEL_MIN, TECH_LEVEL_MAX)
        else:
            self.tech_level = tech_level

        # Randomly select a planet type if not specified
        if planet_type is None:
            self.planet_type = random.choice(PLANET_TYPES)
        else:
            self.planet_type = planet_type

        # Randomly select a variant from available ones for this type
        if variant is None:
            available_variants = PLANET_VARIANTS.get(self.planet_type, [1])
            self.variant = random.choice(available_variants)
        else:
            # Validate the requested variant exists
            available_variants = PLANET_VARIANTS.get(self.planet_type, [1])
            if variant in available_variants:
                self.variant = variant
            else:
                print(f"Warning: Variant {variant} not available for {self.planet_type}, using random variant")
                self.variant = random.choice(available_variants)

        # Placeholder: all planets start with neutral reputation towards player
        self.reputation_with_player = DEFAULT_PLAYER_REPUTATION

        self.radius = random.randint(PLANET_RADIUS_MIN, PLANET_RADIUS_MAX)

        # Make unlandable if tech level is 0 (unless specific override later)
        self.is_landable = self.tech_level > 0

        # Try to load a spritesheet for this planet type
        sprite_id = f"{self.planet_type}_{self.variant}"
        print(f"Attempting to load spritesheet for planet: {sprite_id}")
        self.spritesheet = load_sprite("planet", sprite_id)

        try:
            if self.spritesheet and self.spritesheet.image:
                print(f"Using spritesheet for planet: {sprite_id}")
                # Use the first frame from the spritesheet
                self.image = self.spritesheet.get_frame(0)
                if self.image:
                    # Resize the image to match the desired radius
                    current_size = max(self.image.get_width(), self.image.get_height())
                    scale_factor = (self.radius * 2) / current_size
                    new_width = int(self.image.get_width() * scale_factor)
                    new_height = int(self.image.get_height() * scale_factor)
                    self.image = pg.transform.scale(self.image, (new_width, new_height))
                else:
                    print(f"Failed to get frame 0 from spritesheet for planet: {sprite_id}")
                    self._create_default_planet_image()
            else:
                # Fall back to a generated circle if no sprite is available
                print(f"No spritesheet found for planet: {sprite_id}, using default circle")
                self._create_default_planet_image()
        except Exception as e:
            print(f"Error loading planet sprite: {e}")
            self._create_default_planet_image()

        self.rect = self.image.get_rect()

        # Position in world coordinates
        self.pos = pg.math.Vector2(world_x, world_y)
        self.rect.center = self.pos

        # Animation properties
        self.rotation_speed = random.uniform(0.05, 0.2) if self.spritesheet else 0
        self.current_frame = 0
        self.frame_timer = 0

    def _create_default_planet_image(self):
        """Create a default planet image based on planet type."""
        diameter = self.radius * 2
        self.image = pg.Surface([diameter, diameter], pg.SRCALPHA)  # SRCALPHA for transparency
        self.image.fill((0, 0, 0, 0))  # Transparent background

        # Choose color based on planet type
        color = PLANET_COLOR  # Default blue
        if self.planet_type == "barren":  # Changed from "desert" to "barren"
            color = (210, 180, 140)  # Tan
        elif self.planet_type == "ice":
            color = (200, 230, 255)  # Light blue
        elif self.planet_type == "gas_giant":
            color = (230, 180, 80)   # Yellow-orange
        elif self.planet_type == "volcanic":
            color = (200, 0, 0)      # Red
        elif self.planet_type == "toxic":
            color = (150, 230, 0)    # Toxic green
        elif self.planet_type == "ocean":
            color = (0, 100, 200)    # Deep blue
        elif self.planet_type == "terrestrial":
            color = (0, 150, 0)      # Green

        # Draw the planet circle
        pg.draw.circle(self.image, color, (self.radius, self.radius), self.radius)

        # Add some visual interest based on planet type
        if self.planet_type == "terrestrial":
            # Add some "continents"
            for _ in range(3):
                continent_color = (0, 100, 0)  # Dark green
                pos_x = random.randint(self.radius // 2, self.radius * 3 // 2)
                pos_y = random.randint(self.radius // 2, self.radius * 3 // 2)
                size = random.randint(self.radius // 4, self.radius // 2)
                pg.draw.circle(self.image, continent_color, (pos_x, pos_y), size)
        elif self.planet_type == "ice":
            # Add some "ice caps"
            cap_color = (255, 255, 255)  # White
            pg.draw.circle(self.image, cap_color, (self.radius, self.radius // 3), self.radius // 2)
            pg.draw.circle(self.image, cap_color, (self.radius, self.radius * 5 // 3), self.radius // 2)
        elif self.planet_type == "barren":  # Updated from gas_giant
            # Add some "craters"
            for i in range(2):
                crater_color = (max(color[0] - 40, 0), max(color[1] - 40, 0), max(color[2] - 40, 0))
                crater_x = random.randint(self.radius // 2, self.radius * 3 // 2)
                crater_y = random.randint(self.radius // 2, self.radius * 3 // 2)
                crater_size = random.randint(self.radius // 6, self.radius // 4)
                pg.draw.circle(self.image, crater_color, (crater_x, crater_y), crater_size)

    def update(self):
        # Update position (planets are stationary for now)
        self.rect.center = self.pos

        # Handle animation if we have a spritesheet
        try:
            if self.spritesheet and self.spritesheet.frame_count > 1:
                self.frame_timer += 1

                # Update frame every few ticks based on rotation speed
                if self.frame_timer >= 60 / self.rotation_speed:
                    self.frame_timer = 0
                    self.current_frame = (self.current_frame + 1) % self.spritesheet.frame_count

                    # Update the image to the new frame
                    new_frame = self.spritesheet.get_frame(self.current_frame)
                    if new_frame:
                        # Maintain the current size
                        current_size = max(self.rect.width, self.rect.height)
                        new_size = max(new_frame.get_width(), new_frame.get_height())
                        if new_size != current_size:
                            scale_factor = current_size / new_size
                            new_width = int(new_frame.get_width() * scale_factor)
                            new_height = int(new_frame.get_height() * scale_factor)
                            self.image = pg.transform.scale(new_frame, (new_width, new_height))
                        else:
                            self.image = new_frame
        except Exception as e:
            print(f"Error updating planet animation: {e}")
