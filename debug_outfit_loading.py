"""
Debug script to test outfit data loading
"""

import sys
import os
import json

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("=== OUTFIT LOADING DEBUG ===")

# Test 1: Check if JSON file exists and is readable
outfits_json_path = os.path.join(os.path.dirname(__file__), 'outfits_data.json')
print(f"1. Checking for outfits_data.json at: {outfits_json_path}")
print(f"   File exists: {os.path.exists(outfits_json_path)}")

if os.path.exists(outfits_json_path):
    try:
        with open(outfits_json_path, 'r') as f:
            data = json.load(f)
        print(f"   File readable: Yes, contains {len(data)} outfits")
        print(f"   First few outfits: {list(data.keys())[:5]}")
    except Exception as e:
        print(f"   File readable: No, error: {e}")

# Test 2: Try importing the standardized outfits module
print("\n2. Testing standardized_outfits import...")
try:
    from game_objects.standardized_outfits import OUTFITS_REGISTRY, get_outfit_by_id
    print(f"   Import successful: Yes")
    print(f"   OUTFITS_REGISTRY size: {len(OUTFITS_REGISTRY)}")
    print(f"   Available outfits: {list(OUTFITS_REGISTRY.keys())}")
except Exception as e:
    print(f"   Import failed: {e}")
    import traceback
    traceback.print_exc()

# Test 3: Try importing the data loader directly
print("\n3. Testing outfit_data_loader import...")
try:
    from game_objects.outfit_data_loader import load_outfits_from_data_files
    print(f"   Data loader import: Success")
    
    # Try loading manually
    print("   Attempting manual load...")
    result = load_outfits_from_data_files()
    print(f"   Manual load result: {result} outfits loaded")
    
    # Check registry again
    from game_objects.standardized_outfits import OUTFITS_REGISTRY
    print(f"   Registry size after manual load: {len(OUTFITS_REGISTRY)}")
    
except Exception as e:
    print(f"   Data loader import failed: {e}")
    import traceback
    traceback.print_exc()

# Test 4: Try importing example_outfits
print("\n4. Testing example_outfits import...")
try:
    from game_objects.example_outfits import create_example_outfits
    print(f"   Example outfits import: Success")
    
    # Check if example outfits would help
    from game_objects.standardized_outfits import OUTFITS_REGISTRY
    print(f"   Registry size before example creation: {len(OUTFITS_REGISTRY)}")
    
except Exception as e:
    print(f"   Example outfits import failed: {e}")
    import traceback
    traceback.print_exc()

print("\n=== DEBUG COMPLETE ===")
