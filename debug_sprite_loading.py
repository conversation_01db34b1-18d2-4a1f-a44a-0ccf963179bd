#!/usr/bin/env python3
"""
Debug script to test sprite loading and examine actual sprite dimensions
"""
import pygame as pg
import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from game_objects.sprite_manager import load_sprite, SpriteSheet

def debug_sprite_loading():
    """Test sprite loading and examine the results"""
    
    # Initialize pygame
    pg.init()
    pg.display.set_mode((800, 600))  # Small window for testing
    
    print("=== SPRITE LOADING DEBUG ===")
    
    # Test loading the scout ship
    print("\n1. Testing scout ship loading:")
    scout_sprite = load_sprite("ship", "scout", "small")
    
    if scout_sprite:
        print(f"✓ Scout sprite loaded successfully")
        print(f"  - Image path: {scout_sprite.image_path}")
        print(f"  - Metadata path: {scout_sprite.metadata_path}")
        print(f"  - Frame count: {scout_sprite.frame_count}")
        print(f"  - Sprite size: {scout_sprite.sprite_size}")
        print(f"  - Sheet dimensions: {scout_sprite.sheet_width}x{scout_sprite.sheet_height}")
        
        if scout_sprite.image:
            actual_size = scout_sprite.image.get_size()
            print(f"  - Actual image size: {actual_size}")
            
            # Test getting a frame
            frame_0 = scout_sprite.get_frame(0)
            if frame_0:
                frame_size = frame_0.get_size()
                print(f"  - Frame 0 size: {frame_size}")
            else:
                print("  ✗ Could not get frame 0")
                
            # Test getting frame by angle
            frame_90 = scout_sprite.get_frame_by_angle(90)
            if frame_90:
                frame_90_size = frame_90.get_size()
                print(f"  - Frame at 90° size: {frame_90_size}")
            else:
                print("  ✗ Could not get frame at 90°")
        else:
            print("  ✗ Image not loaded")
    else:
        print("✗ Scout sprite failed to load")
    
    # Test loading a non-existent planet
    print("\n2. Testing non-existent planet loading:")
    desert_sprite = load_sprite("planet", "desert_2")
    if desert_sprite:
        print(f"✓ Desert_2 sprite loaded: {desert_sprite.image_path}")
    else:
        print("✗ Desert_2 sprite not found (expected)")
    
    # Test loading an existing planet
    print("\n3. Testing existing planet loading:")
    terrestrial_sprite = load_sprite("planet", "terrestrial_1")
    if terrestrial_sprite:
        print(f"✓ Terrestrial_1 sprite loaded: {terrestrial_sprite.image_path}")
        if terrestrial_sprite.image:
            actual_size = terrestrial_sprite.image.get_size()
            print(f"  - Actual image size: {actual_size}")
    else:
        print("✗ Terrestrial_1 sprite not found")
    
    # List available planet sprites
    print("\n4. Available planet sprites:")
    planets_dir = os.path.join("assets", "images", "sprites", "planets")
    if os.path.exists(planets_dir):
        planet_files = [f for f in os.listdir(planets_dir) if f.endswith('_spritesheet.png')]
        for planet_file in sorted(planet_files):
            planet_name = planet_file.replace('_spritesheet.png', '')
            print(f"  - {planet_name}")
    else:
        print(f"  ✗ Planets directory not found: {planets_dir}")
    
    print("\n=== DEBUG COMPLETE ===")

if __name__ == "__main__":
    debug_sprite_loading()
