# Escape Velocity Py - Modular Outfit System

## Overview

The Escape Velocity Py game uses a **data-driven, modular outfit system** that separates the game engine (rules) from the content (data). This allows players to create unlimited combinations of weapons, ammunition, and other outfits without modifying the core game code.

## System Architecture

### Three-Layer Architecture

```
┌─────────────────────┐
│   GAME ENGINE       │  ← Defines HOW things work (rules)
│   (Rules & Logic)   │
├─────────────────────┤
│   JSON DATA         │  ← Defines WHAT exists (content)
│   (Content Files)   │
├─────────────────────┤
│   EDITOR            │  ← Allows creation of new content
│   (Creation Tools)  │
└─────────────────────┘
```

### 1. Game Engine (Rules Layer)
Located in: `src/game_objects/`

**What it does:**
- Defines projectile physics and behaviors
- Handles damage calculations
- Manages weapon mount types (fixed, turret, guided)
- Controls firing rates and energy consumption
- Loads and interprets JSON data

**Key files:**
- `standardized_outfits.py` - Outfit class definitions
- `outfit_data_loader.py` - JSON loading system
- `projectiles.py` - Projectile physics
- `player.py` - Weapon firing logic

### 2. JSON Data (Content Layer)
Located in: `outfits_data.json`, `ships_data.json`

**What it contains:**
- Outfit specifications (weapons, ammo, defense, etc.)
- Ship configurations
- **Compatibility relationships** between launchers and ammunition

**Key concept: Launcher/Ammo Compatibility**
```json
{
  \"heavy_missile_rack\": {
    \"category\": \"weapons\",
    \"uses_ammo\": true,
    \"max_ammo\": 20
  },
  \"plasma_torpedo\": {
    \"category\": \"ammunition\",
    \"compatible_launchers\": [\"heavy_missile_rack\", \"torpedo_tube\"]
  }
}
```

### 3. Editor (Creation Layer)
Located in: `editor_modules/`

**What it does:**
- Provides GUI for creating/editing outfits
- Saves data to JSON files
- Allows setting compatibility relationships
- No game logic - pure data manipulation

## How the Launcher/Ammo System Works

### Core Concept
- **Launchers** are weapons that fire projectiles
- **Ammunition** defines the projectile properties
- **Compatibility** is defined by the ammo's `compatible_launchers` array

### Data Flow
1. **Creation**: Player uses editor to create launcher and ammo
2. **Compatibility**: Player assigns ammo to compatible launchers
3. **Runtime**: Game engine loads launcher → checks loaded ammo → uses ammo specs for projectile

### Example Workflow

#### Step 1: Create a Launcher
```json
{
  \"torpedo_tube\": {
    \"name\": \"Heavy Torpedo Tube\",
    \"category\": \"weapons\",
    \"uses_ammo\": true,
    \"max_ammo\": 8,
    \"fire_rate\": 0.5,
    \"mount_type\": \"fixed\",
    \"power_cost\": 15.0
  }
}
```

#### Step 2: Create Ammunition Types
```json
{
  \"plasma_torpedo\": {
    \"name\": \"Plasma Torpedo\",
    \"category\": \"ammunition\",
    \"shield_damage\": 150,
    \"armor_damage\": 100,
    \"projectile_speed\": 200,
    \"tracking_strength\": 0.8,
    \"compatible_launchers\": [\"torpedo_tube\"]
  },
  \"emp_torpedo\": {
    \"name\": \"EMP Torpedo\",
    \"category\": \"ammunition\",
    \"shield_damage\": 250,
    \"armor_damage\": 10,
    \"projectile_speed\": 180,
    \"tracking_strength\": 0.9,
    \"compatible_launchers\": [\"torpedo_tube\"]
  }
}
```

#### Step 3: Runtime Execution
```
Player fires torpedo_tube → Game checks loaded ammo type → 
If plasma_torpedo: Creates projectile with 150 shield damage, speed 200
If emp_torpedo: Creates projectile with 250 shield damage, speed 180
```

## Outfit Categories

### 1. Weapons
**Direct Fire Weapons:**
- `uses_ammo: false`
- Weapon defines damage, range, behavior
- Examples: lasers, beam weapons, railguns

**Launchers:**
- `uses_ammo: true`
- Ammo defines damage, range, behavior
- Examples: missile racks, torpedo tubes, rocket launchers

### 2. Ammunition
- Must specify `compatible_launchers` array
- Defines projectile properties: damage, speed, tracking, sprite
- Examples: missiles, torpedoes, rockets, shells

### 3. Defense
- Shield boosters, armor plating, damage reduction
- Applied when outfit is installed on ship

### 4. Engines
- Acceleration, speed, turn rate modifications
- Fuel efficiency improvements

### 5. Electronics
- Sensors, targeting systems, jammers
- Communication equipment

### 6. Utility
- Cargo expansion, fuel tanks, crew quarters
- Power generators, life support

## JSON Field Reference

### Weapon Fields
```json
{
  \"mount_type\": \"fixed|turret|guided\",
  \"fire_rate\": 2.0,
  \"range\": 800,
  \"energy_usage\": 10.0,
  \"uses_ammo\": true,
  \"max_ammo\": 20,
  \"power_cost\": 15.0,
  \"shield_damage\": 50,    // Only if uses_ammo = false
  \"armor_damage\": 50      // Only if uses_ammo = false
}
```

### Ammunition Fields
```json
{
  \"quantity\": 10,
  \"shield_damage\": 80,
  \"armor_damage\": 60,
  \"projectile_speed\": 300,
  \"range\": 1000,
  \"projectile_behavior\": \"guided\",
  \"tracking_strength\": 0.8,
  \"explosion_radius\": 25,
  \"projectile_sprite\": \"path/to/sprite.png\",
  \"compatible_launchers\": [\"launcher_id_1\", \"launcher_id_2\"]
}
```

## Editor Usage

### Creating a New Launcher
1. Open Editor → Weapons tab
2. Set `uses_ammo: true`
3. Configure mount type, fire rate, max ammo capacity
4. **Do not set** shield_damage/armor_damage (ammo handles this)

### Creating Compatible Ammo
1. Open Editor → Ammunition tab
2. Set damage, speed, tracking, sprite
3. **Critical**: Add launcher IDs to `compatible_launchers` list
4. Set appropriate quantity for purchase

### Testing Compatibility
1. Load game
2. Install launcher on ship
3. Purchase ammo → should auto-load into compatible launchers
4. Fire weapon → should use ammo specifications

## Troubleshooting

### \"No Compatible Launchers\" Error
- Check ammo's `compatible_launchers` array
- Ensure launcher ID matches exactly
- Verify launcher has `uses_ammo: true`

### Wrong Damage/Speed Values
- Launchers should NOT have damage values
- Ammo should define all projectile properties
- Check `projectile_speed` field name (not \"speed\")

### Ammo Not Loading
- Verify `compatible_launchers` array is not empty
- Check launcher's `max_ammo` capacity
- Ensure ammo `quantity` > 0

## Extension Examples

### Multi-Launcher Compatibility
```json
{
  \"universal_missile\": {
    \"compatible_launchers\": [
      \"light_missile_rack\",
      \"heavy_missile_rack\",
      \"torpedo_tube\"
    ]
  }
}
```

### Specialized Ammo Types
```json
{
  \"armor_piercing_shell\": {
    \"shield_damage\": 20,
    \"armor_damage\": 150,
    \"compatible_launchers\": [\"railgun\"]
  },
  \"shield_buster_shell\": {
    \"shield_damage\": 200,
    \"armor_damage\": 30,
    \"compatible_launchers\": [\"railgun\"]
  }
}
```

## Best Practices

1. **Naming**: Use descriptive, consistent IDs
2. **Balance**: Test damage/speed ratios for gameplay
3. **Compatibility**: Group related launchers and ammo
4. **Documentation**: Add clear descriptions for each outfit
5. **Validation**: Always test editor→game workflow

## Technical Notes

- Outfit loading happens in `outfit_data_loader.py`
- Projectile creation in `player.py::fire_weapon()`
- Editor save/load in `data_manager.py`
- Compatibility checking in `player.py::load_ammo()`

---

*This system allows infinite weapon combinations while maintaining consistent game physics and balance.*