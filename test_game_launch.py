"""
Test if the main game can launch without the keystate error
"""

print("=== TESTING MAIN GAME LAUNCH ===")

try:
    import sys
    import os
    
    # Add the src directory to the path
    game_dir = os.path.dirname(__file__)
    src_dir = os.path.join(game_dir, 'src')
    sys.path.insert(0, src_dir)
    
    print(f"Game directory: {game_dir}")
    print(f"Source directory: {src_dir}")
    
    # Try to import the main game components
    print("Importing game modules...")
    
    from game_objects.standardized_outfits import OUTFITS_REGISTRY
    print(f"✓ Standardized outfits imported, registry size: {len(OUTFITS_REGISTRY)}")
    
    from game_objects.example_outfits import *
    print(f"✓ Example outfits imported, registry size after: {len(OUTFITS_REGISTRY)}")
    
    from game_objects.player import Player
    print("✓ Player class imported")
    
    # Test creating a basic game instance
    import pygame as pg
    pg.init()
    print("✓ Pygame initialized")
    
    # Create a minimal game-like object for player testing
    class MockGame:
        def __init__(self):
            self.screen = pg.display.set_mode((800, 600))
            self.all_sprites = pg.sprite.Group()
            
        def set_status_message(self, msg, color=(255,255,255)):
            print(f"Status: {msg}")
    
    mock_game = MockGame()
    print("✓ Mock game created")
    
    # Try creating a player
    player = Player(mock_game, "Test Player", "Test Ship", "scout")
    print("✓ Player created successfully")
    
    # Test the problematic input_state creation
    keystate = pg.key.get_pressed()
    input_state = {
        'thrust_forward': keystate[pg.K_w],
        'thrust_reverse': keystate[pg.K_s], 
        'turn_left': keystate[pg.K_a],
        'turn_right': keystate[pg.K_d],
        'afterburner': keystate[pg.K_LSHIFT],  # This was the problematic line
        'fire_weapons': keystate[pg.K_SPACE]
    }
    print("✓ Input state creation works")
    
    print("\n=== ALL TESTS PASSED ===")
    print("The game should now launch without the keystate error!")
    
except Exception as e:
    print(f"\n❌ ERROR: {e}")
    import traceback
    traceback.print_exc()
    
finally:
    try:
        pg.quit()
    except:
        pass
