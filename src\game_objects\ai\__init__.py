"""
AI Package - Modular AI system for Escape Velocity Py

This package contains a clean, modular AI system split into:
- core: Base functionality, constants, and utilities
- systems: Individual AI subsystems (sensors, combat, movement, etc.)
- behaviors: Specific behavior implementations (combat, patrol, flee)
- personalities: Faction and role-based AI personalities

The system is designed to be maintainable, performant, and AI-assistant friendly.

Main entry point: AICoordinator - orchestrates all AI subsystems
"""

from .ai_coordinator import AICoordinator, create_ai_coordinator
from .core.ai_constants import *
from .core.ai_utils import *

__all__ = [
    'AICoordinator',
    'create_ai_coordinator'
]
