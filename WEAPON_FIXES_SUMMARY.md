# Escape Velocity Weapon System Fixes

## Issues Fixed

### 1. Weapon Cycling Only Shows Launchers Once
**Problem**: When you had a missile rack with multiple ammo types (dumbfire and smart missiles), pressing R would only show the launcher once, not allowing you to select different ammo types.

**Solution**: Implemented a new weapon/ammo combination system that creates separate entries for each weapon + ammo type combination. Now R cycling shows:
- Laser Cannon
- Missile Rack - Dumbfire Missiles
- Missile Rack - Smart Missiles

### 2. Double Ammo Consumption Bug
**Problem**: Each weapon shot was consuming 2 ammo instead of 1 because ammo was being consumed in both `fire_weapon()` method and `weapon.fire()` method.

**Solution**: Removed the duplicate ammo consumption in `fire_weapon()` method. Now ammo is only consumed once in the `weapon.fire()` method.

### 3. Cannot Select Different Ammo Types
**Problem**: No way to cycle through different ammo types for the same launcher.

**Solution**: Added two approaches:
- **R key**: Cycles through all weapon/ammo combinations (including different ammo types for the same launcher)
- **T key**: Cycles through ammo types for the currently selected launcher only

## Implementation Details

### New Player Class Features

1. **Enhanced Weapon/Ammo Tracking**:
   ```python
   self.weapon_ammo_combinations = []  # List of (weapon_index, ammo_id) tuples
   self.active_weapon_ammo_index = 0
   self.available_ammo_types = {}  # Dict of ammo_id -> ammo_outfit for inventory
   ```

2. **New Methods**:
   - `_rebuild_weapon_ammo_combinations()`: Builds list of all weapon/ammo combinations
   - `get_current_weapon_and_ammo()`: Gets currently selected weapon and ammo type
   - `_switch_launcher_ammo()`: Switches ammo type loaded in a launcher
   - `cycle_ammo_for_current_weapon()`: Cycles through ammo types for current launcher

3. **Updated Methods**:
   - `cycle_weapons()`: Now cycles through weapon/ammo combinations
   - `get_active_weapon_info()`: Shows weapon name and loaded ammo type
   - `fire_weapon()`: Uses new weapon/ammo system, fixes double consumption
   - `load_ammo()`: Now manages ammo inventory properly

### Control Scheme

- **R Key**: Cycle through all weapon/ammo combinations
  - Laser Cannon
  - Missile Rack - Dumbfire
  - Missile Rack - Smart Missiles
  - (cycles back to start)

- **T Key**: Cycle through ammo types for current launcher
  - If you have a missile rack selected, T will cycle between:
    - Dumbfire Missiles
    - Smart Missiles
    - (any other compatible ammo types)

## Files Modified

1. **`src/game_objects/player.py`**:
   - Added weapon/ammo combination system
   - Fixed double ammo consumption
   - Added new cycling methods
   - Updated weapon info display

2. **`src/main.py`**:
   - Added T key handler for ammo cycling
   - Updated R key description

## Testing

### Manual Testing
1. **Run the game** and check that you start with:
   - Laser Cannon
   - Missile Rack with both dumbfire and smart missiles loaded

2. **Test R key cycling**:
   - Press R repeatedly
   - Should show: Laser Cannon → Missile Rack - Dumbfire → Missile Rack - Smart Missiles → (repeat)

3. **Test T key cycling**:
   - Select a missile rack (using R)
   - Press T repeatedly
   - Should cycle between different ammo types for that launcher

4. **Test ammo consumption**:
   - Fire missiles and check that only 1 ammo is consumed per shot
   - Previously was consuming 2 ammo per shot

### Automated Testing
Run the test script:
```bash
cd EscapeVelocityPy
python test_weapon_fixes.py
```

This will test:
- Weapon/ammo combination building
- Cycling functionality
- Ammo consumption (should be 1 per shot)
- Ammo availability

## Expected Behavior

### Before Fixes:
- R cycling: Laser Cannon → Missile Rack → (repeat)
- No way to select different missile types
- 2 ammo consumed per missile shot
- Smart missiles not accessible

### After Fixes:
- R cycling: Laser Cannon → Missile Rack (Dumbfire) → Missile Rack (Smart) → (repeat)
- T cycling: When missile rack selected, cycles between Dumbfire ⟷ Smart missiles
- 1 ammo consumed per missile shot
- Smart missiles fully accessible and functional

## Debugging

If you encounter issues:

1. **Check console output** for error messages
2. **Verify ammo loading**: Both dumbfire and smart missiles should load at game start
3. **Check weapon combinations**: Should see multiple combinations in debug output
4. **Test ammo switching**: T key should show status messages when switching ammo

## Future Enhancements

Possible improvements:
- Visual indicators for selected ammo type in UI
- Sound effects for weapon/ammo switching  
- Ammunition reload system from cargo/inventory
- Multiple launchers of the same type with different ammo loads
