"""
AI Systems Package - Modular AI subsystems for Escape Velocity Py

This package contains the core AI systems:
- sensor_system: Target detection and threat assessment
- state_system: State management with transition cooldowns
- movement_system: Navigation and positioning
- weapon_system: Weapon selection and firing

Each system is designed to be independent and focused on a single responsibility.
"""

from .sensor_system import AISensorManager
from .state_system import AIStateManager
from .movement_system import AIMovementManager
from .weapon_system import AIWeaponManager

__all__ = [
    'AISensorManager',
    'AIStateManager',
    'AIMovementManager',
    'AIWeaponManager'
]
