#!/usr/bin/env python3
"""
Test script to verify weapon cycling and ammo consumption fixes.
Run this to test the fixes without launching the full game.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import required modules
from game_objects.player import Player
from game_objects.standardized_outfits import get_outfit_by_id
from game_objects.outfit_data_loader import load_outfits_from_data_files

# Mock game class for testing
class MockGame:
    def __init__(self):
        self.status_messages = []
    
    def set_status_message(self, message, color):
        self.status_messages.append((message, color))
        print(f"Status: {message}")

def test_weapon_fixes():
    """Test the weapon cycling and ammo consumption fixes."""
    print("="*60)
    print("TESTING ESCAPE VELOCITY WEAPON FIXES")
    print("="*60)
    
    # Load outfits
    print("\n1. Loading outfits...")
    outfit_count = load_outfits_from_data_files()
    print(f"   Loaded {outfit_count} outfits")
    
    # Create mock game and player
    print("\n2. Creating test player...")
    mock_game = MockGame()
    player = Player(mock_game, "Test Pilot", "Test Ship", "scout")
    
    print(f"   Player created with {len(player.weapons)} weapons")
    print(f"   Available ammo types: {list(player.available_ammo_types.keys())}")
    
    # Test weapon/ammo combinations
    print("\n3. Testing weapon/ammo combinations...")
    print(f"   Weapon/ammo combinations: {len(player.weapon_ammo_combinations)}")
    for i, (weapon_idx, ammo_id) in enumerate(player.weapon_ammo_combinations):
        weapon = player.weapons[weapon_idx]
        ammo_name = player.available_ammo_types[ammo_id].name if ammo_id else "N/A"
        print(f"   {i}: {weapon.name} - {ammo_name}")
    
    # Test weapon cycling
    print("\n4. Testing weapon cycling (R key simulation)...")
    for i in range(len(player.weapon_ammo_combinations) + 1):
        player.cycle_weapons()
        weapon, ammo_id = player.get_current_weapon_and_ammo()
        info = player.get_active_weapon_info()
        print(f"   Cycle {i+1}: {info}")
    
    # Test ammo cycling for launchers
    print("\n5. Testing ammo cycling (T key simulation)...")
    # First select a launcher
    for i, (weapon_idx, ammo_id) in enumerate(player.weapon_ammo_combinations):
        weapon = player.weapons[weapon_idx]
        if getattr(weapon, 'uses_ammo', False):
            player.active_weapon_ammo_index = i
            break
    
    print(f"   Selected launcher: {player.get_active_weapon_info()}")
    
    # Try cycling ammo
    for i in range(3):
        player.cycle_ammo_for_current_weapon()
        info = player.get_active_weapon_info()
        print(f"   Ammo cycle {i+1}: {info}")
    
    # Test ammo consumption
    print("\n6. Testing ammo consumption (simulated firing)...")
    weapon, ammo_id = player.get_current_weapon_and_ammo()
    if weapon and getattr(weapon, 'uses_ammo', False):
        initial_ammo = getattr(weapon, 'current_ammo', 0)
        print(f"   Initial ammo: {initial_ammo}")
        
        # Simulate firing (without actually creating projectiles)
        if weapon.can_fire():
            success = weapon.fire()  # This should consume 1 ammo
            final_ammo = getattr(weapon, 'current_ammo', 0)
            consumed = initial_ammo - final_ammo
            print(f"   After firing: {final_ammo} (consumed: {consumed})")
            
            if consumed == 1:
                print("   ✅ PASS: Ammo consumption is correct (1 per shot)")
            else:
                print(f"   ❌ FAIL: Wrong ammo consumption ({consumed} per shot)")
        else:
            print("   Cannot fire weapon")
    
    print("\n7. Testing weapon availability check...")
    # Check that both ammo types are available for missile rack
    for ammo_id, ammo_outfit in player.available_ammo_types.items():
        compatible_launchers = getattr(ammo_outfit, 'compatible_launchers', [])
        print(f"   {ammo_outfit.name}: Compatible with {compatible_launchers}, Quantity: {ammo_outfit.quantity}")
    
    print("\n" + "="*60)
    print("TEST COMPLETED")
    print("="*60)
    
    print("\nKey findings:")
    print("- R key cycles through all weapon/ammo combinations")
    print("- T key cycles through ammo types for current launcher")
    print("- Ammo consumption should be fixed (1 per shot, not 2)")
    print("- Both dumbfire and smart missiles should be available")

if __name__ == "__main__":
    test_weapon_fixes()
