import pygame as pg

class Faction:
    def __init__(self, faction_id, name, color=(200, 200, 200)):
        self.id = faction_id # e.g., "federation", "pirates"
        self.name = name # e.g., "Galactic Federation", "Marauders Clan"
        self.color = color # For UI elements, map coloring, etc.

    def __repr__(self):
        return f"Faction(ID: {self.id}, Name: {self.name})"

# Predefined Factions (can be expanded or moved to a config file)
FACTIONS_DATA = {
    "unaligned": {"name": "Unaligned", "color": (150, 150, 150)},
    "federation": {"name": "Terran Federation", "color": (100, 100, 255)},
    "corporation": {"name": "Orion Corp", "color": (255, 255, 100)},
    "pirates": {"name": "Void Raiders", "color": (255, 50, 50)},
    "independent": {"name": "Independent Colonies", "color": (50, 200, 50)},
    "merchants": {"name": "Trade Guild", "color": (200, 100, 200)},
}

def get_faction_list():
    return [Faction(fid, data["name"], data["color"]) for fid, data in FACTIONS_DATA.items()]

def get_faction_by_id(faction_id):
    if faction_id in FACTIONS_DATA:
        data = FACTIONS_DATA[faction_id]
        return Faction(faction_id, data["name"], data["color"])
    return Faction("unknown", "Unknown Faction", (128,128,128)) # Default for safety
