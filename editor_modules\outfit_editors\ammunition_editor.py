"""
Ammunition Editor for the Enhanced Content Editor
Handles editing of ammunition outfits
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .base_editor import BaseOutfitEditor
from .ui_components import StandardParameterGrid, ParameterLoader

class AmmunitionEditor(BaseOutfitEditor):
    """Editor for ammunition outfits."""
    
    def __init__(self, parent, data_manager):
        super().__init__(parent, data_manager, "ammunition")
    
    def setup_editor_ui(self, parent):
        """Setup the ammunition editor interface."""
        # Basic Properties Grid
        self.basic_grid = StandardParameterGrid(parent, "Basic Properties")
        self.basic_grid.add_string_field("Name", "name")
        self.basic_grid.add_int_field("Cost", "cost", 10, 10000)
        self.basic_grid.add_multi_select_field("Compatible Launchers", "compatible_launchers", self._get_available_launchers)
        self.basic_grid.add_int_field("Quantity", "quantity", 1, 100)
        self.basic_grid.add_file_field("Outfitter Icon", "outfitter_icon")
        self.basic_grid.add_file_field("Outfitter Image", "outfitter_image")
        self.basic_grid.add_file_field("Projectile Sprite", "projectile_sprite", 
                                      [("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")])
        self.basic_grid.set_multiselect_change_callback(self._on_launcher_selection_change)
        self.basic_grid.pack(fill=tk.X, padx=5, pady=5)

        # Combat Properties Grid
        self.combat_grid = StandardParameterGrid(parent, "Combat Properties")
        self.combat_grid.add_int_field("Shield Damage", "shield_damage", 1, 1000)
        self.combat_grid.add_int_field("Armor Damage", "armor_damage", 1, 1000)
        self.combat_grid.add_int_field("Range", "range", 50, 2000)
        self.combat_grid.add_int_field("Projectile Speed", "projectile_speed", 50, 1000)
        self.combat_grid.add_float_field("Tracking Strength", "tracking_strength", 0.0, 1.0, 0.1)
        self.combat_grid.pack(fill=tk.X, padx=5, pady=5)

        # Behavior Properties Grid
        self.behavior_grid = StandardParameterGrid(parent, "Behavior Properties")
        self.behavior_grid.add_combo_field("Behavior", "projectile_behavior", ["dumbfire", "guided", "beam", "delayed", "proximity"])
        self.behavior_grid.add_float_field("Delay Time", "delay_time", 0.0, 10.0, 0.1)
        self.behavior_grid.add_int_field("Explosion Radius", "explosion_radius", 0, 100)
        
        # Add callback to behavior dropdown to save immediately
        behavior_var = self.behavior_grid.vars['projectile_behavior']
        behavior_var.trace('w', self._on_behavior_change)
        
        self.behavior_grid.pack(fill=tk.X, padx=5, pady=5)
        
        # Description
        desc_frame = ttk.LabelFrame(parent, text="Description")
        desc_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.description_text = tk.Text(desc_frame, height=3, wrap=tk.WORD)
        self.description_text.pack(fill=tk.X, padx=5, pady=5)

        # Save button
        save_frame = ttk.Frame(parent)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        ttk.Button(save_frame, text="Save Ammunition", command=self.save_item).pack(side=tk.RIGHT, padx=5)
    
    def load_item_into_editor(self, ammo):
        """Load ammunition data into the editor."""
        super().load_item_into_editor(ammo)
        
        # Load using standardized parameter loader
        ParameterLoader.load_outfit_parameters(ammo, self.basic_grid)
        ParameterLoader.load_outfit_parameters(ammo, self.combat_grid)
        ParameterLoader.load_outfit_parameters(ammo, self.behavior_grid)
        
        # Load description
        self.description_text.delete(1.0, tk.END)
        self.description_text.insert(1.0, getattr(ammo, 'description', ''))
    
    def save_item(self):
        """Save the current ammunition with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No ammunition selected to save")
            return
        
        try:
            # Save using standardized parameter loader
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.basic_grid)
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.combat_grid)
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.behavior_grid)
            
            # Save description
            self.current_outfit.description = self.description_text.get(1.0, tk.END).strip()
            
            super().save_item()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save ammunition: {e}")
    
    def _on_behavior_change(self, *args):
        """Auto-save when behavior dropdown changes to prevent data loss."""
        if self.current_outfit:
            try:
                behavior_value = self.behavior_grid.vars['projectile_behavior'].get()
                self.current_outfit.projectile_behavior = behavior_value
                print(f"Auto-saved behavior change: {behavior_value}")  # Debug
            except Exception as e:
                print(f"Error auto-saving behavior: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new ammunition instance."""
        class SimpleAmmo:
            def __init__(self, id, name):
                # Basic properties
                self.id = id
                self.name = name
                self.category = "ammunition"
                self.cost = 500
                self.compatible_launchers = []
                self.quantity = 10
                self.outfitter_icon = ""
                self.outfitter_image = ""
                self.projectile_sprite = ""
                # Combat properties  
                self.shield_damage = 40
                self.armor_damage = 60
                self.range = 600  # FIXED: Ensure range has a default value
                self.projectile_speed = 300  # FIXED: Use projectile_speed instead of speed
                self.tracking_strength = 0.0
                # Behavior properties
                self.projectile_behavior = "dumbfire"
                self.delay_time = 0.0
                self.explosion_radius = 20
                self.description = ""

        return SimpleAmmo(item_id, item_id.replace('_', ' ').title())
    
    def _on_launcher_selection_change(self, var_name):
        """Auto-save when launcher selection changes."""
        if var_name == 'compatible_launchers' and self.current_outfit:
            try:
                # Get current selections and save immediately
                selected_launchers = ParameterLoader._get_multi_select_values(self.basic_grid.vars['compatible_launchers'])
                self.current_outfit.compatible_launchers = selected_launchers
                print(f"Auto-saved launcher selection: {selected_launchers}")
            except Exception as e:
                print(f"Error auto-saving launcher selection: {e}")
    
    def _get_available_launchers(self):
        """Get list of available launcher weapons."""
        launchers = []
        weapons = self.data_manager.get_outfits_by_category('weapons')
        for weapon_id, weapon in weapons.items():
            if getattr(weapon, 'uses_ammo', False):
                launchers.append(weapon_id)
        return launchers
