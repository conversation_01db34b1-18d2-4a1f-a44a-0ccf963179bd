#!/usr/bin/env python3
"""
Test script to verify weapon system fixes
"""

import sys
import os
sys.path.append('src')

# Set up the game objects path
import json

def test_json_data_integrity():
    """Test that JSON data has all required fields."""
    print("=== Testing JSON Data Integrity ===")
    
    with open('outfits_data.json', 'r') as f:
        data = json.load(f)
    
    issues = []
    
    for outfit_id, outfit_data in data.items():
        category = outfit_data.get('category', 'unknown')
        
        if category == 'weapons':
            # Check required weapon fields
            required_fields = [
                'projectile_behavior', 'projectile_speed', 'shield_damage', 
                'armor_damage', 'mount_type', 'uses_ammo', 'power_cost'
            ]
            
            # For direct-fire weapons, check projectile fields
            if not outfit_data.get('uses_ammo', False):
                required_fields.extend(['tracking_strength', 'proximity_radius', 'delay_time'])
            
            missing_fields = []
            for field in required_fields:
                if field not in outfit_data:
                    missing_fields.append(field)
            
            if missing_fields:
                issues.append(f"Weapon '{outfit_id}' missing fields: {missing_fields}")
                
        elif category == 'ammunition':
            # Check required ammo fields
            required_fields = [
                'compatible_launchers', 'projectile_behavior', 'projectile_speed',
                'shield_damage', 'armor_damage', 'tracking_strength', 'range'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field not in outfit_data:
                    missing_fields.append(field)
            
            if missing_fields:
                issues.append(f"Ammo '{outfit_id}' missing fields: {missing_fields}")
    
    if issues:
        print("❌ Issues found:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ All weapons and ammo have required fields")
        return True

def test_editor_projectile_fields():
    """Test that editor projectile fields match game engine."""
    print("\\n=== Testing Editor Projectile Fields ===")
    
    # Read the ui_components.py file to check projectile fields
    with open('editor_modules/outfit_editors/ui_components.py', 'r') as f:
        content = f.read()
    
    # Check that obsolete fields are removed
    obsolete_fields = ['projectile_color', 'projectile_size', 'projectile_lifetime']
    issues = []
    
    for field in obsolete_fields:
        if field in content:
            issues.append(f"Obsolete field '{field}' still in editor")
    
    # Check that required fields are present
    required_fields = ['projectile_speed', 'projectile_behavior', 'tracking_strength', 'proximity_radius', 'delay_time']
    
    for field in required_fields:
        if field not in content:
            issues.append(f"Required field '{field}' missing from editor")
    
    if issues:
        print("❌ Issues found:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ Editor projectile fields match game engine")
        return True

def test_weapon_launcher_compatibility():
    """Test that weapons and ammo are properly configured."""
    print("\\n=== Testing Weapon/Ammo Compatibility ===")
    
    with open('outfits_data.json', 'r') as f:
        data = json.load(f)
    
    weapons = {k: v for k, v in data.items() if v.get('category') == 'weapons'}
    ammo = {k: v for k, v in data.items() if v.get('category') == 'ammunition'}
    
    issues = []
    
    # Check that all launchers have compatible ammo
    for weapon_id, weapon_data in weapons.items():
        if weapon_data.get('uses_ammo', False):
            # Find compatible ammo
            compatible_ammo = []
            for ammo_id, ammo_data in ammo.items():
                if weapon_id in ammo_data.get('compatible_launchers', []):
                    compatible_ammo.append(ammo_id)
            
            if not compatible_ammo:
                issues.append(f"Launcher '{weapon_id}' has no compatible ammo")
            else:
                print(f"✅ Launcher '{weapon_id}' compatible with: {compatible_ammo}")
    
    # Check that all ammo has valid launcher targets
    for ammo_id, ammo_data in ammo.items():
        launchers = ammo_data.get('compatible_launchers', [])
        missing_launchers = []
        
        for launcher_id in launchers:
            if launcher_id not in weapons:
                missing_launchers.append(launcher_id)
        
        if missing_launchers:
            issues.append(f"Ammo '{ammo_id}' references non-existent launchers: {missing_launchers}")
    
    if issues:
        print("❌ Issues found:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ All weapon/ammo compatibility is correct")
        return True

def test_turret_weapon_config():
    """Test that turret weapons have proper configuration."""
    print("\\n=== Testing Turret Weapon Configuration ===")
    
    with open('outfits_data.json', 'r') as f:
        data = json.load(f)
    
    issues = []
    
    for outfit_id, outfit_data in data.items():
        if (outfit_data.get('category') == 'weapons' and 
            outfit_data.get('mount_type') == 'turret'):
            
            # Check that turret has all required fields
            required_fields = [
                'projectile_behavior', 'projectile_speed', 'shield_damage', 
                'armor_damage', 'tracking_strength', 'proximity_radius', 'delay_time'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field not in outfit_data:
                    missing_fields.append(field)
            
            if missing_fields:
                issues.append(f"Turret '{outfit_id}' missing fields: {missing_fields}")
            else:
                print(f"✅ Turret '{outfit_id}' properly configured")
    
    if issues:
        print("❌ Issues found:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ All turret weapons properly configured")
        return True

def main():
    """Run all tests."""
    print("🔧 Escape Velocity Weapon System Test Suite\\n")
    
    tests = [
        test_json_data_integrity,
        test_editor_projectile_fields,
        test_weapon_launcher_compatibility,
        test_turret_weapon_config
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results.append(False)
    
    print("\\n" + "="*50)
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 ALL TESTS PASSED ({passed}/{total})")
        print("\\nWeapon system is ready to use!")
    else:
        print(f"⚠️  SOME TESTS FAILED ({passed}/{total})")
        print("\\nPlease fix the issues above before using the weapon system.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
