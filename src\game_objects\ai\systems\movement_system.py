"""
AI Movement System - Handles ship movement, navigation, and positioning
Provides intelligent movement behaviors for different AI states.
"""
import pygame as pg
import math
import random
from ..core.ai_constants import (
    AI_DRAG_FACTOR,
    AI_PATROL_RADIUS,
    AI_WEAPON_RANGE,
    FIXED_WEAPON_OPTIMAL_RANGE,
    TURRET_WEAPON_OPTIMAL_RANGE,
    MISSILE_WEAPON_OPTIMAL_RANGE
)
from ..core.ai_utils import (
    normalize_angle, 
    angle_difference, 
    clamp, 
    choose_random_position_in_circle
)

class AIMovementManager:
    """
    Manages AI ship movement including navigation, positioning, and maneuvering.
    Provides different movement behaviors for various AI states.
    """
    
    def __init__(self, ai_ship):
        self.ship = ai_ship
        self.target_position = None
        self.movement_mode = "idle"
        self.maneuver_timer = 0
        self.last_maneuver_change = 0
        
        # Movement parameters
        self.desired_range = AI_WEAPON_RANGE
        self.approach_speed = 1.0
        self.turn_speed = 3.0
        
        # Maneuvering state
        self.current_maneuver = "none"
        self.maneuver_data = {}
    
    def update(self, dt, ai_state, target=None):
        """
        Update movement system based on AI state and target.
        
        Args:
            dt: Delta time
            ai_state: Current AI state
            target: Current target entity (if any)
        """
        self.maneuver_timer += 1
        
        # Update movement based on AI state
        if ai_state == "IDLE":
            self.perform_idle_movement()
        elif ai_state == "PATROLLING":
            self.perform_patrol_movement()
        elif ai_state == "ATTACKING":
            self.perform_combat_movement(target)
        elif ai_state == "FLEEING":
            self.perform_flee_movement(target)
        elif ai_state == "DISABLED":
            self.perform_disabled_movement()
        
        # Apply movement and physics
        self.apply_movement(dt)
    
    def perform_idle_movement(self):
        """Movement behavior when idle - minimal movement."""
        self.movement_mode = "idle"
        
        # Gradually reduce velocity
        if self.ship.vel.length() > 0.1:
            self.ship.vel *= 0.98
        
        # Occasional small random movements
        if random.random() < 0.01:  # 1% chance per frame
            random_angle = random.uniform(0, 360)
            small_thrust = pg.math.Vector2(1, 0).rotate(random_angle) * 0.5
            self.ship.vel += small_thrust
    
    def perform_patrol_movement(self):
        """Movement behavior when patrolling."""
        self.movement_mode = "patrol"
        
        # Get patrol center from state manager
        state_manager = getattr(self.ship, 'state_manager', None)
        if state_manager:
            patrol_center = state_manager.get_state_data('patrol_center', self.ship.pos)
            patrol_target = state_manager.get_state_data('patrol_target')
        else:
            patrol_center = self.ship.pos
            patrol_target = None
        
        # Choose new patrol target if needed
        if not patrol_target or self.ship.pos.distance_to(patrol_target) < 50:
            patrol_target = choose_random_position_in_circle(patrol_center, AI_PATROL_RADIUS)
            if state_manager:
                state_manager.set_state_data('patrol_target', patrol_target)
        
        # Move toward patrol target
        self.move_toward_position(patrol_target, 0.7)
    
    def perform_combat_movement(self, target):
        """
        Movement behavior when in combat.
        
        Args:
            target: The target entity to engage
        """
        if not target or not hasattr(target, 'pos'):
            return
        
        self.movement_mode = "combat"
        
        # Determine optimal range based on weapons
        optimal_range = self.calculate_optimal_combat_range()
        
        # Get distance to target
        distance_to_target = self.ship.pos.distance_to(target.pos)
        
        # Choose combat maneuver based on distance and situation
        if distance_to_target > optimal_range * 1.5:
            self.current_maneuver = "approach"
        elif distance_to_target < optimal_range * 0.7:
            self.current_maneuver = "retreat"
        else:
            # In optimal range - use advanced maneuvers
            if self.maneuver_timer - self.last_maneuver_change > 120:  # Change every 2 seconds
                self.choose_combat_maneuver()
                self.last_maneuver_change = self.maneuver_timer
        
        # Execute the chosen maneuver
        self.execute_combat_maneuver(target, optimal_range)
    
    def perform_flee_movement(self, threat=None):
        """
        Movement behavior when fleeing.
        
        Args:
            threat: The entity to flee from (if any)
        """
        self.movement_mode = "flee"
        
        if threat and hasattr(threat, 'pos'):
            # Flee directly away from threat
            flee_direction = (self.ship.pos - threat.pos).normalize()
        else:
            # Flee in random direction if no specific threat
            flee_direction = pg.math.Vector2(1, 0).rotate(random.uniform(0, 360))
        
        # Move at maximum speed away from threat
        self.ship.vel += flee_direction * self.ship.acceleration * 1.5
        
        # Add some evasive maneuvering
        if random.random() < 0.1:  # 10% chance for evasive turn
            evasive_angle = random.uniform(-45, 45)
            evasive_thrust = pg.math.Vector2(1, 0).rotate(
                flee_direction.angle() + evasive_angle
            ) * self.ship.acceleration * 0.5
            self.ship.vel += evasive_thrust
    
    def perform_disabled_movement(self):
        """Movement behavior when disabled - drift with minimal control."""
        self.movement_mode = "disabled"
        
        # Gradual velocity reduction (ship is damaged)
        self.ship.vel *= 0.99
        
        # Occasional thruster sputter
        if random.random() < 0.02:  # 2% chance
            sputter_direction = pg.math.Vector2(1, 0).rotate(random.uniform(0, 360))
            self.ship.vel += sputter_direction * 0.2
    
    def calculate_optimal_combat_range(self):
        """
        Calculate optimal combat range based on equipped weapons.
        
        Returns:
            float: Optimal combat range
        """
        if not hasattr(self.ship, 'weapons') or not self.ship.weapons:
            return AI_WEAPON_RANGE
        
        # Analyze weapons to determine optimal range
        total_range = 0
        weapon_count = 0
        
        for weapon in self.ship.weapons:
            if hasattr(weapon, 'mount_type'):
                if weapon.mount_type == "fixed":
                    total_range += FIXED_WEAPON_OPTIMAL_RANGE
                elif weapon.mount_type == "turret":
                    total_range += TURRET_WEAPON_OPTIMAL_RANGE
                elif weapon.mount_type == "missile":
                    total_range += MISSILE_WEAPON_OPTIMAL_RANGE
                else:
                    total_range += AI_WEAPON_RANGE
                weapon_count += 1
        
        if weapon_count > 0:
            return total_range / weapon_count
        else:
            return AI_WEAPON_RANGE
    
    def choose_combat_maneuver(self):
        """Choose a new combat maneuver based on situation."""
        maneuvers = ["circle_strafe", "approach", "retreat", "flanking"]
        
        # Weight maneuvers based on ship characteristics
        weights = [1.0, 1.0, 1.0, 1.0]  # Default equal weights
        
        # Prefer circle strafing for medium ships
        if hasattr(self.ship, 'ship_size'):
            if self.ship.ship_size == "medium":
                weights[0] = 2.0  # Circle strafe
            elif self.ship.ship_size == "small":
                weights[3] = 2.0  # Flanking
            elif self.ship.ship_size == "large":
                weights[1] = 2.0  # Approach
        
        # Choose weighted random maneuver
        total_weight = sum(weights)
        rand_val = random.uniform(0, total_weight)
        
        cumulative = 0
        for i, weight in enumerate(weights):
            cumulative += weight
            if rand_val <= cumulative:
                self.current_maneuver = maneuvers[i]
                break
        
        # Initialize maneuver data
        self.maneuver_data = {
            'start_time': self.maneuver_timer,
            'direction': random.choice([-1, 1])  # Clockwise or counter-clockwise
        }
    
    def execute_combat_maneuver(self, target, optimal_range):
        """
        Execute the current combat maneuver.
        
        Args:
            target: Target entity
            optimal_range: Desired distance from target
        """
        if self.current_maneuver == "approach":
            self.move_toward_position(target.pos, 1.0)
        
        elif self.current_maneuver == "retreat":
            retreat_direction = (self.ship.pos - target.pos).normalize()
            retreat_position = self.ship.pos + retreat_direction * 100
            self.move_toward_position(retreat_position, 0.8)
        
        elif self.current_maneuver == "circle_strafe":
            self.execute_circle_strafe(target, optimal_range)
        
        elif self.current_maneuver == "flanking":
            self.execute_flanking_maneuver(target, optimal_range)
    
    def execute_circle_strafe(self, target, range_distance):
        """
        Execute circle strafing maneuver around target.
        
        Args:
            target: Target to circle
            range_distance: Desired distance from target
        """
        direction = self.maneuver_data.get('direction', 1)
        
        # Calculate perpendicular direction for strafing
        to_target = target.pos - self.ship.pos
        if to_target.length() > 0:
            to_target = to_target.normalize()
            
            # Perpendicular vector for strafing
            strafe_direction = pg.math.Vector2(-to_target.y, to_target.x) * direction
            
            # Combine approach/retreat with strafing
            current_distance = self.ship.pos.distance_to(target.pos)
            if current_distance > range_distance:
                # Too far - approach while strafing
                movement = to_target * 0.7 + strafe_direction * 0.3
            elif current_distance < range_distance * 0.8:
                # Too close - retreat while strafing
                movement = -to_target * 0.7 + strafe_direction * 0.3
            else:
                # Good distance - pure strafe
                movement = strafe_direction
            
            self.ship.vel += movement * self.ship.acceleration
    
    def execute_flanking_maneuver(self, target, range_distance):
        """
        Execute flanking maneuver to attack from the side.
        
        Args:
            target: Target to flank
            range_distance: Desired distance from target
        """
        direction = self.maneuver_data.get('direction', 1)
        
        # Calculate flanking position
        to_target = target.pos - self.ship.pos
        if to_target.length() > 0:
            # Position to the side of the target
            flank_angle = 90 * direction  # 90 degrees to the side
            flank_direction = to_target.rotate(flank_angle).normalize()
            flank_position = target.pos + flank_direction * range_distance
            
            self.move_toward_position(flank_position, 0.9)
    
    def move_toward_position(self, target_pos, speed_factor=1.0):
        """
        Move toward a specific position.
        
        Args:
            target_pos: Target position (Vector2)
            speed_factor: Speed multiplier (0.0 to 1.0)
        """
        if not target_pos:
            return
        
        direction = target_pos - self.ship.pos
        if direction.length() > 5:  # Don't micro-adjust
            direction = direction.normalize()
            thrust = direction * self.ship.acceleration * speed_factor
            self.ship.vel += thrust
    
    def apply_movement(self, dt):
        """
        Apply movement physics and constraints.
        
        Args:
            dt: Delta time
        """
        # Apply drag
        self.ship.vel *= (1.0 - AI_DRAG_FACTOR)
        
        # Limit maximum velocity
        max_speed = getattr(self.ship, 'max_speed', 200)
        if self.ship.vel.length() > max_speed:
            self.ship.vel = self.ship.vel.normalize() * max_speed
        
        # Update position
        self.ship.pos += self.ship.vel * dt
    
    def get_movement_info(self):
        """Get current movement information for debugging."""
        return {
            'mode': self.movement_mode,
            'maneuver': self.current_maneuver,
            'velocity': self.ship.vel.length(),
            'target_pos': self.target_position
        }
