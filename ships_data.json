{"scout": {"id": "scout", "name": "Scout", "ship_class": "fighter", "size": "small", "cost": 15000, "min_tech_level": 1, "outfit_space": 20, "cargo_space": 5, "mass": 0.0, "max_speed": 10.0, "acceleration": 1.0, "turn_rate": 1.75, "shields": 50, "max_shields": 50, "armor": 30, "max_armor": 30, "shield_recharge_rate": 0.0, "power_capacity": 750, "power_regen_rate": 10.0, "thruster_power_cost": 1.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 80, "fuel_consumption_rate": 0.0, "jump_fuel_cost": 0.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "scout_spritesheet.png", "sprite_size": 32, "animation_frames": 32, "manufacturer": "", "origin": "", "availability": "", "default_outfits": {}, "description": "A fast, agile scout ship with minimal cargo space."}, "light_fighter": {"id": "light_fighter", "name": "Light Fighter", "ship_class": "fighter", "size": "small", "cost": 18000, "min_tech_level": 1, "outfit_space": 25, "cargo_space": 3, "mass": 1.0, "max_speed": 3.75, "acceleration": 0.5, "turn_rate": 1.5, "shields": 60, "max_shields": 60, "armor": 40, "max_armor": 40, "shield_recharge_rate": 1.0, "power_capacity": 60, "power_regen_rate": 3.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 80, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 32, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": "A nimble fighter designed for dogfighting."}, "freighter": {"id": "freighter", "name": "Freighter", "ship_class": "freighter", "size": "medium", "cost": 45000, "min_tech_level": 2, "outfit_space": 35, "cargo_space": 120, "mass": 1.0, "max_speed": 2.25, "acceleration": 0.25, "turn_rate": 0.5, "shields": 80, "max_shields": 80, "armor": 100, "max_armor": 100, "shield_recharge_rate": 1.0, "power_capacity": 80, "power_regen_rate": 4.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 195, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 48, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": "A standard cargo ship with good capacity."}, "kraken": {"id": "kraken", "name": "<PERSON><PERSON><PERSON>", "ship_class": "fighter", "size": "small", "cost": 10000, "min_tech_level": 1, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "max_shields": 50, "armor": 30, "max_armor": 30, "shield_recharge_rate": 1.0, "power_capacity": 60, "power_regen_rate": 3.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 80, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 32, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": ""}, "corvette": {"id": "corvette", "name": "Corvette", "ship_class": "corvette", "size": "large", "cost": 10000, "min_tech_level": 1, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "max_shields": 50, "armor": 30, "max_armor": 30, "shield_recharge_rate": 1.0, "power_capacity": 165, "power_regen_rate": 5.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 200, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 64, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": ""}, "heavyfighter": {"id": "heavyfighter", "name": "Heavyfighter", "ship_class": "fighter", "size": "medium", "cost": 10000, "min_tech_level": 1, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "max_shields": 50, "armor": 30, "max_armor": 30, "shield_recharge_rate": 1.0, "power_capacity": 100, "power_regen_rate": 4.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 120, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 48, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": ""}, "gunship": {"id": "gunship", "name": "Gunship", "ship_class": "corvette", "size": "medium", "cost": 10000, "min_tech_level": 1, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "max_shields": 50, "armor": 30, "max_armor": 30, "shield_recharge_rate": 1.0, "power_capacity": 110, "power_regen_rate": 4.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 150, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 48, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": ""}, "passengerliner": {"id": "passengerliner", "name": "Passengerliner", "ship_class": "transport", "size": "medium", "cost": 10000, "min_tech_level": 1, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "max_shields": 50, "armor": 30, "max_armor": 30, "shield_recharge_rate": 1.0, "power_capacity": 80, "power_regen_rate": 4.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 180, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 48, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": ""}, "bulkcarrier": {"id": "bulkcarrier", "name": "Bulkcarrier", "ship_class": "transport", "size": "large", "cost": 10000, "min_tech_level": 1, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "max_shields": 50, "armor": 30, "max_armor": 30, "shield_recharge_rate": 1.0, "power_capacity": 120, "power_regen_rate": 5.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 240, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 64, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": ""}, "frigate": {"id": "frigate", "name": "Frigate", "ship_class": "frigate", "size": "large", "cost": 10000, "min_tech_level": 1, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "max_shields": 50, "armor": 30, "max_armor": 30, "shield_recharge_rate": 1.0, "power_capacity": 180, "power_regen_rate": 5.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 220, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 64, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": ""}, "heavyfreighter": {"id": "heavyfreighter", "name": "<PERSON>freighter", "ship_class": "freighter", "size": "large", "cost": 10000, "min_tech_level": 1, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "max_shields": 50, "armor": 30, "max_armor": 30, "shield_recharge_rate": 1.0, "power_capacity": 120, "power_regen_rate": 5.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 260, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 64, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": ""}, "battleship": {"id": "battleship", "name": "Battleship", "ship_class": "battleship", "size": "capital", "cost": 10000, "min_tech_level": 1, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "max_shields": 50, "armor": 30, "max_armor": 30, "shield_recharge_rate": 1.0, "power_capacity": 300, "power_regen_rate": 6.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 330, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 256, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": ""}, "carrier": {"id": "carrier", "name": "Carrier", "ship_class": "carrier", "size": "capital", "cost": 10000, "min_tech_level": 1, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "max_shields": 50, "armor": 30, "max_armor": 30, "shield_recharge_rate": 1.0, "power_capacity": 280, "power_regen_rate": 6.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 420, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 256, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": ""}, "cruiser": {"id": "cruiser", "name": "Cruiser", "ship_class": "cruiser", "size": "capital", "cost": 10000, "min_tech_level": 1, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "max_shields": 50, "armor": 30, "max_armor": 30, "shield_recharge_rate": 1.0, "power_capacity": 260, "power_regen_rate": 6.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 360, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 256, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": ""}, "destroyer": {"id": "destroyer", "name": "Destroyer", "ship_class": "destroyer", "size": "capital", "cost": 10000, "min_tech_level": 1, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "max_shields": 50, "armor": 30, "max_armor": 30, "shield_recharge_rate": 1.0, "power_capacity": 240, "power_regen_rate": 6.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 300, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 256, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": ""}, "courier": {"id": "courier", "name": "Courier", "ship_class": "freighter", "size": "small", "cost": 10000, "min_tech_level": 1, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "max_shields": 50, "armor": 30, "max_armor": 30, "shield_recharge_rate": 1.0, "power_capacity": 48, "power_regen_rate": 3.0, "thruster_power_cost": 5.0, "weapon_power_cost_base": 10.0, "shield_regen_power_cost": 2.0, "fuel_capacity": 130, "fuel_consumption_rate": 1.0, "jump_fuel_cost": 25.0, "shipyard_image": "", "shipyard_display_image": "", "sprite": "", "sprite_size": 32, "animation_frames": 32, "manufacturer": "Independent Shipyards", "origin": "Core Worlds", "availability": "common", "default_outfits": {}, "description": ""}}