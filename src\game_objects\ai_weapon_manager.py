"""
AI Weapon Manager - Dynamic weapon assignment system for AI ships
This module handles intelligent weapon assignment based on available outfits.
"""
import random
from game_objects.standardized_outfits import (
    OUTFITS_REGISTRY, OUTFIT_CATEGORY_WEAPONS, OUTFIT_CATEGORY_AMMUNITION,
    MOUNT_TYPE_FIXED, MOUNT_TYPE_TURRET, MOUNT_TYPE_GUIDED
)

class AIWeaponManager:
    """
    Manages dynamic weapon assignment for AI ships based on available outfits.
    Prevents crashes from missing weapons and scales with game balance changes.
    """
    
    def __init__(self):
        self.weapon_cache = {}
        self.ammo_cache = {}
        self._refresh_weapon_cache()
    
    def _refresh_weapon_cache(self):
        """Refresh the weapon cache from the current outfit registry."""
        self.weapon_cache = {
            'small': {'fixed': [], 'turret': [], 'missile': []},
            'medium': {'fixed': [], 'turret': [], 'missile': []},
            'large': {'fixed': [], 'turret': [], 'missile': []},
            'capital': {'fixed': [], 'turret': [], 'missile': []}
        }
        self.ammo_cache = {}
        
        # Categorize available weapons
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            if outfit.category == OUTFIT_CATEGORY_WEAPONS:
                self._categorize_weapon(outfit_id, outfit)
            elif outfit.category == OUTFIT_CATEGORY_AMMUNITION:
                self._categorize_ammo(outfit_id, outfit)
    
    def _categorize_weapon(self, weapon_id, weapon):
        """Categorize a weapon by size compatibility and mount type."""
        # Determine compatible ship sizes
        compatible_sizes = getattr(weapon, 'ship_size_restrictions', [])
        if not compatible_sizes:
            # If no restrictions, assume compatible with all sizes
            compatible_sizes = ['small', 'medium', 'large', 'capital']
        
        # Determine weapon category
        if weapon.uses_ammo:
            weapon_category = 'missile'
        elif weapon.mount_type == MOUNT_TYPE_TURRET:
            weapon_category = 'turret'
        else:
            weapon_category = 'fixed'
        
        # Add to appropriate size categories
        for size in compatible_sizes:
            if size in self.weapon_cache:
                self.weapon_cache[size][weapon_category].append(weapon_id)
    
    def _categorize_ammo(self, ammo_id, ammo):
        """Categorize ammunition by type."""
        ammo_type = getattr(ammo, 'ammo_type', 'unknown')
        if ammo_type not in self.ammo_cache:
            self.ammo_cache[ammo_type] = []
        self.ammo_cache[ammo_type].append(ammo_id)
    
    def get_available_weapons_for_ship(self, ship_size, faction_id=None):
        """
        Get available weapons for a ship size, optionally filtered by faction.
        
        Args:
            ship_size: Size of the ship ('small', 'medium', 'large', 'capital')
            faction_id: Optional faction ID for faction-specific weapons
            
        Returns:
            dict: Dictionary with 'fixed', 'turret', and 'missile' weapon lists
        """
        if ship_size not in self.weapon_cache:
            return {'fixed': [], 'turret': [], 'missile': []}
        
        # For now, return all available weapons
        # TODO: Add faction-specific filtering if needed
        return self.weapon_cache[ship_size].copy()
    
    def select_primary_weapon(self, ship_size, preference='balanced'):
        """
        Select a primary weapon for an AI ship.
        
        Args:
            ship_size: Size of the ship
            preference: Weapon preference ('fixed', 'turret', 'missile', 'balanced')
            
        Returns:
            str: Weapon ID or None if no weapons available
        """
        available = self.get_available_weapons_for_ship(ship_size)
        
        if preference == 'balanced':
            # Try to get a mix of weapon types
            all_weapons = []
            all_weapons.extend(available['fixed'])
            all_weapons.extend(available['turret'])
            # Add missiles with lower probability for primary weapon
            all_weapons.extend(available['missile'][:len(available['missile'])//2])
        elif preference in available:
            all_weapons = available[preference]
        else:
            # Fallback to any available weapon
            all_weapons = []
            for weapon_list in available.values():
                all_weapons.extend(weapon_list)
        
        return random.choice(all_weapons) if all_weapons else None
    
    def select_secondary_weapon(self, ship_size, primary_weapon_id):
        """
        Select a secondary weapon that complements the primary weapon.
        
        Args:
            ship_size: Size of the ship
            primary_weapon_id: ID of the primary weapon
            
        Returns:
            str: Weapon ID or None if no suitable secondary weapon
        """
        available = self.get_available_weapons_for_ship(ship_size)
        primary_weapon = OUTFITS_REGISTRY.get(primary_weapon_id)
        
        if not primary_weapon:
            return None
        
        # Choose complementary weapon type
        if primary_weapon.mount_type == MOUNT_TYPE_FIXED:
            # If primary is fixed, prefer turret or missile as secondary
            candidates = available['turret'] + available['missile']
        elif primary_weapon.mount_type == MOUNT_TYPE_TURRET:
            # If primary is turret, prefer fixed or missile as secondary
            candidates = available['fixed'] + available['missile']
        else:
            # If primary is missile, prefer energy weapons
            candidates = available['fixed'] + available['turret']
        
        # Remove the primary weapon from candidates
        candidates = [w for w in candidates if w != primary_weapon_id]
        
        return random.choice(candidates) if candidates else None
    
    def select_ammo_for_weapon(self, weapon_id):
        """
        Select appropriate ammunition for a weapon.
        
        Args:
            weapon_id: ID of the weapon that needs ammo
            
        Returns:
            str: Ammo ID or None if no compatible ammo available
        """
        weapon = OUTFITS_REGISTRY.get(weapon_id)
        if not weapon or not weapon.uses_ammo:
            return None
        
        ammo_type = getattr(weapon, 'ammo_type', '')
        available_ammo = self.ammo_cache.get(ammo_type, [])
        
        return random.choice(available_ammo) if available_ammo else None
    
    def equip_ai_ship(self, ai_ship):
        """
        Equip an AI ship with appropriate weapons based on available outfits.
        
        Args:
            ai_ship: The AI ship to equip
        """
        ship_size = ai_ship.ship.size
        faction_id = ai_ship.faction_id
        
        # Refresh cache to ensure we have latest outfits
        self._refresh_weapon_cache()
        
        # Determine weapon loadout based on ship size and role
        loadout = self._determine_loadout(ship_size, faction_id)
        
        # Install weapons
        for weapon_role, weapon_id in loadout.items():
            if weapon_id:
                success = ai_ship.install_outfit(weapon_id)
                if success:
                    print(f"AI Ship: Equipped {weapon_id} as {weapon_role}")
                    
                    # Load ammo if needed
                    ammo_id = self.select_ammo_for_weapon(weapon_id)
                    if ammo_id:
                        ai_ship.load_ammo(ammo_id)
                        print(f"AI Ship: Loaded {ammo_id} for {weapon_id}")
                else:
                    print(f"AI Ship: Failed to equip {weapon_id}")
    
    def _determine_loadout(self, ship_size, faction_id):
        """
        Determine the weapon loadout for a ship based on size and faction.
        
        Args:
            ship_size: Size of the ship
            faction_id: Faction of the ship
            
        Returns:
            dict: Dictionary mapping weapon roles to weapon IDs
        """
        loadout = {}
        
        # Primary weapon (always try to equip)
        primary = self.select_primary_weapon(ship_size, 'balanced')
        if primary:
            loadout['primary'] = primary
        
        # Secondary weapon based on ship size
        if ship_size in ['medium', 'large', 'capital']:
            secondary = self.select_secondary_weapon(ship_size, primary)
            if secondary:
                loadout['secondary'] = secondary
        
        # Additional weapons for larger ships
        if ship_size in ['large', 'capital']:
            # Try to add a missile weapon if not already equipped
            available = self.get_available_weapons_for_ship(ship_size)
            missile_weapons = [w for w in available['missile'] 
                             if w not in loadout.values()]
            if missile_weapons and random.random() < 0.7:
                loadout['missile'] = random.choice(missile_weapons)
        
        if ship_size == 'capital':
            # Capital ships get even more weapons
            available = self.get_available_weapons_for_ship(ship_size)
            turret_weapons = [w for w in available['turret'] 
                            if w not in loadout.values()]
            if turret_weapons and random.random() < 0.8:
                loadout['turret'] = random.choice(turret_weapons)
        
        return loadout
    
    def get_fallback_weapons(self):
        """
        Get basic fallback weapons if no outfits are available.
        
        Returns:
            dict: Basic weapon definitions
        """
        return {
            'basic_laser': {
                'name': 'Basic Laser',
                'damage': 10,
                'fire_rate': 2.0,
                'range': 300,
                'mount_type': MOUNT_TYPE_FIXED
            }
        }

# Global weapon manager instance
AI_WEAPON_MANAGER = AIWeaponManager()
