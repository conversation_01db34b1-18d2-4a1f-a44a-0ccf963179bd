"""
Standardized UI Components for Outfit Editors
Eliminates code duplication across outfit editor classes
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox

class StandardParameterGrid:
    """Reusable parameter grid for common outfit properties."""
    
    def __init__(self, parent, title="Basic Properties"):
        self.frame = ttk.LabelFrame(parent, text=title)
        self.vars = {}
        self.row = 0
        
    def add_string_field(self, label, var_name, width=25, **kwargs):
        """Add a string entry field."""
        ttk.Label(self.frame, text=f"{label}:").grid(row=self.row, column=0, sticky=tk.W, padx=5, pady=2)
        self.vars[var_name] = tk.StringVar()
        ttk.Entry(self.frame, textvariable=self.vars[var_name], width=width, **kwargs).grid(
            row=self.row, column=1, padx=5, pady=2)
        self.row += 1
        
    def add_int_field(self, label, var_name, min_val=0, max_val=100000, **kwargs):
        """Add an integer spinbox field."""
        ttk.Label(self.frame, text=f"{label}:").grid(row=self.row, column=0, sticky=tk.W, padx=5, pady=2)
        self.vars[var_name] = tk.IntVar()
        ttk.Spinbox(self.frame, from_=min_val, to=max_val, textvariable=self.vars[var_name], 
                   width=10, **kwargs).grid(row=self.row, column=1, padx=5, pady=2)
        self.row += 1
        
    def add_float_field(self, label, var_name, min_val=0.0, max_val=100.0, increment=0.1, **kwargs):
        """Add a float spinbox field."""
        ttk.Label(self.frame, text=f"{label}:").grid(row=self.row, column=0, sticky=tk.W, padx=5, pady=2)
        self.vars[var_name] = tk.DoubleVar()
        ttk.Spinbox(self.frame, from_=min_val, to=max_val, increment=increment, 
                   textvariable=self.vars[var_name], width=10, **kwargs).grid(
                   row=self.row, column=1, padx=5, pady=2)
        self.row += 1
        
    def add_combo_field(self, label, var_name, values, **kwargs):
        """Add a combobox field."""
        ttk.Label(self.frame, text=f"{label}:").grid(row=self.row, column=0, sticky=tk.W, padx=5, pady=2)
        self.vars[var_name] = tk.StringVar()
        ttk.Combobox(self.frame, textvariable=self.vars[var_name], values=values, 
                    width=15, **kwargs).grid(row=self.row, column=1, padx=5, pady=2)
        self.row += 1
        
    def add_file_field(self, label, var_name, file_types=None, **kwargs):
        """Add a file browse field."""
        if file_types is None:
            file_types = [("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]
            
        ttk.Label(self.frame, text=f"{label}:").grid(row=self.row, column=0, sticky=tk.W, padx=5, pady=2)
        self.vars[var_name] = tk.StringVar()
        
        entry_frame = ttk.Frame(self.frame)
        entry_frame.grid(row=self.row, column=1, padx=5, pady=2, sticky=tk.W)
        
        ttk.Entry(entry_frame, textvariable=self.vars[var_name], width=20).pack(side=tk.LEFT)
        ttk.Button(entry_frame, text="Browse", 
                  command=lambda: self._browse_file(self.vars[var_name], file_types)).pack(side=tk.LEFT, padx=2)
        self.row += 1
        
    def _browse_file(self, var, file_types):
        """Browse for a file."""
        filename = filedialog.askopenfilename(filetypes=file_types)
        if filename:
            var.set(filename)
            
    def pack(self, **kwargs):
        """Pack the frame."""
        self.frame.pack(**kwargs)
        return self
        
    def grid(self, **kwargs):
        """Grid the frame.""" 
        self.frame.grid(**kwargs)
        return self

class ParameterLoader:
    """Handles loading and saving parameters with validation."""
    
    @staticmethod
    def load_outfit_parameters(outfit, parameter_grid):
        """Load outfit data into parameter grid variables."""
        for var_name, var in parameter_grid.vars.items():
            value = getattr(outfit, var_name, None)
            if value is not None:
                if isinstance(var, (tk.IntVar, tk.DoubleVar)):
                    var.set(value)
                else:
                    var.set(str(value))
                    
    @staticmethod
    def save_outfit_parameters(outfit, parameter_grid):
        """Save parameter grid variables to outfit object."""
        for var_name, var in parameter_grid.vars.items():
            if hasattr(outfit, var_name):
                setattr(outfit, var_name, var.get())
                
class ProjectileParameterGrid(StandardParameterGrid):
    """Specialized grid for projectile parameters."""
    
    def __init__(self, parent):
        super().__init__(parent, "Projectile Properties")
        self.setup_projectile_fields()
        
    def setup_projectile_fields(self):
        """Setup projectile-specific fields."""
        self.add_int_field("Speed", "projectile_speed", 0, 1000)
        self.add_string_field("Color (R,G,B)", "projectile_color", 15)
        self.add_string_field("Size (W,H)", "projectile_size", 15)
        self.add_combo_field("Behavior", "projectile_behavior", 
                           ["instant", "dumbfire", "guided", "beam", "delayed", "proximity"])
        self.add_float_field("Lifetime", "projectile_lifetime", 0.1, 30.0, 0.1)
        
class GuidanceParameterGrid(StandardParameterGrid):
    """Specialized grid for missile guidance parameters."""
    
    def __init__(self, parent):
        super().__init__(parent, "Guidance System")
        self.setup_guidance_fields()
        
    def setup_guidance_fields(self):
        """Setup guidance-specific fields."""
        self.add_float_field("Guidance Level", "guidance_level", 0.0, 1.0, 0.1)
        self.add_int_field("Turn Rate", "turn_rate", 0, 360)
        self.add_int_field("Acceleration", "acceleration", 0, 500)
        self.add_combo_field("Tracking Behavior", "tracking_behavior",
                           ["basic", "predictive", "heavy", "smart"])
