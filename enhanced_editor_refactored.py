"""
Escape Velocity Py - Enhanced Content Editor - REFACTORED VERSION
Main entry point for the modular content editor system

This is the new modular version of the enhanced editor that replaces the 
monolithic 3000+ line version with a clean, maintainable architecture.

The editor is now split into:
- DataManager: Handles all game data loading/saving
- MainWindow: Manages the main interface and tabs
- Individual editors for each outfit category and ships
- Base editor class for common functionality

This makes the code much easier to maintain, extend, and debug.

USAGE:
    python enhanced_editor_refactored.py

The modular structure is organized as follows:

editor_modules/
├── __init__.py
├── data_manager.py          # Handles all data operations
├── main_window.py           # Main application window
├── ship_editor.py           # Ship editing functionality
└── outfit_editors/
    ├── __init__.py
    ├── base_editor.py       # Base class for outfit editors
    ├── weapons_editor.py    # Weapons editing
    ├── ammunition_editor.py # Ammunition editing
    ├── defense_editor.py    # Defense outfits editing
    ├── engines_editor.py    # Engine outfits editing
    ├── electronics_editor.py # Electronics editing
    └── utility_editor.py    # Utility outfits editing

Benefits of this refactored approach:
1. Maintainability: Each module has a single responsibility
2. Extensibility: Easy to add new outfit categories or features
3. Debugging: Issues are isolated to specific modules
4. Code reuse: Base editor provides common functionality
5. Memory efficiency: Only load what's needed
6. Team development: Multiple developers can work on different modules

The original 3000+ line file was becoming unwieldy and difficult to maintain.
This modular approach provides the same functionality with better organization.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
from pathlib import Path

# Add the game's src directory to the path
game_src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(game_src_path))

def main():
    """Main function to run the enhanced editor."""
    try:
        # Import editor modules (done here to handle import errors gracefully)
        from editor_modules.main_window import MainEditorWindow
        from editor_modules.data_manager import DataManager
        
        # Initialize data manager
        data_manager = DataManager()
        
        # Create main window
        root = tk.Tk()
        app = MainEditorWindow(root, data_manager)
        
        # Start the application
        root.mainloop()
        
    except ImportError as e:
        # Handle case where modular files don't exist yet
        messagebox.showerror("Module Error", 
                           f"Editor modules not found: {e}\n\n"
                           "Please ensure the editor_modules directory and its files exist.\n"
                           "The modular editor structure should be in place.")
        print(f"Import Error: {e}")
        print("Please check that all editor module files are present.")
        
    except Exception as e:
        messagebox.showerror("Startup Error", f"Failed to start editor: {e}")
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
