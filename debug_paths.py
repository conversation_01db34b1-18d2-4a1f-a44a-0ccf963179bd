"""
Debug script to check paths and file existence for spritesheets.
"""

import os
import sys

def print_separator():
    print("-" * 80)

def main():
    # Print current working directory
    print("Current working directory:")
    print(os.getcwd())
    print_separator()
    
    # Print script directory
    print("Script directory:")
    print(os.path.dirname(os.path.abspath(__file__)))
    print_separator()
    
    # Check if the sprites directory exists
    sprites_path = os.path.join("assets", "images", "sprites")
    print(f"Checking if sprites directory exists: {sprites_path}")
    if os.path.exists(sprites_path):
        print(f"✓ Directory exists: {sprites_path}")
    else:
        print(f"✗ Directory not found: {sprites_path}")
        
        # Try with absolute path from script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        abs_sprites_path = os.path.join(script_dir, "assets", "images", "sprites")
        print(f"Trying absolute path: {abs_sprites_path}")
        if os.path.exists(abs_sprites_path):
            print(f"✓ Directory exists at absolute path: {abs_sprites_path}")
        else:
            print(f"✗ Directory not found at absolute path: {abs_sprites_path}")
            
            # Try going up one directory
            parent_dir = os.path.dirname(script_dir)
            parent_sprites_path = os.path.join(parent_dir, "assets", "images", "sprites")
            print(f"Trying parent directory: {parent_sprites_path}")
            if os.path.exists(parent_sprites_path):
                print(f"✓ Directory exists in parent directory: {parent_sprites_path}")
            else:
                print(f"✗ Directory not found in parent directory: {parent_sprites_path}")
    
    print_separator()
    
    # Check for ship spritesheets
    ship_sizes = ["small", "medium", "large", "capital"]
    for size in ship_sizes:
        ship_path = os.path.join(sprites_path, "ships", size)
        print(f"Checking ship directory: {ship_path}")
        if os.path.exists(ship_path):
            print(f"✓ Directory exists: {ship_path}")
            # List all files in the directory
            files = os.listdir(ship_path)
            print(f"Files in {ship_path}:")
            for file in files:
                print(f"  - {file}")
        else:
            print(f"✗ Directory not found: {ship_path}")
    
    print_separator()
    
    # Check for planet spritesheets
    planet_path = os.path.join(sprites_path, "planets")
    print(f"Checking planet directory: {planet_path}")
    if os.path.exists(planet_path):
        print(f"✓ Directory exists: {planet_path}")
        # List all files in the directory
        files = os.listdir(planet_path)
        print(f"Files in {planet_path}:")
        for file in files:
            print(f"  - {file}")
    else:
        print(f"✗ Directory not found: {planet_path}")
    
    print_separator()
    
    # Try to load a specific ship spritesheet
    ship_id = "scout"
    ship_size = "small"
    ship_sprite_path = os.path.join(sprites_path, "ships", ship_size, f"{ship_id}_spritesheet.png")
    print(f"Checking for ship spritesheet: {ship_sprite_path}")
    if os.path.exists(ship_sprite_path):
        print(f"✓ Ship spritesheet found: {ship_sprite_path}")
    else:
        print(f"✗ Ship spritesheet not found: {ship_sprite_path}")
        
        # Try with absolute path
        abs_ship_sprite_path = os.path.abspath(ship_sprite_path)
        print(f"Trying absolute path: {abs_ship_sprite_path}")
        if os.path.exists(abs_ship_sprite_path):
            print(f"✓ Ship spritesheet found at absolute path: {abs_ship_sprite_path}")
        else:
            print(f"✗ Ship spritesheet not found at absolute path: {abs_ship_sprite_path}")
    
    print_separator()
    
    # Print Python path
    print("Python path:")
    for path in sys.path:
        print(f"  - {path}")

if __name__ == "__main__":
    main()
