"""
Weapons Editor for the Enhanced Content Editor
Handles editing of weapon outfits
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from .base_editor import BaseOutfitEditor
from .ui_components import StandardParameterGrid, ProjectileParameterGrid, ParameterLoader
import os
from pathlib import Path

class WeaponsEditor(BaseOutfitEditor):
    """Editor for weapon outfits."""

    def __init__(self, parent, data_manager):
        super().__init__(parent, data_manager, "weapons")

    def setup_editor_ui(self, parent):
        """Setup the weapon editor interface."""
        # Create scrollable frame
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Basic Properties Grid
        self.basic_grid = StandardParameterGrid(scrollable_frame, "Basic Properties")
        self.basic_grid.add_string_field("Name", "name")
        self.basic_grid.add_int_field("Cost", "cost", 100, 100000)
        self.basic_grid.add_int_field("Space Required", "space_required", 1, 50)
        self.basic_grid.add_combo_field("Mount Type", "mount_type", ["fixed", "turret"])
        self.basic_grid.add_int_field("Min Tech Level", "min_tech_level", 1, 10)
        self.basic_grid.add_file_field("Outfitter Icon", "outfitter_icon")
        self.basic_grid.add_file_field("Outfitter Image", "outfitter_image")
        self.basic_grid.pack(fill=tk.X, padx=5, pady=5)

        # Weapon Type Selection
        type_frame = ttk.LabelFrame(scrollable_frame, text="Weapon Type")
        type_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.weapon_type_var = tk.StringVar(value="direct_fire")
        ttk.Radiobutton(type_frame, text="Direct Fire (Laser/Beam/Projectile)", 
                       variable=self.weapon_type_var, value="direct_fire",
                       command=self.on_weapon_type_change).pack(anchor=tk.W, padx=5, pady=2)
        ttk.Radiobutton(type_frame, text="Launcher (Missiles/Mass Driver)", 
                       variable=self.weapon_type_var, value="launcher",
                       command=self.on_weapon_type_change).pack(anchor=tk.W, padx=5, pady=2)

        # Sound Effects
        sound_frame = ttk.LabelFrame(scrollable_frame, text="Sound Effects")
        sound_frame.pack(fill=tk.X, padx=5, pady=5)
        
        sound_grid = ttk.Frame(sound_frame)
        sound_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(sound_grid, text="Fire Sound:").grid(row=0, column=0, sticky=tk.W)
        self.sound_var = tk.StringVar()
        self.sound_combo = ttk.Combobox(sound_grid, textvariable=self.sound_var, width=20)
        self.sound_combo.grid(row=0, column=1, padx=5)
        
        ttk.Button(sound_grid, text="Browse", command=self.browse_sound).grid(row=0, column=2, padx=2)
        ttk.Button(sound_grid, text="Preview", command=self.preview_sound).grid(row=0, column=3, padx=2)
        ttk.Button(sound_grid, text="Refresh", command=self.refresh_sound_list).grid(row=0, column=4, padx=2)

        # Combat Properties Grids (separate for launcher vs direct fire)
        self.direct_fire_combat_grid = StandardParameterGrid(scrollable_frame, "Combat Properties")
        self.direct_fire_combat_grid.add_int_field("Shield Damage", "shield_damage", 1, 1000)
        self.direct_fire_combat_grid.add_int_field("Armor Damage", "armor_damage", 1, 1000)
        self.direct_fire_combat_grid.add_float_field("Fire Rate", "fire_rate", 0.1, 20.0, 0.1)
        self.direct_fire_combat_grid.add_int_field("Range", "range", 50, 2000)
        self.direct_fire_combat_grid.add_float_field("Power Cost", "power_cost", 0.0, 200.0, 0.1)
        self.direct_fire_combat_grid.add_float_field("Energy Usage", "energy_usage", 0.0, 200.0, 0.1)
        self.direct_fire_combat_grid.add_float_field("Accuracy", "accuracy", 0.0, 1.0, 0.01)
        
        self.launcher_combat_grid = StandardParameterGrid(scrollable_frame, "Launcher Properties")
        self.launcher_combat_grid.add_float_field("Fire Rate", "fire_rate", 0.1, 20.0, 0.1)
        self.launcher_combat_grid.add_float_field("Power Cost", "power_cost", 0.0, 200.0, 0.1)
        self.launcher_combat_grid.add_float_field("Energy Usage", "energy_usage", 0.0, 200.0, 0.1)
        self.launcher_combat_grid.add_int_field("Max Ammo", "max_ammo", 1, 200)

        # Projectile Properties
        self.projectile_grid = ProjectileParameterGrid(scrollable_frame)

        # Save button
        save_frame = ttk.Frame(parent)
        save_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(save_frame, text="Save Weapon", command=self.save_item).pack(side=tk.RIGHT, padx=5)

        # Initialize
        self.refresh_sound_list()
        self.on_weapon_type_change()

    def on_weapon_type_change(self):
        """Show/hide UI based on weapon type."""
        is_launcher = self.weapon_type_var.get() == "launcher"
        
        if is_launcher:
            # Hide direct fire grid, show launcher grid
            self.direct_fire_combat_grid.frame.pack_forget()
            self.launcher_combat_grid.pack(fill=tk.X, padx=5, pady=5)
            if hasattr(self, 'projectile_grid'):
                self.projectile_grid.frame.pack_forget()
        else:
            # Show direct fire grid, hide launcher grid
            self.launcher_combat_grid.frame.pack_forget()
            self.direct_fire_combat_grid.pack(fill=tk.X, padx=5, pady=5)
            if hasattr(self, 'projectile_grid'):
                self.projectile_grid.pack(fill=tk.X, padx=5, pady=5)

    def load_item_into_editor(self, weapon):
        """Load weapon data into the editor."""
        super().load_item_into_editor(weapon)

        # Load using standardized parameter loader
        ParameterLoader.load_outfit_parameters(weapon, self.basic_grid)
        
        # Set weapon type first
        self.weapon_type_var.set("launcher" if getattr(weapon, 'uses_ammo', False) else "direct_fire")
        
        # Load appropriate combat grid based on weapon type
        if getattr(weapon, 'uses_ammo', False):
            ParameterLoader.load_outfit_parameters(weapon, self.launcher_combat_grid)
        else:
            ParameterLoader.load_outfit_parameters(weapon, self.direct_fire_combat_grid)
            ParameterLoader.load_outfit_parameters(weapon, self.projectile_grid)
        
        # Handle armor damage fallback
        if not hasattr(weapon, 'armor_damage'):
            if hasattr(self, 'direct_fire_combat_grid') and 'armor_damage' in self.direct_fire_combat_grid.vars:
                self.direct_fire_combat_grid.vars['armor_damage'].set(getattr(weapon, 'damage', 10))

        # Load sound effects
        self.sound_var.set(getattr(weapon, 'fire_sound', ''))

        # Update UI visibility
        self.on_weapon_type_change()

    def save_item(self):
        """Save the current weapon with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No weapon selected to save")
            return

        try:
            # Save using standardized parameter loader
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.basic_grid)
            
            # Set weapon type
            self.current_outfit.uses_ammo = (self.weapon_type_var.get() == "launcher")
            
            if self.weapon_type_var.get() == "launcher":
                # For launchers, save launcher properties
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.launcher_combat_grid)
                # Set default max_ammo if missing
                if not hasattr(self.current_outfit, 'max_ammo'):
                    self.current_outfit.max_ammo = 10
            else:
                # Save combat and projectile properties for direct-fire
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.direct_fire_combat_grid)
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.projectile_grid)

            # Update sound effects
            if hasattr(self.current_outfit, 'fire_sound'):
                self.current_outfit.fire_sound = self.sound_var.get()
            else:
                # Add fire_sound attribute if it doesn't exist
                self.current_outfit.fire_sound = self.sound_var.get()

            super().save_item()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save weapon: {e}")

    def create_new_item_instance(self, item_id):
        """Create a new weapon instance."""
        class SimpleWeapon:
            def __init__(self, id, name):
                # Basic properties
                self.id = id
                self.name = name
                self.category = "weapons"
                self.subcategory = "energy"
                self.cost = 1000
                self.space_required = 1
                self.mount_type = "fixed"
                self.min_tech_level = 1
                self.outfitter_icon = ""
                self.outfitter_image = ""
                # Combat properties
                self.shield_damage = 10
                self.armor_damage = 10
                self.fire_rate = 1.0
                self.range = 300
                self.power_cost = 5.0
                self.energy_usage = 1.0
                self.accuracy = 1.0
                # Projectile properties
                self.projectile_speed = 800
                self.beam_color = "255,0,0"
                self.beam_size = "8,2"
                self.projectile_behavior = "instant"
                self.tracking_strength = 0.0
                self.proximity_radius = 0
                self.delay_time = 0.0
                # Launcher properties
                self.uses_ammo = False
                self.ammo_type = ""
                self.max_ammo = 0
                # Other
                self.fire_sound = ""
                self.description = ""

        return SimpleWeapon(item_id, item_id.replace('_', ' ').title())

    def refresh_sound_list(self):
        """Refresh the list of available sound files."""
        try:
            sound_dir = Path("src/assets/soundfx/weapons")
            if sound_dir.exists():
                sounds = []
                for file_path in sound_dir.iterdir():
                    if file_path.is_file() and file_path.suffix.lower() in ['.mp3', '.wav', '.ogg']:
                        sounds.append(file_path.name)

                self.sound_combo['values'] = [''] + sorted(sounds)
            else:
                self.sound_combo['values'] = ['']
        except Exception as e:
            print(f"Error refreshing sound list: {e}")
            self.sound_combo['values'] = ['']

    def browse_sound(self):
        """Browse for a sound file."""
        try:
            sound_dir = Path("src/assets/soundfx/weapons")
            sound_dir.mkdir(parents=True, exist_ok=True)

            filename = filedialog.askopenfilename(
                title="Select Sound File",
                initialdir=str(sound_dir),
                filetypes=[
                    ("Audio files", "*.mp3 *.wav *.ogg"),
                    ("MP3 files", "*.mp3"),
                    ("WAV files", "*.wav"),
                    ("OGG files", "*.ogg"),
                    ("All files", "*.*")
                ]
            )

            if filename:
                # Get just the filename, not the full path
                sound_file = Path(filename).name
                self.sound_var.set(sound_file)
                self.refresh_sound_list()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to browse for sound: {e}")

    def preview_sound(self):
        """Preview the selected sound."""
        try:
            sound_file = self.sound_var.get()
            if not sound_file:
                messagebox.showwarning("Warning", "No sound file selected")
                return

            # Try to play the sound using pygame
            import pygame as pg

            if not pg.mixer.get_init():
                pg.mixer.init()

            sound_path = Path("src/assets/soundfx/weapons") / sound_file
            if sound_path.exists():
                sound = pg.mixer.Sound(str(sound_path))
                sound.play()
                messagebox.showinfo("Preview", f"Playing: {sound_file}")
            else:
                messagebox.showerror("Error", f"Sound file not found: {sound_file}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to preview sound: {e}")

    def browse_outfitter_image(self):
        """Browse for an outfitter image file."""
        try:
            image_dir = Path("src/assets/images/outfitters/weapons")
            image_dir.mkdir(parents=True, exist_ok=True)

            filename = filedialog.askopenfilename(
                title="Select Outfitter Image",
                initialdir=str(image_dir),
                filetypes=[
                    ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
                    ("PNG files", "*.png"),
                    ("JPEG files", "*.jpg *.jpeg"),
                    ("All files", "*.*")
                ]
            )

            if filename:
                # Get just the filename, not the full path
                image_file = Path(filename).name
                self.outfitter_image_var.set(image_file)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to browse for outfitter image: {e}")

    def preview_outfitter_image(self):
        """Preview the selected outfitter image."""
        image_filename = self.outfitter_image_var.get()
        if not image_filename:
            messagebox.showwarning("Warning", "No outfitter image selected to preview")
            return

        try:
            from PIL import Image, ImageTk

            image_path = Path("src/assets/images/outfitters/weapons") / image_filename
            if not image_path.exists():
                messagebox.showerror("Error", f"Image file not found: {image_filename}")
                return

            # Create preview window
            preview_window = tk.Toplevel()
            preview_window.title(f"Outfitter Image Preview: {image_filename}")
            preview_window.geometry("300x300")

            # Load and display the image
            image = Image.open(image_path)

            # Scale image if it's too large
            max_size = 250
            if image.width > max_size or image.height > max_size:
                image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)

            photo = ImageTk.PhotoImage(image)

            # Create label to display image
            image_label = ttk.Label(preview_window, image=photo)
            image_label.image = photo  # Keep a reference
            image_label.pack(pady=10)

            # Add info label
            info_text = f"File: {image_filename}\nDimensions: {image.width}x{image.height}"
            info_label = ttk.Label(preview_window, text=info_text, justify=tk.CENTER)
            info_label.pack(pady=5)

            # Add close button
            ttk.Button(preview_window, text="Close", command=preview_window.destroy).pack(pady=10)

        except ImportError:
            messagebox.showerror("Error", "PIL (Pillow) library is required for image preview.\nInstall with: pip install Pillow")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to preview image: {e}")