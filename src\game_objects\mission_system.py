"""
Mission System for Escape Velocity Py
Handles mission generation, tracking, and completion
"""
import pygame as pg
import random
from typing import Dict, List, Optional, Tuple

# Mission types
MISSION_TYPE_CARGO_DELIVERY = "cargo_delivery"

# Mission status
MISSION_STATUS_AVAILABLE = "available"
MISSION_STATUS_ACTIVE = "active"
MISSION_STATUS_COMPLETED = "completed"
MISSION_STATUS_FAILED = "failed"

# Colors for UI
BACKGROUND_COLOR = (20, 20, 30)
PANEL_COLOR = (40, 40, 60)
TEXT_COLOR = (220, 220, 220)
TITLE_COLOR = (255, 255, 100)
BUTTON_COLOR = (60, 60, 100)
BUTTON_HOVER_COLOR = (80, 80, 120)
BUTTON_TEXT_COLOR = (255, 255, 255)
ACCEPT_COLOR = (100, 255, 100)
DECLINE_COLOR = (255, 100, 100)

class Mission:
    """Base mission class"""
    def __init__(self, mission_id: str, mission_type: str, title: str, description: str,
                 reward: int, origin_system: str, origin_planet: str):
        self.mission_id = mission_id
        self.mission_type = mission_type
        self.title = title
        self.description = description
        self.reward = reward
        self.origin_system = origin_system
        self.origin_planet = origin_planet
        self.status = MISSION_STATUS_AVAILABLE
        self.accepted_time = None

class CargoDeliveryMission(Mission):
    """Cargo delivery mission"""
    def __init__(self, mission_id: str, title: str, description: str, reward: int,
                 origin_system: str, origin_planet: str, destination_system: str,
                 destination_planet: str, cargo_type: str, cargo_amount: int, cargo_space: int):
        super().__init__(mission_id, MISSION_TYPE_CARGO_DELIVERY, title, description,
                        reward, origin_system, origin_planet)
        self.destination_system = destination_system
        self.destination_planet = destination_planet
        self.cargo_type = cargo_type
        self.cargo_amount = cargo_amount
        self.cargo_space = cargo_space  # Space required in cargo hold
        self.cargo_loaded = False

class MissionSystem:
    """Main mission system manager"""
    def __init__(self, game):
        self.game = game
        self.available_missions: List[Mission] = []
        self.active_missions: List[Mission] = []
        self.completed_missions: List[Mission] = []
        self.mission_counter = 0

        # UI state
        self.selected_mission_index = 0
        self.scroll_offset = 0
        self.max_visible_missions = 8

        # Cargo types for missions
        self.cargo_types = [
            "Medical Supplies", "Food Rations", "Electronics", "Machinery Parts",
            "Rare Minerals", "Scientific Equipment", "Luxury Goods", "Industrial Components",
            "Agricultural Products", "Energy Cells", "Communication Devices", "Raw Materials"
        ]

    def generate_missions_for_planet(self, planet, system_id: str) -> None:
        """Generate random missions for a planet"""
        if planet.tech_level < 1:
            return

        # Generate 1-3 missions per planet
        num_missions = random.randint(1, 3)

        for _ in range(num_missions):
            if random.random() < 0.8:  # 80% chance for cargo delivery missions
                mission = self._generate_cargo_delivery_mission(planet, system_id)
                if mission:
                    self.available_missions.append(mission)

    def _generate_cargo_delivery_mission(self, origin_planet, origin_system_id: str) -> Optional[CargoDeliveryMission]:
        """Generate a cargo delivery mission"""
        # Find a suitable destination system
        available_systems = [sys_id for sys_id in self.game.galaxy_systems.keys()
                           if sys_id != origin_system_id]

        if not available_systems:
            return None

        destination_system_id = random.choice(available_systems)
        destination_system = self.game.galaxy_systems[destination_system_id]

        # Calculate distance for reward scaling
        origin_pos = self.game.galaxy_systems[origin_system_id].pos
        dest_pos = destination_system.pos
        distance = origin_pos.distance_to(dest_pos)

        # Calculate number of jumps (rough estimate)
        jumps = max(1, int(distance / 100))  # Rough jump calculation

        # Generate mission details
        cargo_type = random.choice(self.cargo_types)
        cargo_amount = random.randint(1, 5)
        cargo_space = cargo_amount * random.randint(1, 3)  # Space per unit varies

        # Base reward scaled by distance and cargo
        base_reward = 500 + (jumps * 200) + (cargo_space * 50)
        reward = int(base_reward * random.uniform(0.8, 1.2))

        # Generate destination planet name
        destination_planet_name = f"{destination_system.name} Station"

        mission_id = f"cargo_{self.mission_counter}"
        self.mission_counter += 1

        title = f"Deliver {cargo_type}"
        description = (f"Transport {cargo_amount} units of {cargo_type} "
                      f"to {destination_planet_name} in the {destination_system.name} system. "
                      f"Cargo space required: {cargo_space} tons. "
                      f"Estimated jumps: {jumps}")

        mission = CargoDeliveryMission(
            mission_id, title, description, reward,
            origin_system_id, origin_planet.name,
            destination_system_id, destination_planet_name,
            cargo_type, cargo_amount, cargo_space
        )

        return mission

    def show_mission_board(self, screen, planet) -> str:
        """Show the mission board interface"""
        # Generate missions if none exist for this planet
        if not self.available_missions:
            self.generate_missions_for_planet(planet, self.game.current_system_id)

        waiting_at_mission_board = True
        while waiting_at_mission_board and self.game.running:
            self.game.clock.tick(self.game.FPS)

            for event in pg.event.get():
                if event.type == pg.QUIT:
                    self.game.running = False
                    return "QUIT"
                elif event.type == pg.KEYDOWN:
                    if event.key == pg.K_ESCAPE:
                        return "DOCKED"
                    elif event.key == pg.K_UP:
                        self.selected_mission_index = max(0, self.selected_mission_index - 1)
                        self._update_scroll()
                    elif event.key == pg.K_DOWN:
                        self.selected_mission_index = min(len(self.available_missions) - 1,
                                                        self.selected_mission_index + 1)
                        self._update_scroll()
                    elif event.key == pg.K_RETURN or event.key == pg.K_a:
                        if self.available_missions:
                            self._accept_mission(self.available_missions[self.selected_mission_index])
                            return "DOCKED"
                elif event.type == pg.MOUSEBUTTONDOWN:
                    if event.button == 1:  # Left click
                        mouse_pos = pg.mouse.get_pos()
                        self._handle_mouse_click(mouse_pos)

            self._draw_mission_board(screen, planet)
            pg.display.flip()

        return "DOCKED"

    def _update_scroll(self):
        """Update scroll offset based on selected mission"""
        if self.selected_mission_index < self.scroll_offset:
            self.scroll_offset = self.selected_mission_index
        elif self.selected_mission_index >= self.scroll_offset + self.max_visible_missions:
            self.scroll_offset = self.selected_mission_index - self.max_visible_missions + 1

    def _draw_mission_board(self, screen, planet):
        """Draw the mission board interface"""
        screen.fill(BACKGROUND_COLOR)

        # Title
        self.game.draw_text("Mission Board", 48, TITLE_COLOR,
                           screen.get_width() // 2, 30, align="center")
        self.game.draw_text(f"{planet.name} - {planet.faction}", 24, TEXT_COLOR,
                           screen.get_width() // 2, 70, align="center")

        # Instructions
        self.game.draw_text("Use UP/DOWN arrows to select, ENTER/A to accept, ESC to exit",
                           18, TEXT_COLOR, screen.get_width() // 2, 100, align="center")

        # Mission list panel
        panel_rect = pg.Rect(50, 130, screen.get_width() - 100, screen.get_height() - 200)
        pg.draw.rect(screen, PANEL_COLOR, panel_rect)
        pg.draw.rect(screen, TEXT_COLOR, panel_rect, 2)

        if not self.available_missions:
            self.game.draw_text("No missions available", 32, TEXT_COLOR,
                               panel_rect.centerx, panel_rect.centery, align="center")
            return

        # Draw missions
        visible_missions = self.available_missions[self.scroll_offset:self.scroll_offset + self.max_visible_missions]

        for i, mission in enumerate(visible_missions):
            y_pos = panel_rect.top + 20 + i * 80
            mission_rect = pg.Rect(panel_rect.left + 10, y_pos, panel_rect.width - 20, 75)

            # Highlight selected mission
            if i + self.scroll_offset == self.selected_mission_index:
                pg.draw.rect(screen, BUTTON_HOVER_COLOR, mission_rect)

            pg.draw.rect(screen, TEXT_COLOR, mission_rect, 1)

            # Mission title
            self.game.draw_text(mission.title, 24, TITLE_COLOR,
                               mission_rect.left + 10, mission_rect.top + 5, align="topleft")

            # Mission description (truncated)
            desc_lines = self._wrap_text(mission.description, 60)
            for j, line in enumerate(desc_lines[:2]):  # Show max 2 lines
                self.game.draw_text(line, 16, TEXT_COLOR,
                                   mission_rect.left + 10, mission_rect.top + 30 + j * 18, align="topleft")

            # Reward
            self.game.draw_text(f"Reward: {mission.reward} credits", 18, ACCEPT_COLOR,
                               mission_rect.right - 10, mission_rect.top + 5, align="topright")

    def _wrap_text(self, text: str, max_chars: int) -> List[str]:
        """Wrap text to fit within character limit"""
        words = text.split()
        lines = []
        current_line = ""

        for word in words:
            if len(current_line + word) <= max_chars:
                current_line += word + " "
            else:
                if current_line:
                    lines.append(current_line.strip())
                current_line = word + " "

        if current_line:
            lines.append(current_line.strip())

        return lines

    def _handle_mouse_click(self, mouse_pos):
        """Handle mouse clicks on the mission board"""
        # Simple implementation - could be expanded for more interactive UI
        pass

    def _accept_mission(self, mission: Mission):
        """Accept a mission"""
        if isinstance(mission, CargoDeliveryMission):
            # Check if player has enough cargo space
            current_cargo = sum(self.game.player.cargo.values())
            available_space = self.game.player.cargo_space - current_cargo

            if available_space < mission.cargo_space:
                self.game.set_status_message(f"Not enough cargo space! Need {mission.cargo_space} tons.",
                                           DECLINE_COLOR, 180)
                return

            # Load cargo
            cargo_key = f"mission_{mission.mission_id}"
            self.game.player.cargo[cargo_key] = mission.cargo_space
            mission.cargo_loaded = True

        # Move mission to active list
        self.available_missions.remove(mission)
        mission.status = MISSION_STATUS_ACTIVE
        self.active_missions.append(mission)

        self.game.set_status_message(f"Mission accepted: {mission.title}", ACCEPT_COLOR, 180)

    def check_mission_completion(self, planet, system_id: str):
        """Check if any active missions can be completed at this planet"""
        for mission in self.active_missions[:]:  # Copy list to avoid modification during iteration
            if isinstance(mission, CargoDeliveryMission):
                # Check if this is the destination system for the mission
                if mission.destination_system == system_id:
                    # For simplicity, any planet in the destination system can complete the mission
                    # In a more complex system, you might want specific planet matching
                    self._complete_cargo_mission(mission)

    def _complete_cargo_mission(self, mission: CargoDeliveryMission):
        """Complete a cargo delivery mission"""
        # Remove cargo from player
        cargo_key = f"mission_{mission.mission_id}"
        if cargo_key in self.game.player.cargo:
            del self.game.player.cargo[cargo_key]

        # Give reward
        self.game.player.credits += mission.reward

        # Move to completed missions
        self.active_missions.remove(mission)
        mission.status = MISSION_STATUS_COMPLETED
        self.completed_missions.append(mission)

        self.game.set_status_message(f"Mission completed! Received {mission.reward} credits.",
                                   ACCEPT_COLOR, 180)

    def get_mission_markers(self) -> List[Tuple[str, str]]:
        """Get list of systems that have active mission destinations"""
        markers = []
        for mission in self.active_missions:
            if isinstance(mission, CargoDeliveryMission):
                markers.append((mission.destination_system, mission.destination_planet))
        return markers
