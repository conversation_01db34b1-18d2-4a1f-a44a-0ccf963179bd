"""
Image Loader for Outfitter and Shipyard Images
Handles loading and caching of outfit and ship images for display in game interfaces
"""

import pygame as pg
from pathlib import Path
import os

class ImageLoader:
    """Handles loading and caching of images for outfitters and shipyard."""
    
    def __init__(self):
        self.image_cache = {}
        self.default_size = (64, 64)  # Default size for outfit images
        self.ship_default_size = (200, 150)  # Default size for ship images
        
        # Set up image directories
        self.outfitter_dirs = {
            'weapons': Path('src/assets/images/outfitters/weapons'),
            'ammunition': Path('src/assets/images/outfitters/ammunition'),
            'defense': Path('src/assets/images/outfitters/defense'),
            'electronics': Path('src/assets/images/outfitters/electronics'),
            'utility': Path('src/assets/images/outfitters/utility')
        }
        self.shipyard_dir = Path('src/assets/images/shipyard')
    
    def load_outfit_image(self, outfit, target_size=None):
        """
        Load an outfit image for display in the outfitter.
        
        Args:
            outfit: The outfit object
            target_size: Tuple (width, height) for scaling, uses default if None
            
        Returns:
            pygame.Surface or None if image not found/loadable
        """
        if target_size is None:
            target_size = self.default_size
        
        # Get image filename from outfit
        image_filename = getattr(outfit, 'outfitter_image', '')
        if not image_filename:
            return None
        
        # Create cache key
        cache_key = f"outfit_{outfit.category}_{image_filename}_{target_size[0]}x{target_size[1]}"
        
        # Return cached image if available
        if cache_key in self.image_cache:
            return self.image_cache[cache_key]
        
        # Determine category directory
        category = getattr(outfit, 'category', 'weapons')
        image_dir = self.outfitter_dirs.get(category, self.outfitter_dirs['weapons'])
        
        # Try to load the image
        image_path = image_dir / image_filename
        if not image_path.exists():
            print(f"Outfit image not found: {image_path}")
            return None
        
        try:
            # Load and scale the image
            image = pg.image.load(str(image_path))
            if image.get_size() != target_size:
                image = pg.transform.scale(image, target_size)
            
            # Cache the image
            self.image_cache[cache_key] = image
            
            print(f"Loaded outfit image: {image_path}")
            return image
            
        except Exception as e:
            print(f"Failed to load outfit image {image_path}: {e}")
            return None
    
    def load_ship_image(self, ship, target_size=None):
        """
        Load a ship image for display in the shipyard.
        
        Args:
            ship: The ship object
            target_size: Tuple (width, height) for scaling, uses default if None
            
        Returns:
            pygame.Surface or None if image not found/loadable
        """
        if target_size is None:
            target_size = self.ship_default_size
        
        # Get image filename from ship
        image_filename = getattr(ship, 'shipyard_display_image', '')
        if not image_filename:
            return None
        
        # Create cache key
        cache_key = f"ship_{image_filename}_{target_size[0]}x{target_size[1]}"
        
        # Return cached image if available
        if cache_key in self.image_cache:
            return self.image_cache[cache_key]
        
        # Try to load the image
        image_path = self.shipyard_dir / image_filename
        if not image_path.exists():
            print(f"Ship image not found: {image_path}")
            return None
        
        try:
            # Load and scale the image
            image = pg.image.load(str(image_path))
            if image.get_size() != target_size:
                image = pg.transform.scale(image, target_size)
            
            # Cache the image
            self.image_cache[cache_key] = image
            
            print(f"Loaded ship image: {image_path}")
            return image
            
        except Exception as e:
            print(f"Failed to load ship image {image_path}: {e}")
            return None
    
    def create_placeholder_image(self, size, text="No Image", color=(128, 128, 128)):
        """
        Create a placeholder image when no image is available.
        
        Args:
            size: Tuple (width, height)
            text: Text to display on placeholder
            color: Background color
            
        Returns:
            pygame.Surface
        """
        cache_key = f"placeholder_{size[0]}x{size[1]}_{text}_{color}"
        
        if cache_key in self.image_cache:
            return self.image_cache[cache_key]
        
        # Create placeholder surface
        surface = pg.Surface(size)
        surface.fill(color)
        
        # Add border
        pg.draw.rect(surface, (64, 64, 64), surface.get_rect(), 2)
        
        # Add text if pygame font is available
        try:
            font = pg.font.Font(None, min(size[0] // 8, size[1] // 4, 24))
            text_surface = font.render(text, True, (255, 255, 255))
            text_rect = text_surface.get_rect(center=(size[0] // 2, size[1] // 2))
            surface.blit(text_surface, text_rect)
        except:
            pass  # Skip text if font not available
        
        # Cache the placeholder
        self.image_cache[cache_key] = surface
        
        return surface
    
    def get_outfit_image_or_placeholder(self, outfit, target_size=None):
        """
        Get outfit image or create placeholder if not available.
        
        Args:
            outfit: The outfit object
            target_size: Tuple (width, height) for scaling
            
        Returns:
            pygame.Surface (either loaded image or placeholder)
        """
        if target_size is None:
            target_size = self.default_size
        
        image = self.load_outfit_image(outfit, target_size)
        if image is None:
            # Create placeholder with outfit name
            outfit_name = getattr(outfit, 'name', 'Unknown')
            image = self.create_placeholder_image(target_size, outfit_name[:10])
        
        return image
    
    def get_ship_image_or_placeholder(self, ship, target_size=None):
        """
        Get ship image or create placeholder if not available.
        
        Args:
            ship: The ship object
            target_size: Tuple (width, height) for scaling
            
        Returns:
            pygame.Surface (either loaded image or placeholder)
        """
        if target_size is None:
            target_size = self.ship_default_size
        
        image = self.load_ship_image(ship, target_size)
        if image is None:
            # Create placeholder with ship name
            ship_name = getattr(ship, 'name', 'Unknown Ship')
            image = self.create_placeholder_image(target_size, ship_name[:15])
        
        return image
    
    def clear_cache(self):
        """Clear the image cache to free memory."""
        self.image_cache.clear()
        print("Image cache cleared")
    
    def get_cache_info(self):
        """Get information about the current cache."""
        return {
            'cached_images': len(self.image_cache),
            'cache_keys': list(self.image_cache.keys())
        }
