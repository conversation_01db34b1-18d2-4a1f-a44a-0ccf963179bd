"""
Shipyard module for Escape Velocity Py.
This module handles the shipyard interface for buying and selling ships.
"""

import sys
import os

# If this script is run directly, add the 'src' directory to sys.path
if __name__ == "__main__":
    # Get the absolute path of the current script
    current_script_path = os.path.abspath(__file__)
    # Navigate up two levels to get to the 'src' directory
    # (from game_objects/shipyard.py to game_objects/ to src/)
    src_dir = os.path.dirname(os.path.dirname(current_script_path))
    if src_dir not in sys.path:
        sys.path.insert(0, src_dir)

import pygame as pg
from game_objects.ships import SHIPS, get_ship_by_id
from game_objects.outfits import get_outfit_by_id as get_outfit_definition_by_id # Import for outfit details

# UI Constants (can be shared or customized)
BACKGROUND_COLOR = (20, 20, 40)
PANEL_COLOR = (30, 30, 50)
TITLE_COLOR = (220, 220, 255)
TEXT_COLOR = (200, 200, 200)
HIGHLIGHT_COLOR = (100, 100, 160)
SELECTED_COLOR = (80, 80, 140)
BUTTON_COLOR = (60, 60, 100)
BUTTON_HOVER_COLOR = (80, 80, 120)
BUTTON_TEXT_COLOR = (220, 220, 220)
BUTTON_DISABLED_COLOR = (40, 40, 60)
BUTTON_DISABLED_TEXT_COLOR = (120, 120, 120)

STAT_GOOD_COLOR = (100, 255, 100)
STAT_BAD_COLOR = (255, 100, 100)
STAT_NEUTRAL_COLOR = (200, 200, 200)

class Shipyard:
    """Shipyard interface for buying and selling ships."""

    def __init__(self, game):
        """Initialize the shipyard."""
        self.game = game
        self.available_ships_for_purchase = []
        self.player_ships_for_sale = [] # For selling from fleet
        self.selected_ship_for_purchase = None
        self.selected_player_ship_for_sale = None
        
        self.scroll_offset_purchase = 0
        self.scroll_offset_sell = 0
        self.max_items_per_page = 8 # Adjust as needed
        
        self.current_tab = "buy"  # "buy", "sell", "fleet" (fleet later)

        # UI element rects
        self.tab_buy_rect = None
        self.tab_sell_rect = None
        # self.tab_fleet_rect = None # For later
        self.back_button_rect = None
        self.purchase_list_rects = []
        self.sell_list_rects = []
        self.buy_action_button_rect = None
        self.sell_action_button_rect = None
        # Pagination buttons might be needed

    def _get_available_ships(self):
        """Get ships available for purchase at the current planet."""
        if not self.game.docked_planet:
            return []
        
        planet_tech_level = self.game.docked_planet.tech_level
        available = []
        for ship_id, ship_template in SHIPS.items():
            if ship_template.min_tech_level <= planet_tech_level:
                # TODO: Add faction restrictions or other availability logic here if needed
                available.append(ship_template)
        
        # Sort by tech level, then by name
        available.sort(key=lambda s: (s.min_tech_level, s.name))
        return available

    def _get_player_fleet_ships(self):
        """Get ships the player owns (current ship + fleet ships)."""
        player = self.game.player
        owned_ship_templates = []
        if player and player.ship:
            # Add current ship (it's already a template from SHIPS)
            if player.ship not in owned_ship_templates:
                owned_ship_templates.append(player.ship)
            
            # Add ships from the fleet
            # Assuming player.fleet_ships stores ship objects (templates) directly for now
            for fleet_ship_template in player.fleet_ships:
                if fleet_ship_template not in owned_ship_templates:
                    owned_ship_templates.append(fleet_ship_template)
        
        # Sort for consistent display, e.g., by name
        owned_ship_templates.sort(key=lambda s: s.name)
        return owned_ship_templates


    def show_shipyard_screen(self, screen):
        """Show the shipyard screen."""
        self.available_ships_for_purchase = self._get_available_ships()
        self.player_ships_for_sale = self._get_player_fleet_ships() # Initially just current ship
        self.selected_ship_for_purchase = None
        self.selected_player_ship_for_sale = None
        self.scroll_offset_purchase = 0
        self.scroll_offset_sell = 0

        running = True
        while running and self.game.running:
            self.game.clock.tick(self.game.FPS)
            mouse_pos = pg.mouse.get_pos()

            for event in pg.event.get():
                if event.type == pg.QUIT:
                    self.game.running = False
                    running = False
                if event.type == pg.KEYDOWN:
                    if event.key == pg.K_ESCAPE:
                        running = False
                if event.type == pg.MOUSEBUTTONDOWN and event.button == 1:
                    if self.back_button_rect and self.back_button_rect.collidepoint(mouse_pos):
                        running = False
                    
                    if self.tab_buy_rect and self.tab_buy_rect.collidepoint(mouse_pos):
                        self.current_tab = "buy"
                        self.selected_ship_for_purchase = None
                        self.selected_player_ship_for_sale = None
                    elif self.tab_sell_rect and self.tab_sell_rect.collidepoint(mouse_pos):
                        self.current_tab = "sell"
                        self.selected_ship_for_purchase = None
                        self.selected_player_ship_for_sale = None

                    if self.current_tab == "buy":
                        for i, rect in enumerate(self.purchase_list_rects):
                            if rect.collidepoint(mouse_pos):
                                actual_index = i + self.scroll_offset_purchase
                                if 0 <= actual_index < len(self.available_ships_for_purchase):
                                    self.selected_ship_for_purchase = self.available_ships_for_purchase[actual_index]
                                    self.selected_player_ship_for_sale = None
                                break
                        if self.buy_action_button_rect and self.buy_action_button_rect.collidepoint(mouse_pos):
                            if self.selected_ship_for_purchase:
                                self._buy_ship(self.selected_ship_for_purchase)
                                # Refresh lists after buying
                                self.available_ships_for_purchase = self._get_available_ships()
                                self.player_ships_for_sale = self._get_player_fleet_ships()


                    elif self.current_tab == "sell":
                        for i, rect in enumerate(self.sell_list_rects):
                            if rect.collidepoint(mouse_pos):
                                actual_index = i + self.scroll_offset_sell
                                if 0 <= actual_index < len(self.player_ships_for_sale):
                                    self.selected_player_ship_for_sale = self.player_ships_for_sale[actual_index]
                                    self.selected_ship_for_purchase = None
                                break
                        if self.sell_action_button_rect and self.sell_action_button_rect.collidepoint(mouse_pos):
                            if self.selected_player_ship_for_sale:
                                self._sell_ship(self.selected_player_ship_for_sale)
                                # Refresh lists
                                self.available_ships_for_purchase = self._get_available_ships()
                                self.player_ships_for_sale = self._get_player_fleet_ships()
                                self.selected_player_ship_for_sale = None


            screen.fill(BACKGROUND_COLOR)
            self._draw_ui(screen, mouse_pos)
            pg.display.flip()

        return "DOCKED" # Or whatever state it should return to

    def _buy_ship(self, ship_to_buy):
        # Placeholder for buy logic
        player = self.game.player
        cost_of_new_ship = ship_to_buy.cost
        trade_in_value = 0

        if player.ship: # If player has a current ship
            trade_in_value = player.ship.cost // 2 # Simple trade-in value
            # TODO: Add value of outfits on old ship? For now, outfits are lost/sold separately.

        final_cost = cost_of_new_ship - trade_in_value

        if player.credits >= final_cost:
            player.credits -= final_cost
            
            # Store the old ship (template) in the fleet
            if player.ship: # If there was an old ship
                # TODO: Handle outfits on the old ship more gracefully (transfer/sell options)
                # For now, outfits are sold automatically.
                sold_outfits_value = 0
                for outfit_id_on_old_ship, count in player.outfits.items():
                    outfit_def = get_outfit_definition_by_id(outfit_id_on_old_ship)
                    if outfit_def:
                        sold_outfits_value += (outfit_def.cost // 2) * count
                player.credits += sold_outfits_value
                if sold_outfits_value > 0:
                     self.game.set_status_message(f"Sold outfits from {player.ship.name} for {sold_outfits_value} cr.", STAT_NEUTRAL_COLOR, duration=120)

                # Add the old ship (template) to the fleet
                # Ensure it's the template, not a live instance with runtime state
                old_ship_template_id = None
                for sid, s_template in SHIPS.items():
                    if s_template.name == player.ship.name:
                        old_ship_template_id = sid
                        break
                if old_ship_template_id:
                    player.fleet_ships.append(SHIPS[old_ship_template_id]) 
                    # TODO: Consider fleet limits

            # Set the new ship as active
            # ship_to_buy is already a template from SHIPS
            player.ship = ship_to_buy 
            player.ship_name = ship_to_buy.name
            
            # Reset player's outfits and stats for the new ship
            player.outfits = {} 
            player.used_outfit_space = 0
            
            # Update base stats from the new ship
            player.base_acceleration = player.ship.acceleration
            player.base_turn_rate = player.ship.turn_rate
            player.base_max_speed = player.ship.max_speed
            player.max_shields = player.ship.shields
            player.shields = player.ship.shields
            player.max_armor = player.ship.armor
            player.armor = player.ship.armor
            player.cargo_space = player.ship.cargo_space
            player.outfit_space = player.ship.outfit_space
            
            player._apply_outfit_effects() # This will clear and rebuild self.weapons

            self.game.set_status_message(f"Purchased {player.ship.name}! (Net Cost: {final_cost} cr)", STAT_GOOD_COLOR)
            self.selected_ship_for_purchase = None 
            
            # Refresh lists as player's fleet has changed for the sell tab
            self.player_ships_for_sale = self._get_player_fleet_ships()

        else:
            self.game.set_status_message(f"Not enough credits! Need {final_cost} cr (after trade-in).", STAT_BAD_COLOR)


    def _sell_ship(self, ship_to_sell):
        # Selling ships requires more complex fleet management.
        # For now, this function will be very limited or disabled.
        player = self.game.player

        # Can't sell the last ship if no fleet system exists to switch to another.
        if player.ship.name == ship_to_sell.name and not player.fleet_ships:
            self.game.set_status_message("Cannot sell your only ship without a replacement or fleet.", STAT_BAD_COLOR)
            return

        # If selling the current ship, and there are fleet ships, one needs to be made active.
        # This logic is complex and deferred.
        if player.ship.name == ship_to_sell.name and player.fleet_ships:
            self.game.set_status_message("Fleet management not yet implemented to switch active ship.", STAT_BAD_COLOR)
            return

        # Placeholder: if selling a ship from fleet (not yet implemented in UI properly)
        # ship_id_to_sell = None
        # for sid, s_template in SHIPS.items():
        #     if s_template.name == ship_to_sell.name:
        #         ship_id_to_sell = sid
        #         break
        # if ship_id_to_sell in player.fleet_ships:
        #     player.fleet_ships.remove(ship_id_to_sell)
        #     sell_price = ship_to_sell.cost // 2
        #     player.credits += sell_price
        #     self.game.set_status_message(f"Sold {ship_to_sell.name} from fleet for {sell_price} cr.", STAT_GOOD_COLOR)
        #     self.selected_player_ship_for_sale = None
        # else:
        #     self.game.set_status_message(f"Cannot sell {ship_to_sell.name} at this time.", STAT_BAD_COLOR)
        
        self.game.set_status_message(f"Selling ships from fleet not fully implemented.", STAT_BAD_COLOR)


    def _draw_ui(self, screen, mouse_pos):
        width, height = screen.get_width(), screen.get_height()
        self.game.draw_text(f"Shipyard - {self.game.docked_planet.name}", 36, TITLE_COLOR, width // 2, 30, align="center")
        self.game.draw_text(f"Credits: {self.game.player.credits}", 20, TEXT_COLOR, width - 30, 30, align="topright")

        # Tabs
        tab_y_pos = 70
        tab_width = 120
        tab_height = 30
        self.tab_buy_rect = pg.Rect(50, tab_y_pos, tab_width, tab_height)
        self.tab_sell_rect = pg.Rect(50 + tab_width + 10, tab_y_pos, tab_width, tab_height)
        # self.tab_fleet_rect = pg.Rect(50 + (tab_width + 10) * 2, tab_y_pos, tab_width, tab_height)

        pg.draw.rect(screen, BUTTON_HOVER_COLOR if self.tab_buy_rect.collidepoint(mouse_pos) and self.current_tab != "buy" else (SELECTED_COLOR if self.current_tab == "buy" else BUTTON_COLOR), self.tab_buy_rect)
        self.game.draw_text("Buy Ships", 20, BUTTON_TEXT_COLOR, self.tab_buy_rect.centerx, self.tab_buy_rect.centery, align="center")
        pg.draw.rect(screen, BUTTON_HOVER_COLOR if self.tab_sell_rect.collidepoint(mouse_pos) and self.current_tab != "sell" else (SELECTED_COLOR if self.current_tab == "sell" else BUTTON_COLOR), self.tab_sell_rect)
        self.game.draw_text("Sell Ships", 20, BUTTON_TEXT_COLOR, self.tab_sell_rect.centerx, self.tab_sell_rect.centery, align="center")
        # pg.draw.rect(screen, BUTTON_COLOR, self.tab_fleet_rect) # Later
        # self.game.draw_text("Manage Fleet", 20, BUTTON_TEXT_COLOR, self.tab_fleet_rect.centerx, self.tab_fleet_rect.centery, align="center")


        # Main content area
        list_panel_rect = pg.Rect(50, tab_y_pos + tab_height + 20, (width - 100) * 0.4, height - (tab_y_pos + tab_height + 20) - 100)
        details_panel_rect = pg.Rect(list_panel_rect.right + 10, tab_y_pos + tab_height + 20, (width - 100) * 0.6 - 10, height - (tab_y_pos + tab_height + 20) - 100)
        
        pg.draw.rect(screen, PANEL_COLOR, list_panel_rect)
        pg.draw.rect(screen, TEXT_COLOR, list_panel_rect, 1)
        pg.draw.rect(screen, PANEL_COLOR, details_panel_rect)
        pg.draw.rect(screen, TEXT_COLOR, details_panel_rect, 1)

        if self.current_tab == "buy":
            self._draw_ship_list(screen, list_panel_rect, self.available_ships_for_purchase, self.scroll_offset_purchase, "purchase")
            if self.selected_ship_for_purchase:
                self._draw_ship_details(screen, details_panel_rect, self.selected_ship_for_purchase, "buy")
        elif self.current_tab == "sell":
            self._draw_ship_list(screen, list_panel_rect, self.player_ships_for_sale, self.scroll_offset_sell, "sell")
            if self.selected_player_ship_for_sale:
                self._draw_ship_details(screen, details_panel_rect, self.selected_player_ship_for_sale, "sell")

        # Back button
        self.back_button_rect = pg.Rect(width - 120, height - 60, 100, 40)
        pg.draw.rect(screen, BUTTON_HOVER_COLOR if self.back_button_rect.collidepoint(mouse_pos) else BUTTON_COLOR, self.back_button_rect)
        self.game.draw_text("Back (Esc)", 18, BUTTON_TEXT_COLOR, self.back_button_rect.centerx, self.back_button_rect.centery, align="center")

    def _draw_ship_list(self, screen, panel_rect, ships_list, scroll_offset, list_type):
        item_height = 40
        start_y = panel_rect.top + 10
        
        current_list_rects = []

        for i in range(self.max_items_per_page):
            actual_index = i + scroll_offset
            if actual_index < len(ships_list):
                ship = ships_list[actual_index]
                item_rect = pg.Rect(panel_rect.left + 5, start_y + i * item_height, panel_rect.width - 10, item_height - 2)
                
                is_selected = False
                if list_type == "purchase" and self.selected_ship_for_purchase == ship:
                    is_selected = True
                elif list_type == "sell" and self.selected_player_ship_for_sale == ship:
                    is_selected = True

                pg.draw.rect(screen, SELECTED_COLOR if is_selected else HIGHLIGHT_COLOR, item_rect)
                self.game.draw_text(f"{ship.name} ({ship.ship_class})", 18, TEXT_COLOR, item_rect.left + 10, item_rect.centery, align="left")
                self.game.draw_text(f"{ship.cost} cr", 18, STAT_NEUTRAL_COLOR, item_rect.right - 10, item_rect.centery, align="right")
                current_list_rects.append(item_rect)
            else:
                break # No more items to display
        
        if list_type == "purchase":
            self.purchase_list_rects = current_list_rects
        elif list_type == "sell":
            self.sell_list_rects = current_list_rects
        
        # TODO: Add scrollbar or up/down buttons if list exceeds max_items_per_page

    def _draw_ship_details(self, screen, panel_rect, ship, mode): # mode is "buy" or "sell"
        self.game.draw_text(ship.name, 28, TITLE_COLOR, panel_rect.centerx, panel_rect.top + 30, align="center")
        
        y_offset = panel_rect.top + 70
        line_h = 22

        stats_to_draw = [
            (f"Class: {ship.ship_class.capitalize()}", TEXT_COLOR),
            (f"Size: {ship.size.capitalize()}", TEXT_COLOR),
            (f"Tech Level Req: {ship.min_tech_level}", TEXT_COLOR),
            (f"Cost: {ship.cost} cr" if mode == "buy" else f"Sell Value: {ship.cost // 2} cr", STAT_NEUTRAL_COLOR),
            (f"Hull: {ship.armor}", STAT_GOOD_COLOR),
            (f"Shields: {ship.shields}", STAT_GOOD_COLOR),
            (f"Outfit Space: {ship.outfit_space} tons", STAT_NEUTRAL_COLOR),
            (f"Cargo Space: {ship.cargo_space} tons", STAT_NEUTRAL_COLOR),
            (f"Max Speed: {ship.max_speed:.1f}", STAT_GOOD_COLOR),
            (f"Acceleration: {ship.acceleration:.2f}", STAT_GOOD_COLOR),
            (f"Turn Rate: {ship.turn_rate:.2f}", STAT_GOOD_COLOR),
        ]

        for i, (text, color) in enumerate(stats_to_draw):
            self.game.draw_text(text, 18, color, panel_rect.left + 20, y_offset + i * line_h, align="topleft")

        desc_rect = pg.Rect(panel_rect.left + 15, y_offset + len(stats_to_draw) * line_h + 10, panel_rect.width - 30, 100)
        self.game.draw_text_wrapped(ship.description, 16, TEXT_COLOR, desc_rect)

        # Buy/Sell Action Button
        button_text = "Buy Ship" if mode == "buy" else "Sell This Ship"
        action_button_rect = pg.Rect(panel_rect.centerx - 100, panel_rect.bottom - 60, 200, 40)
        
        can_act = False
        if mode == "buy":
            self.buy_action_button_rect = action_button_rect
            can_act = self.game.player.credits >= ship.cost
        elif mode == "sell":
            self.sell_action_button_rect = action_button_rect
            # Prevent selling last ship (simple check for now)
            can_act = not (len(self.player_ships_for_sale) <= 1 and self.game.player.ship.name == ship.name)


        btn_color = BUTTON_COLOR if can_act else BUTTON_DISABLED_COLOR
        txt_color = BUTTON_TEXT_COLOR if can_act else BUTTON_DISABLED_TEXT_COLOR
        
        pg.draw.rect(screen, btn_color, action_button_rect)
        self.game.draw_text(button_text, 20, txt_color, action_button_rect.centerx, action_button_rect.centery, align="center")

        if mode == "buy" and not can_act:
            self.game.draw_text("Not enough credits", 16, STAT_BAD_COLOR, action_button_rect.centerx, action_button_rect.bottom + 15, align="center")
        elif mode == "sell" and not can_act:
             self.game.draw_text("Cannot sell last ship", 16, STAT_BAD_COLOR, action_button_rect.centerx, action_button_rect.bottom + 15, align="center")
