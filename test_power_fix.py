#!/usr/bin/env python3
"""
Test script to verify power system fixes are working
"""
import sys
import os

# Add src directory to path
src_path = os.path.join(os.path.dirname(__file__), "src")
sys.path.insert(0, src_path)

try:
    # Import the ship data loader
    from game_objects.ship_data_loader import load_ships_from_data_files, SHIPS_REGISTRY
    
    print("🔍 Testing Power System Fix...")
    print("=" * 50)
    
    # Load ships from data files
    loaded_count = load_ships_from_data_files()
    print(f"Loaded {loaded_count} ships from JSON")
    
    # Test the scout ship that should have your edited values
    if 'scout' in SHIPS_REGISTRY:
        scout = SHIPS_REGISTRY['scout']
        print(f"\n🚀 Scout Ship Power Properties:")
        print(f"  Power Capacity: {scout.power_capacity} (should be 75)")
        print(f"  Power Regen Rate: {scout.power_regen_rate} (should be 10.0)")
        print(f"  Thruster Power Cost: {scout.thruster_power_cost} (should be 1.0)")
        print(f"  Weapon Power Cost Base: {scout.weapon_power_cost_base}")
        print(f"  Shield Regen Power Cost: {scout.shield_regen_power_cost}")
        
        # Check if values match your edits
        if (scout.power_capacity == 75 and 
            scout.power_regen_rate == 10.0 and 
            scout.thruster_power_cost == 1.0):
            print("\n✅ SUCCESS: Power values are loading from JSON!")
        else:
            print(f"\n❌ PROBLEM: Power values don't match JSON data")
            
    else:
        print("❌ Scout ship not found in registry")
        
    # Test sprite information
    try:
        from game_objects.ship_sprite_mapper import SHIP_SPRITE_MAPPER
        scout_sprites = SHIP_SPRITE_MAPPER.get_sprite_metadata_for_ship('scout', 'small')
        print(f"\n🎨 Scout Sprite Info:")
        print(f"  Game Sprite: {scout_sprites['game_sprite']}")
        print(f"  Sprite Size: {scout_sprites['sprite_size']}")
        print(f"  Animation Frames: {scout_sprites['animation_frames']}")
        
        if scout_sprites['game_sprite']:
            print("✅ Sprite system working")
        else:
            print("⚠️  No sprite found for scout")
            
    except Exception as e:
        print(f"❌ Sprite system error: {e}")
        
    print("\n" + "=" * 50)
    print("Test complete! If you see ✅ SUCCESS, the fix is working.")
    
except Exception as e:
    print(f"❌ Error running test: {e}")
    import traceback
    traceback.print_exc()
