"""
Commodities system for Escape Velocity Py.
This module contains all tradable goods and their properties.
"""

import random

# Rarity levels
RARITY_COMMON = "common"
RARITY_UNCOMMON = "uncommon"
RARITY_RARE = "rare"
RARITY_VERY_RARE = "very_rare"
RARITY_EXOTIC = "exotic"

# Commodity categories
CATEGORY_FOOD = "food"
CATEGORY_MINERALS = "minerals"
CATEGORY_METALS = "metals"
CATEGORY_INDUSTRIAL = "industrial"
CATEGORY_LUXURY = "luxury"
CATEGORY_TECHNOLOGY = "technology"
CATEGORY_MEDICAL = "medical"
CATEGORY_ILLEGAL = "illegal"

class Commodity:
    """Base class for all tradable commodities."""
    
    def __init__(self, name, category, rarity, base_price, price_variance, 
                 min_tech_level, illegal=False, description=""):
        """
        Initialize a commodity with its properties.
        
        Args:
            name (str): The name of the commodity
            category (str): The category of the commodity
            rarity (str): Rarity level (common, uncommon, rare, very_rare, exotic)
            base_price (int): Base price per unit in credits
            price_variance (float): How much the price can vary (0.0-1.0)
            min_tech_level (int): Minimum tech level required to trade this commodity
            illegal (bool): Whether this commodity is illegal
            description (str): Description of the commodity
        """
        self.name = name
        self.category = category
        self.rarity = rarity
        self.base_price = base_price
        self.price_variance = price_variance
        self.min_tech_level = min_tech_level
        self.illegal = illegal
        self.description = description
    
    def get_price_at_system(self, system_id, faction_id, tech_level, is_buying=True):
        """
        Calculate the price of this commodity at a specific system.
        
        Args:
            system_id (str): The ID of the system
            faction_id (str): The faction controlling the system
            tech_level (int): The tech level of the system
            is_buying (bool): Whether the system is buying (True) or selling (False)
            
        Returns:
            int: The price of the commodity
        """
        # Use system_id as a seed for consistent pricing
        random.seed(f"{system_id}_{self.name}")
        
        # Base price adjustment based on tech level
        tech_modifier = 1.0
        if tech_level < self.min_tech_level:
            # If tech level is too low, price is much higher (if available at all)
            tech_modifier = 1.5
        elif tech_level > self.min_tech_level + 2:
            # If tech level is much higher, price is lower for low-tech items
            if self.category in [CATEGORY_FOOD, CATEGORY_MINERALS, CATEGORY_METALS]:
                tech_modifier = 0.8
        
        # Rarity affects price
        rarity_modifier = {
            RARITY_COMMON: 1.0,
            RARITY_UNCOMMON: 1.2,
            RARITY_RARE: 1.5,
            RARITY_VERY_RARE: 2.0,
            RARITY_EXOTIC: 3.0
        }.get(self.rarity, 1.0)
        
        # Faction preferences (could be expanded)
        faction_modifier = 1.0
        if faction_id == "pirates" and self.illegal:
            faction_modifier = 0.7  # Pirates offer better prices for illegal goods
        elif faction_id == "federation" and self.illegal:
            faction_modifier = 1.5  # Federation charges more for illegal goods
        
        # Buy/sell difference
        trade_modifier = 0.8 if is_buying else 1.2  # Systems buy low, sell high
        
        # Random variance
        variance = random.uniform(1.0 - self.price_variance, 1.0 + self.price_variance)
        
        # Calculate final price
        price = int(self.base_price * tech_modifier * rarity_modifier * faction_modifier * trade_modifier * variance)
        
        # Reset random seed
        random.seed()
        
        return max(1, price)  # Ensure price is at least 1 credit
    
    def __str__(self):
        return f"{self.name} ({self.category}, {self.rarity})"


# Define all commodities
COMMODITIES = {
    # Food and Agricultural Products
    "grain": Commodity(
        name="Grain",
        category=CATEGORY_FOOD,
        rarity=RARITY_COMMON,
        base_price=10,
        price_variance=0.2,
        min_tech_level=0,
        description="Basic food staple grown on agricultural worlds."
    ),
    
    "livestock": Commodity(
        name="Livestock",
        category=CATEGORY_FOOD,
        rarity=RARITY_COMMON,
        base_price=25,
        price_variance=0.3,
        min_tech_level=0,
        description="Live animals for food production or breeding."
    ),
    
    "fruits": Commodity(
        name="Exotic Fruits",
        category=CATEGORY_FOOD,
        rarity=RARITY_UNCOMMON,
        base_price=40,
        price_variance=0.4,
        min_tech_level=1,
        description="Rare and exotic fruits from various worlds."
    ),
    
    "spices": Commodity(
        name="Spices",
        category=CATEGORY_FOOD,
        rarity=RARITY_RARE,
        base_price=80,
        price_variance=0.5,
        min_tech_level=1,
        description="Rare spices used in cooking and medicine."
    ),
    
    "luxury_food": Commodity(
        name="Luxury Food",
        category=CATEGORY_LUXURY,
        rarity=RARITY_VERY_RARE,
        base_price=150,
        price_variance=0.4,
        min_tech_level=3,
        description="Gourmet food items for the wealthy."
    ),
    
    # Minerals and Raw Materials
    "water": Commodity(
        name="Water",
        category=CATEGORY_MINERALS,
        rarity=RARITY_COMMON,
        base_price=5,
        price_variance=0.5,
        min_tech_level=0,
        description="Clean water, essential for life and industry."
    ),
    
    "ore": Commodity(
        name="Common Ore",
        category=CATEGORY_MINERALS,
        rarity=RARITY_COMMON,
        base_price=15,
        price_variance=0.3,
        min_tech_level=0,
        description="Basic metal ores used in construction."
    ),
    
    "rare_minerals": Commodity(
        name="Rare Minerals",
        category=CATEGORY_MINERALS,
        rarity=RARITY_RARE,
        base_price=100,
        price_variance=0.4,
        min_tech_level=2,
        description="Uncommon minerals used in advanced manufacturing."
    ),
    
    "gemstones": Commodity(
        name="Gemstones",
        category=CATEGORY_LUXURY,
        rarity=RARITY_VERY_RARE,
        base_price=200,
        price_variance=0.6,
        min_tech_level=1,
        description="Precious gems used in jewelry and some industrial applications."
    ),
    
    # Metals
    "steel": Commodity(
        name="Steel",
        category=CATEGORY_METALS,
        rarity=RARITY_COMMON,
        base_price=30,
        price_variance=0.2,
        min_tech_level=1,
        description="Refined metal used in construction and manufacturing."
    ),
    
    "aluminum": Commodity(
        name="Aluminum",
        category=CATEGORY_METALS,
        rarity=RARITY_UNCOMMON,
        base_price=45,
        price_variance=0.3,
        min_tech_level=2,
        description="Lightweight metal used in aerospace and packaging."
    ),
    
    "titanium": Commodity(
        name="Titanium",
        category=CATEGORY_METALS,
        rarity=RARITY_RARE,
        base_price=120,
        price_variance=0.3,
        min_tech_level=3,
        description="Strong, lightweight metal used in high-end manufacturing."
    ),
    
    "precious_metals": Commodity(
        name="Precious Metals",
        category=CATEGORY_METALS,
        rarity=RARITY_VERY_RARE,
        base_price=250,
        price_variance=0.5,
        min_tech_level=2,
        description="Gold, silver, platinum and other valuable metals."
    ),
    
    # Industrial Goods
    "industrial_parts": Commodity(
        name="Industrial Parts",
        category=CATEGORY_INDUSTRIAL,
        rarity=RARITY_COMMON,
        base_price=50,
        price_variance=0.2,
        min_tech_level=2,
        description="Standard components used in manufacturing."
    ),
    
    "machinery": Commodity(
        name="Machinery",
        category=CATEGORY_INDUSTRIAL,
        rarity=RARITY_UNCOMMON,
        base_price=80,
        price_variance=0.3,
        min_tech_level=2,
        description="Complex machines for industrial use."
    ),
    
    "construction_materials": Commodity(
        name="Construction Materials",
        category=CATEGORY_INDUSTRIAL,
        rarity=RARITY_COMMON,
        base_price=35,
        price_variance=0.2,
        min_tech_level=1,
        description="Materials used in building and infrastructure."
    ),
    
    "chemicals": Commodity(
        name="Industrial Chemicals",
        category=CATEGORY_INDUSTRIAL,
        rarity=RARITY_UNCOMMON,
        base_price=70,
        price_variance=0.4,
        min_tech_level=2,
        description="Chemicals used in manufacturing and research."
    ),
    
    # Technology
    "electronics": Commodity(
        name="Electronics",
        category=CATEGORY_TECHNOLOGY,
        rarity=RARITY_UNCOMMON,
        base_price=90,
        price_variance=0.3,
        min_tech_level=3,
        description="Consumer and industrial electronic components."
    ),
    
    "computers": Commodity(
        name="Computers",
        category=CATEGORY_TECHNOLOGY,
        rarity=RARITY_RARE,
        base_price=150,
        price_variance=0.3,
        min_tech_level=4,
        description="Computing systems for various applications."
    ),
    
    "ai_systems": Commodity(
        name="AI Systems",
        category=CATEGORY_TECHNOLOGY,
        rarity=RARITY_VERY_RARE,
        base_price=300,
        price_variance=0.4,
        min_tech_level=5,
        description="Advanced artificial intelligence systems."
    ),
    
    "quantum_components": Commodity(
        name="Quantum Components",
        category=CATEGORY_TECHNOLOGY,
        rarity=RARITY_EXOTIC,
        base_price=500,
        price_variance=0.5,
        min_tech_level=5,
        description="Cutting-edge quantum technology components."
    ),
    
    # Medical
    "medical_supplies": Commodity(
        name="Medical Supplies",
        category=CATEGORY_MEDICAL,
        rarity=RARITY_UNCOMMON,
        base_price=60,
        price_variance=0.3,
        min_tech_level=2,
        description="Basic medical equipment and supplies."
    ),
    
    "pharmaceuticals": Commodity(
        name="Pharmaceuticals",
        category=CATEGORY_MEDICAL,
        rarity=RARITY_RARE,
        base_price=120,
        price_variance=0.4,
        min_tech_level=3,
        description="Medicines and drugs for treating various conditions."
    ),
    
    "advanced_medicines": Commodity(
        name="Advanced Medicines",
        category=CATEGORY_MEDICAL,
        rarity=RARITY_VERY_RARE,
        base_price=250,
        price_variance=0.5,
        min_tech_level=4,
        description="Cutting-edge medical treatments and therapies."
    ),
    
    # Luxury Goods
    "textiles": Commodity(
        name="Fine Textiles",
        category=CATEGORY_LUXURY,
        rarity=RARITY_UNCOMMON,
        base_price=70,
        price_variance=0.3,
        min_tech_level=1,
        description="High-quality fabrics and clothing."
    ),
    
    "artwork": Commodity(
        name="Artwork",
        category=CATEGORY_LUXURY,
        rarity=RARITY_RARE,
        base_price=180,
        price_variance=0.6,
        min_tech_level=2,
        description="Valuable art pieces from across the galaxy."
    ),
    
    "luxury_goods": Commodity(
        name="Luxury Goods",
        category=CATEGORY_LUXURY,
        rarity=RARITY_VERY_RARE,
        base_price=220,
        price_variance=0.5,
        min_tech_level=3,
        description="High-end consumer products for the wealthy."
    ),
    
    # Illegal Goods
    "contraband": Commodity(
        name="Contraband",
        category=CATEGORY_ILLEGAL,
        rarity=RARITY_UNCOMMON,
        base_price=100,
        price_variance=0.5,
        min_tech_level=1,
        illegal=True,
        description="Illegal goods of various types."
    ),
    
    "weapons": Commodity(
        name="Illegal Weapons",
        category=CATEGORY_ILLEGAL,
        rarity=RARITY_RARE,
        base_price=200,
        price_variance=0.6,
        min_tech_level=2,
        illegal=True,
        description="Weapons sold on the black market."
    ),
    
    "exotic_substances": Commodity(
        name="Exotic Substances",
        category=CATEGORY_ILLEGAL,
        rarity=RARITY_VERY_RARE,
        base_price=300,
        price_variance=0.7,
        min_tech_level=3,
        illegal=True,
        description="Rare and illegal substances with various effects."
    ),
}

def get_commodity_by_name(name):
    """Get a commodity by its name (case insensitive)."""
    for commodity_id, commodity in COMMODITIES.items():
        if commodity.name.lower() == name.lower():
            return commodity
    return None

def get_commodity_by_id(commodity_id):
    """Get a commodity by its ID."""
    return COMMODITIES.get(commodity_id)

def get_commodities_by_category(category):
    """Get all commodities of a specific category."""
    return [commodity for commodity in COMMODITIES.values() if commodity.category == category]

def get_commodities_by_rarity(rarity):
    """Get all commodities of a specific rarity."""
    return [commodity for commodity in COMMODITIES.values() if commodity.rarity == rarity]

def get_available_commodities(tech_level, include_illegal=False):
    """Get all commodities available at a specific tech level."""
    return [commodity for commodity in COMMODITIES.values() 
            if commodity.min_tech_level <= tech_level and (include_illegal or not commodity.illegal)]
