"""
Image Manager for Enhanced Content Editor
Handles outfitter and shipyard image selection and preview functionality
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from pathlib import Path

class ImageManager:
    """Manages image selection and preview for outfitters and shipyard."""
    
    @staticmethod
    def get_image_directory(category):
        """Get the appropriate image directory for a category."""
        category_dirs = {
            'weapons': 'src/assets/images/outfitters/weapons',
            'ammunition': 'src/assets/images/outfitters/ammunition',
            'defense': 'src/assets/images/outfitters/defense',
            'electronics': 'src/assets/images/outfitters/electronics',
            'utility': 'src/assets/images/outfitters/utility',
            'ships': 'src/assets/images/shipyard'
        }
        return Path(category_dirs.get(category, 'src/assets/images'))
    
    @staticmethod
    def browse_outfitter_image(category, var):
        """Browse for an outfitter image file."""
        try:
            image_dir = ImageManager.get_image_directory(category)
            image_dir.mkdir(parents=True, exist_ok=True)
            
            filename = filedialog.askopenfilename(
                title=f"Select {category.title()} Image",
                initialdir=str(image_dir),
                filetypes=[
                    ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
                    ("PNG files", "*.png"),
                    ("JPEG files", "*.jpg *.jpeg"),
                    ("All files", "*.*")
                ]
            )
            
            if filename:
                # Get just the filename, not the full path
                image_file = Path(filename).name
                var.set(image_file)
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to browse for {category} image: {e}")
    
    @staticmethod
    def preview_outfitter_image(category, image_filename, parent_window=None):
        """Preview the selected outfitter image."""
        if not image_filename:
            messagebox.showwarning("Warning", f"No {category} image selected to preview")
            return
        
        try:
            from PIL import Image, ImageTk
            
            image_path = ImageManager.get_image_directory(category) / image_filename
            if not image_path.exists():
                messagebox.showerror("Error", f"Image file not found: {image_filename}")
                return
            
            # Create preview window
            if parent_window:
                preview_window = tk.Toplevel(parent_window)
            else:
                preview_window = tk.Toplevel()
            preview_window.title(f"{category.title()} Image Preview: {image_filename}")
            preview_window.geometry("350x350")
            
            # Load and display the image
            image = Image.open(image_path)
            
            # Scale image if it's too large
            max_size = 300
            if image.width > max_size or image.height > max_size:
                image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
            
            photo = ImageTk.PhotoImage(image)
            
            # Create label to display image
            image_label = ttk.Label(preview_window, image=photo)
            image_label.image = photo  # Keep a reference
            image_label.pack(pady=10)
            
            # Add info label
            info_text = f"File: {image_filename}\nCategory: {category.title()}\nDimensions: {image.width}x{image.height}"
            info_label = ttk.Label(preview_window, text=info_text, justify=tk.CENTER)
            info_label.pack(pady=5)
            
            # Add close button
            ttk.Button(preview_window, text="Close", command=preview_window.destroy).pack(pady=10)
            
        except ImportError:
            messagebox.showerror("Error", "PIL (Pillow) library is required for image preview.\nInstall with: pip install Pillow")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to preview image: {e}")
    
    @staticmethod
    def add_outfitter_image_section(parent, category, image_var, label_text="Outfitter Image:"):
        """Add an outfitter image selection section to a frame."""
        # Outfitter Image
        image_frame = ttk.Frame(parent)
        image_frame.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(image_frame, text=label_text).grid(row=0, column=0, sticky=tk.W)
        ttk.Entry(image_frame, textvariable=image_var, width=25).grid(row=0, column=1, padx=5)
        ttk.Button(image_frame, text="Browse", 
                  command=lambda: ImageManager.browse_outfitter_image(category, image_var)).grid(row=0, column=2, padx=2)
        ttk.Button(image_frame, text="Preview", 
                  command=lambda: ImageManager.preview_outfitter_image(category, image_var.get())).grid(row=0, column=3, padx=2)
        
        return image_frame
    
    @staticmethod
    def add_shipyard_image_section(parent, image_var, label_text="Shipyard Image:"):
        """Add a shipyard image selection section to a frame."""
        return ImageManager.add_outfitter_image_section(parent, 'ships', image_var, label_text)

class SoundManager:
    """Manages sound file selection and preview functionality."""
    
    @staticmethod
    def get_sound_directory(category):
        """Get the appropriate sound directory for a category."""
        category_dirs = {
            'weapons': 'src/assets/soundfx/weapons',
            'engines': 'src/assets/soundfx/engines',
            'ambient': 'src/assets/soundfx/ambient'
        }
        return Path(category_dirs.get(category, 'src/assets/soundfx'))
    
    @staticmethod
    def refresh_sound_list(category, combo_widget):
        """Refresh the list of available sound files."""
        try:
            sound_dir = SoundManager.get_sound_directory(category)
            if sound_dir.exists():
                sounds = []
                for file_path in sound_dir.iterdir():
                    if file_path.is_file() and file_path.suffix.lower() in ['.mp3', '.wav', '.ogg']:
                        sounds.append(file_path.name)
                
                combo_widget['values'] = [''] + sorted(sounds)
            else:
                combo_widget['values'] = ['']
        except Exception as e:
            print(f"Error refreshing sound list: {e}")
            combo_widget['values'] = ['']
    
    @staticmethod
    def browse_sound(category, var, combo_widget=None):
        """Browse for a sound file."""
        try:
            sound_dir = SoundManager.get_sound_directory(category)
            sound_dir.mkdir(parents=True, exist_ok=True)
            
            filename = filedialog.askopenfilename(
                title=f"Select {category.title()} Sound",
                initialdir=str(sound_dir),
                filetypes=[
                    ("Audio files", "*.mp3 *.wav *.ogg"),
                    ("MP3 files", "*.mp3"),
                    ("WAV files", "*.wav"),
                    ("OGG files", "*.ogg"),
                    ("All files", "*.*")
                ]
            )
            
            if filename:
                # Get just the filename, not the full path
                sound_file = Path(filename).name
                var.set(sound_file)
                if combo_widget:
                    SoundManager.refresh_sound_list(category, combo_widget)
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to browse for {category} sound: {e}")
    
    @staticmethod
    def preview_sound(category, sound_filename):
        """Preview the selected sound."""
        if not sound_filename:
            messagebox.showwarning("Warning", f"No {category} sound selected to preview")
            return
        
        try:
            import pygame as pg
            
            if not pg.mixer.get_init():
                pg.mixer.init()
            
            sound_path = SoundManager.get_sound_directory(category) / sound_filename
            if sound_path.exists():
                sound = pg.mixer.Sound(str(sound_path))
                sound.play()
                messagebox.showinfo("Preview", f"Playing: {sound_filename}")
            else:
                messagebox.showerror("Error", f"Sound file not found: {sound_filename}")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to preview sound: {e}")
    
    @staticmethod
    def add_sound_section(parent, category, sound_var, combo_widget=None, label_text="Sound Effect:"):
        """Add a sound selection section to a frame."""
        sound_frame = ttk.LabelFrame(parent, text="Sound Effects")
        sound_frame.pack(fill=tk.X, padx=5, pady=5)
        
        sound_grid = ttk.Frame(sound_frame)
        sound_grid.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(sound_grid, text=label_text).grid(row=0, column=0, sticky=tk.W)
        
        if combo_widget is None:
            combo_widget = ttk.Combobox(sound_grid, textvariable=sound_var, width=20)
        combo_widget.grid(row=0, column=1, padx=5)
        
        ttk.Button(sound_grid, text="Browse", 
                  command=lambda: SoundManager.browse_sound(category, sound_var, combo_widget)).grid(row=0, column=2, padx=2)
        ttk.Button(sound_grid, text="Preview", 
                  command=lambda: SoundManager.preview_sound(category, sound_var.get())).grid(row=0, column=3, padx=2)
        ttk.Button(sound_grid, text="Refresh", 
                  command=lambda: SoundManager.refresh_sound_list(category, combo_widget)).grid(row=0, column=4, padx=2)
        
        # Initialize sound list
        SoundManager.refresh_sound_list(category, combo_widget)
        
        return sound_frame, combo_widget
