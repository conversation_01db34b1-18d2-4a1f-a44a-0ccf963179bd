"""
AI Constants - All AI-related constants in one place
Extracted from ai_core.py and other AI files for consistency
"""

# === AI SHIP CONSTANTS ===
AI_DRAG_FACTOR = 0.015
AI_SENSOR_RANGE = 400
AI_PATROL_RADIUS = 300
AI_WEAPON_RANGE = 300

# === STATE CONSTANTS ===
AI_STATE_IDLE = "IDLE"
AI_STATE_PATROLLING = "PATROLLING"
AI_STATE_ATTACKING = "ATTACKING"
AI_STATE_FLEEING = "FLEEING"
AI_STATE_TRADING = "TRADING"
AI_STATE_DISABLED = "DISABLED"

# === COMBAT SUB-STATES ===
COMBAT_STATE_ENGAGING = "ENGAGING"
COMBAT_STATE_PURSUING = "PURSUING"
COMBAT_STATE_FLANKING = "FLANKING"
COMBAT_STATE_RETREATING = "RETREATING"
COMBAT_STATE_MISSILE_ATTACK = "MISSILE_ATTACK"

# === PERSONALITY TYPES ===
AI_PERSONALITY_AGGRESSIVE = "aggressive"
AI_PERSONALITY_DEFENSIVE = "defensive"
AI_PERSONALITY_BALANCED = "balanced"
AI_PERSONALITY_COWARD = "coward"

# === WEAPON ENGAGEMENT RANGES ===
FIXED_WEAPON_OPTIMAL_RANGE = 150
TURRET_WEAPON_OPTIMAL_RANGE = 200
MISSILE_WEAPON_OPTIMAL_RANGE = 300

# === TIMING CONSTANTS ===
SHIELD_RECHARGE_DELAY = 180  # 3 seconds at 60 FPS
TARGET_SCAN_INTERVAL = 30    # 0.5 seconds at 60 FPS
STATE_TRANSITION_COOLDOWN = 180  # 3 seconds minimum between major state changes

# === DISABLE LOGIC - STANDARDIZED ===
DISABLE_THRESHOLD = 0.15  # Ship disabled when armor ≤ 15% AND shields = 0
FLEE_THRESHOLD_DEFAULT = 0.3  # Default flee when health ≤ 30%

# === FLEE THRESHOLDS BY PERSONALITY ===
FLEE_THRESHOLDS = {
    AI_PERSONALITY_AGGRESSIVE: 0.15,  # Fight to 15% health
    AI_PERSONALITY_DEFENSIVE: 0.5,    # Flee at 50% health  
    AI_PERSONALITY_BALANCED: 0.3,     # Flee at 30% health
    AI_PERSONALITY_COWARD: 0.7        # Flee at 70% health
}

# === FLEE THRESHOLDS BY SHIP SIZE ===
FLEE_THRESHOLDS_BY_SIZE = {
    "small": 0.4,      # Small ships flee earlier
    "medium": 0.3,     # Default
    "large": 0.25,     # Large ships fight longer
    "capital": 0.2     # Capital ships fight longest
}

# === WEAPON SWITCHING ===
WEAPON_SWITCH_COOLDOWN = 120  # 2 seconds at 60 FPS
MISSILE_CONSERVATION_THRESHOLD = 0.3  # Use missiles when ammo > 30%

# === PERFORMANCE OPTIMIZATION ===
MAX_SHIPS_SCAN_PER_FRAME = 2  # Stagger target scanning
UPDATE_STAGGER_OFFSET = 10    # Frames between ship updates

# === BOARDING CONSTANTS ===
BOARDING_RANGE = 100  # Distance to board disabled ships

# All available AI states for validation
ALL_AI_STATES = [
    AI_STATE_IDLE,
    AI_STATE_PATROLLING, 
    AI_STATE_ATTACKING,
    AI_STATE_FLEEING,
    AI_STATE_TRADING,
    AI_STATE_DISABLED
]

# All available personalities for validation  
ALL_AI_PERSONALITIES = [
    AI_PERSONALITY_AGGRESSIVE,
    AI_PERSONALITY_DEFENSIVE,
    AI_PERSONALITY_BALANCED,
    AI_PERSONALITY_COWARD
]
