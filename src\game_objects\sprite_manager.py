"""
Enhanced sprite_manager.py with special handling for planet file naming issues
"""

import pygame as pg
import os
import json
import math

class SpriteSheet:
    """
    Class to handle loading and managing spritesheets.
    Supports rotation frames and animation.
    """
    def __init__(self, image_path, metadata_path=None):
        """
        Initialize a spritesheet from an image and optional metadata.

        Args:
            image_path (str): Path to the spritesheet image
            metadata_path (str, optional): Path to the metadata JSON file. If None,
                                          will try to find a matching metadata file.
        """
        self.image_path = image_path
        self.metadata_path = metadata_path
        self.image = None
        self.frames = []
        self.frame_count = 0
        self.sprite_size = 0
        self.sheet_width = 0
        self.sheet_height = 0
        self.layout = "horizontal"
        self.name = os.path.basename(image_path).split('_')[0] if image_path else ""

        # Try to load the image
        self.load_image()

        # Try to load metadata
        if not metadata_path and image_path:
            # Try to find a matching metadata file with different possible naming conventions
            base_path = os.path.splitext(image_path)[0]
            
            # Handle special cases for problematic naming
            if "ice_1_spritesheet_spritesheet" in image_path:
                # Special metadata path for ice_1
                base_path = base_path.replace("_spritesheet", "")
                
            potential_metadata_patterns = [
                f"{base_path}_metadata.json",           # standard pattern
                f"{base_path}_spritesheet_metadata.json", # alternative pattern
                f"{base_path}.json",                    # simple pattern
            ]

            for pattern in potential_metadata_patterns:
                if os.path.exists(pattern):
                    print(f"Found metadata file: {pattern}")
                    self.metadata_path = pattern
                    break

        self.load_metadata()

    def load_image(self):
        """Load the spritesheet image."""
        if not self.image_path:
            print(f"Error: No image path provided for spritesheet")
            return False

        if not os.path.exists(self.image_path):
            print(f"Error: Spritesheet image not found at {self.image_path}")
            print(f"Absolute path: {os.path.abspath(self.image_path)}")
            print(f"Current working directory: {os.getcwd()}")
            return False

        try:
            print(f"Attempting to load image from: {self.image_path}")

            # Make sure Pygame display is initialized before loading images
            if not pg.get_init():
                pg.init()
            if not pg.display.get_surface():
                # Create a temporary surface if no display is set
                pg.display.set_mode((1, 1), pg.NOFRAME)

            # Now load the image
            self.image = pg.image.load(self.image_path).convert_alpha()
            print(f"Successfully loaded image: {self.image_path}")
            return True
        except Exception as e:
            print(f"Error loading spritesheet image: {e}")
            return False

    def load_metadata(self):
        """Load the metadata for the spritesheet."""
        if not self.metadata_path:
            print(f"No metadata path provided for spritesheet: {self.image_path}")
            # If no metadata, try to infer from the image
            if self.image:
                print(f"Inferring metadata from image for: {self.image_path}")
                self._infer_metadata_from_image()
            return False

        if not os.path.exists(self.metadata_path):
            print(f"Metadata file not found: {self.metadata_path}")
            # If metadata file doesn't exist, try to infer from the image
            if self.image:
                print(f"Inferring metadata from image for: {self.image_path}")
                self._infer_metadata_from_image()
            return False

        try:
            print(f"Loading metadata from: {self.metadata_path}")
            with open(self.metadata_path, 'r') as f:
                metadata = json.load(f)

                self.name = metadata.get('ship_name', self.name)
                self.frame_count = metadata.get('frame_count', 0)
                self.sprite_size = metadata.get('sprite_size', 0)
                self.sheet_width = metadata.get('sheet_width', 0)
                self.sheet_height = metadata.get('sheet_height', 0)
                self.layout = metadata.get('layout', 'horizontal')
                self.frames = metadata.get('frames', [])

                print(f"Successfully loaded metadata: {self.metadata_path}")
                print(f"Frame count: {self.frame_count}, Sprite size: {self.sprite_size}")
                return True
        except Exception as e:
            print(f"Error loading spritesheet metadata: {e}")
            # Try to infer from the image as a fallback
            if self.image:
                print(f"Attempting to infer metadata from image as fallback")
                self._infer_metadata_from_image()
            return False

    def _infer_metadata_from_image(self):
        """Infer metadata from the image if no metadata file is available."""
        if not self.image:
            return

        # Assume it's a horizontal spritesheet with square frames
        img_width, img_height = self.image.get_size()
        self.sprite_size = img_height  # Assume height is the frame size
        self.frame_count = img_width // img_height
        self.sheet_width = img_width
        self.sheet_height = img_height
        self.layout = "horizontal"

        # Generate frame data
        self.frames = []
        for i in range(self.frame_count):
            angle = (360 / self.frame_count * i) % 360
            x = i * self.sprite_size
            y = 0
            self.frames.append({
                "frame": i,
                "angle": angle,
                "x": x,
                "y": y,
                "width": self.sprite_size,
                "height": self.sprite_size
            })

    def get_frame(self, frame_index):
        """Get a specific frame from the spritesheet."""
        if not self.image or frame_index >= len(self.frames):
            return None

        frame = self.frames[frame_index]
        x, y = frame["x"], frame["y"]
        width, height = frame["width"], frame["height"]

        # Create a new surface for the frame
        frame_surface = pg.Surface((width, height), pg.SRCALPHA)
        frame_surface.blit(self.image, (0, 0), (x, y, width, height))
        return frame_surface

    def get_frame_by_angle(self, angle):
        """Get the frame that most closely matches the given angle."""
        if not self.image or not self.frames:
            return None

        angle = angle % 360
        closest_frame = 0
        closest_diff = 360

        for i, frame in enumerate(self.frames):
            frame_angle = frame["angle"]
            diff = abs(frame_angle - angle)
            if diff < closest_diff:
                closest_diff = diff
                closest_frame = i

        return self.get_frame(closest_frame)


def load_sprite(entity_type, entity_id, size=None):
    """Load a sprite for a game entity with enhanced planet support."""
    
    # Try multiple base paths to handle different working directory scenarios
    possible_base_paths = [
        os.path.join("assets", "images", "sprites"),
        os.path.join("..", "assets", "images", "sprites"),
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                    "assets", "images", "sprites")
    ]

    # Find the first valid base path
    base_path = None
    for path in possible_base_paths:
        if os.path.exists(path):
            base_path = path
            break

    if base_path is None:
        print(f"ERROR: Could not find sprites directory in any of these locations:")
        for path in possible_base_paths:
            print(f"  - {path}")
        return None

    # Construct the path based on entity type
    if entity_type == "ship":
        if size:
            path = os.path.join(base_path, "ships", size, f"{entity_id}_spritesheet.png")
        else:
            # Try to find the ship in any size folder
            path = None
            for size_cat in ["small", "medium", "large", "capital"]:
                test_path = os.path.join(base_path, "ships", size_cat, f"{entity_id}_spritesheet.png")
                if os.path.exists(test_path):
                    path = test_path
                    break
            if path is None:
                path = os.path.join(base_path, "ships", "small", f"{entity_id}_spritesheet.png")
                
    elif entity_type == "planet":
        # Handle special cases for problematic planet file naming
        if entity_id == "ice_1":
            # Special case: ice_1 has weird naming: ice_1_spritesheet_spritesheet.png
            path = os.path.join(base_path, "planets", "ice_1_spritesheet_spritesheet.png")
            print(f"Special handling for ice_1: {path}")
        else:
            # Normal planet path
            path = os.path.join(base_path, "planets", f"{entity_id}_spritesheet.png")
            
    elif entity_type == "station":
        path = os.path.join(base_path, "stations", f"{entity_id}_spritesheet.png")
    elif entity_type == "projectile":
        path = os.path.join(base_path, "projectiles", f"{entity_id}_spritesheet.png")
    elif entity_type == "effect":
        path = os.path.join(base_path, "effects", f"{entity_id}_spritesheet.png")
    else:
        print(f"Unknown entity type: {entity_type}")
        return None

    # Check if the file exists
    if not os.path.exists(path):
        print(f"Sprite not found: {path}")
        
        # For planets, try alternative paths for problematic naming
        if entity_type == "planet":
            print(f"Trying alternative planet paths for: {entity_id}")
            
            # List of alternative paths to try
            alternative_paths = []
            
            # Try removing duplicated parts in name
            if "volcanic_volcanic" in entity_id:
                fixed_id = entity_id.replace("volcanic_volcanic", "volcanic")
                alternative_paths.append(os.path.join(base_path, "planets", f"{fixed_id}_spritesheet.png"))
            
            # Try each alternative path
            for alt_path in alternative_paths:
                if os.path.exists(alt_path):
                    print(f"Found alternative planet sprite at: {alt_path}")
                    path = alt_path
                    break
            else:
                print(f"No alternative planet paths found for: {entity_id}")
                return None
        else:
            return None

    print(f"Loading sprite from: {path}")
    return SpriteSheet(path)


def get_angle_from_vector(vector):
    """Convert a direction vector to an angle in degrees."""
    if vector.length() == 0:
        return 0

    angle = math.atan2(vector.y, vector.x) * 180 / math.pi
    angle = (angle + 360) % 360
    return angle
