# Shipyard Images Directory

This directory contains images that will be displayed in the shipyard interface when players browse and purchase ships.

## Purpose

These images provide visual representations of ships in the shipyard interface, helping players:
- Identify different ship types at a glance
- Compare ship designs and aesthetics
- Make informed purchasing decisions
- Enhance the overall game experience

## Image Requirements

### Format
- **Supported formats**: PNG, JPG, JPEG, GIF, BMP
- **Recommended format**: PNG (for transparency support)

### Size
- **Recommended size**: 200x150 to 400x300 pixels
- **Maximum size**: 512x512 pixels (larger images will be scaled down)
- **Aspect ratio**: 4:3 or 16:9 preferred for shipyard display

### Style Guidelines
- Show the ship from an attractive angle (3/4 view or side profile)
- Use consistent lighting and background style
- Ensure ships are clearly visible and detailed
- Consider showing the ship's scale and distinctive features
- Maintain consistent art style across all ship images

## Usage

### Adding Images
1. Place your ship image files in this directory
2. Open the enhanced editor (`enhanced_editor_refactored.py`)
3. Navigate to the Ships tab
4. Select a ship to edit
5. In the "Shipyard Display Image" field, browse and select your image
6. Use the Preview button to verify the image displays correctly
7. Save the ship

### In-Game Display
- Images will appear in the shipyard interface when browsing ships
- Players will see these images alongside ship specifications
- Images help players visualize the ship before purchasing

## File Naming
- Use descriptive names that match the ship name/ID
- Examples:
  - `scout_ship.png`
  - `heavy_fighter.png`
  - `cargo_freighter.png`
  - `battleship.png`

## Image Content Suggestions
- **Fighters**: Show sleek, agile appearance
- **Freighters**: Emphasize cargo capacity and utility
- **Warships**: Display weapons and armor
- **Civilian ships**: Show comfort and passenger amenities

## Performance Notes
- Keep file sizes reasonable (under 500KB per image)
- The game will cache images for better performance
- PNG format with transparency allows for flexible backgrounds
- Consider creating multiple views for larger ships if needed
