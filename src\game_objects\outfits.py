"""
Outfit constants and base classes for Escape Velocity Py.
This module contains only constants and base classes.
All outfit data is now loaded from JSON files via the editor system.
"""

# Outfit categories
CATEGORY_WEAPON = "weapon"
CATEGORY_ENGINE = "engine"
CATEGORY_SHIELD = "shield"
CATEGORY_ARMOR = "armor"
CATEGORY_UTILITY = "utility"
CATEGORY_SPECIAL = "special"
CATEGORY_AMMO = "ammo"

# Weapon types
WEAPON_TYPE_LASER = "laser"
WEAPON_TYPE_MISSILE = "missile"
WEAPON_TYPE_PROJECTILE = "projectile"
WEAPON_TYPE_BEAM = "beam"

# Weapon mount types
MOUNT_TYPE_FIXED = "fixed"      # Fixed forward-firing weapons
MOUNT_TYPE_TURRET = "turret"    # Turret weapons that can target independently
MOUNT_TYPE_GUIDED = "guided"    # Guided weapons that track targets

# Tech levels
TECH_LEVEL_MIN = 0
TECH_LEVEL_MAX = 5

# Ship size compatibility
from game_objects.ships import SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL

# Import functions from the standardized system
from game_objects.standardized_outfits import get_outfit_by_id, get_outfits_by_category

# Legacy compatibility functions that now use the JSON-based system
def get_outfit_by_name(name):
    """Get an outfit by its name (case insensitive)."""
    from game_objects.standardized_outfits import OUTFITS_REGISTRY
    for outfit_id, outfit in OUTFITS_REGISTRY.items():
        if outfit.name.lower() == name.lower():
            return outfit
    return None

def get_outfits_by_category(category):
    """Get all outfits of a specific category."""
    from game_objects.standardized_outfits import OUTFITS_REGISTRY
    return [outfit for outfit in OUTFITS_REGISTRY.values() if outfit.category == category]

def get_compatible_outfits(ship_size):
    """Get all outfits compatible with a specific ship size."""
    from game_objects.standardized_outfits import OUTFITS_REGISTRY
    compatible = []
    for outfit in OUTFITS_REGISTRY.values():
        if hasattr(outfit, 'can_install_on_ship'):
            # For standardized outfits, we need to check size restrictions
            if not hasattr(outfit, 'ship_size_restrictions') or not outfit.ship_size_restrictions:
                compatible.append(outfit)  # No restrictions = compatible with all
            elif ship_size in outfit.ship_size_restrictions:
                compatible.append(outfit)
    return compatible

def get_outfits_by_tech_level(tech_level, include_lower=True):
    """
    Get all outfits available at a specific tech level.

    Args:
        tech_level (int): The tech level to check
        include_lower (bool): If True, include outfits from lower tech levels

    Returns:
        list: List of outfits available at the specified tech level
    """
    from game_objects.standardized_outfits import OUTFITS_REGISTRY
    if include_lower:
        return [outfit for outfit in OUTFITS_REGISTRY.values() 
                if getattr(outfit, 'min_tech_level', 1) <= tech_level]
    else:
        return [outfit for outfit in OUTFITS_REGISTRY.values() 
                if getattr(outfit, 'min_tech_level', 1) == tech_level]

def get_available_outfits_for_purchase(tech_level, ship_size):
    """
    Get all outfits available for purchase at a specific tech level and compatible with a ship size.

    Args:
        tech_level (int): The tech level of the outfitter
        ship_size (str): The size of the ship

    Returns:
        list: List of outfits available for purchase
    """
    from game_objects.standardized_outfits import OUTFITS_REGISTRY
    available = []
    for outfit in OUTFITS_REGISTRY.values():
        # Check tech level
        if getattr(outfit, 'min_tech_level', 1) > tech_level:
            continue
            
        # Check size compatibility
        if hasattr(outfit, 'ship_size_restrictions') and outfit.ship_size_restrictions:
            if ship_size not in outfit.ship_size_restrictions:
                continue
                
        available.append(outfit)
    
    return available

# Legacy constants for backwards compatibility
# These are now just aliases to the standardized system constants
from game_objects.standardized_outfits import (
    OUTFIT_CATEGORY_WEAPONS as CATEGORY_WEAPONS,
    OUTFIT_CATEGORY_DEFENSE as CATEGORY_DEFENSE,
    OUTFIT_CATEGORY_ENGINES as CATEGORY_ENGINES,
    OUTFIT_CATEGORY_ELECTRONICS as CATEGORY_ELECTRONICS,
    OUTFIT_CATEGORY_UTILITY as CATEGORY_UTILITIES,
    OUTFIT_CATEGORY_AMMUNITION as CATEGORY_AMMUNITION,
    OUTFIT_CATEGORY_SPECIAL as CATEGORY_SPECIALS
)

print("✅ Outfits.py cleaned up - now uses JSON data from editor!")
