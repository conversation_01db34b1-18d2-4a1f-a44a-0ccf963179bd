"""
Ship Sprite Mapper - Handles mapping between ship IDs and sprite file names
This module resolves mismatches between ship IDs in JSON and actual sprite files.
"""
import os

class ShipSpriteMapper:
    """
    Maps ship IDs to their corresponding sprite files.
    Handles naming inconsistencies between JSON data and sprite files.
    """
    
    def __init__(self):
        # Manual mapping for known mismatches
        self.id_to_sprite_mapping = {
            # Ships with underscore vs no underscore issues
            'passengerliner': 'passenger_liner',
            'bulkcarrier': 'bulk_carrier', 
            'heavyfreighter': 'heavy_freighter',
            'heavyfighter': 'heavy_fighter',
            
            # Ships that might have different naming
            'light_fighter': 'light_fighter',  # This one should work
            'interceptor': 'interceptor',      # This one should work
            
            # Add more mappings as needed
        }
        
        # Reverse mapping for sprite to ID
        self.sprite_to_id_mapping = {v: k for k, v in self.id_to_sprite_mapping.items()}
        
        # Cache for discovered sprite files
        self.available_sprites = {}
        self._scan_available_sprites()
    
    def _scan_available_sprites(self):
        """Scan the sprites directory to find all available ship sprites."""
        sprite_base_paths = [
            os.path.join("assets", "images", "sprites", "ships"),
            os.path.join("..", "assets", "images", "sprites", "ships"),
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                        "assets", "images", "sprites", "ships")
        ]
        
        # Find the correct base path
        base_path = None
        for path in sprite_base_paths:
            if os.path.exists(path):
                base_path = path
                break
        
        if not base_path:
            print("Warning: Could not find ship sprites directory")
            return
        
        # Scan each size category
        for size in ['small', 'medium', 'large', 'capital']:
            size_path = os.path.join(base_path, size)
            if os.path.exists(size_path):
                for filename in os.listdir(size_path):
                    if filename.endswith('_spritesheet.png'):
                        sprite_name = filename.replace('_spritesheet.png', '')
                        if size not in self.available_sprites:
                            self.available_sprites[size] = []
                        self.available_sprites[size].append(sprite_name)
        
        print(f"Found sprites: {self.available_sprites}")
    
    def get_sprite_name_for_ship(self, ship_id):
        """
        Get the sprite file name for a given ship ID.
        
        Args:
            ship_id: The ship ID from the JSON data
            
        Returns:
            str: The sprite file name (without _spritesheet.png)
        """
        # Check if there's a manual mapping
        if ship_id in self.id_to_sprite_mapping:
            return self.id_to_sprite_mapping[ship_id]
        
        # Otherwise, assume the sprite name matches the ship ID
        return ship_id
    
    def get_ship_id_for_sprite(self, sprite_name):
        """
        Get the ship ID for a given sprite name.
        
        Args:
            sprite_name: The sprite file name (without _spritesheet.png)
            
        Returns:
            str: The ship ID that should be used in JSON
        """
        # Check if there's a reverse mapping
        if sprite_name in self.sprite_to_id_mapping:
            return self.sprite_to_id_mapping[sprite_name]
        
        # Otherwise, assume the ship ID matches the sprite name
        return sprite_name
    
    def find_sprite_for_ship(self, ship_id, ship_size):
        """
        Find the actual sprite file for a ship, handling mismatches.
        
        Args:
            ship_id: The ship ID from JSON
            ship_size: The size category of the ship
            
        Returns:
            str or None: The sprite name if found, None otherwise
        """
        # Get the expected sprite name
        sprite_name = self.get_sprite_name_for_ship(ship_id)
        
        # Check if it exists in the expected size category
        if ship_size in self.available_sprites:
            if sprite_name in self.available_sprites[ship_size]:
                return sprite_name
        
        # If not found, try to find it in any size category
        for size, sprites in self.available_sprites.items():
            if sprite_name in sprites:
                print(f"Warning: Ship '{ship_id}' sprite found in '{size}' category, but ship is marked as '{ship_size}'")
                return sprite_name
        
        # If still not found, try the original ship_id
        if ship_id != sprite_name:
            for size, sprites in self.available_sprites.items():
                if ship_id in sprites:
                    print(f"Warning: Using ship_id '{ship_id}' directly as sprite name")
                    return ship_id
        
        print(f"Warning: No sprite found for ship '{ship_id}' (expected sprite: '{sprite_name}')")
        return None
    
    def get_sprite_metadata_for_ship(self, ship_id, ship_size):
        """
        Get sprite metadata for a ship.
        
        Args:
            ship_id: The ship ID from JSON
            ship_size: The size category of the ship
            
        Returns:
            dict: Sprite metadata with default values
        """
        sprite_name = self.find_sprite_for_ship(ship_id, ship_size)
        
        if not sprite_name:
            # Return default metadata for missing sprites
            return {
                'game_sprite': '',
                'shipyard_sprite': '',
                'animation_frames': 1,
                'sprite_size': 32,
                'animation_type': 'rotation',
                'frame_rate': 30
            }
        
        # Size-based sprite size defaults
        size_to_sprite_size = {
            'small': 32,
            'medium': 48,
            'large': 64,
            'capital': 256
        }
        
        return {
            'game_sprite': f"{sprite_name}_spritesheet.png",
            'shipyard_sprite': f"{sprite_name}_spritesheet.png",
            'animation_frames': 32,  # Most ships have 32 rotation frames
            'sprite_size': size_to_sprite_size.get(ship_size, 32),
            'animation_type': 'rotation',
            'frame_rate': 30
        }
    
    def update_ship_data_with_sprites(self, ship_data):
        """
        Update ship data dictionary with correct sprite information.
        
        Args:
            ship_data: Dictionary of ship data
            
        Returns:
            dict: Updated ship data with sprite information
        """
        updated_data = ship_data.copy()
        
        for ship_id, ship_info in updated_data.items():
            ship_size = ship_info.get('size', 'small')
            sprite_metadata = self.get_sprite_metadata_for_ship(ship_id, ship_size)
            
            # Add sprite metadata to ship info
            ship_info.update(sprite_metadata)
            
            print(f"Updated {ship_id} with sprite: {sprite_metadata['game_sprite']}")
        
        return updated_data
    
    def add_manual_mapping(self, ship_id, sprite_name):
        """
        Add a manual mapping between ship ID and sprite name.
        
        Args:
            ship_id: The ship ID from JSON
            sprite_name: The actual sprite file name
        """
        self.id_to_sprite_mapping[ship_id] = sprite_name
        self.sprite_to_id_mapping[sprite_name] = ship_id
        print(f"Added manual mapping: {ship_id} -> {sprite_name}")

# Global sprite mapper instance
SHIP_SPRITE_MAPPER = ShipSpriteMapper()
