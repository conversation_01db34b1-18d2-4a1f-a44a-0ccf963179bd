"""
Electronics Editor for the Enhanced Content Editor
Handles editing of electronics outfits
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .base_editor import BaseOutfitEditor
from .ui_components import StandardParameterGrid, ParameterLoader

class ElectronicsEditor(BaseOutfitEditor):
    """Editor for electronics outfits."""
    
    def __init__(self, parent, data_manager):
        super().__init__(parent, data_manager, "electronics")
    
    def setup_editor_ui(self, parent):
        """Setup the electronics editor interface."""
        # Create scrollable frame
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Basic Properties Grid
        self.basic_grid = StandardParameterGrid(scrollable_frame, "Basic Properties")
        self.basic_grid.add_string_field("Name", "name")
        self.basic_grid.add_int_field("Cost", "cost", 100, 100000)
        self.basic_grid.add_int_field("Space Required", "space_required", 1, 50)
        self.basic_grid.add_combo_field("Electronics Type", "subcategory", ["sensors", "targeting", "jamming", "communication", "scanning", "navigation", "countermeasures"])
        self.basic_grid.add_int_field("Min Tech Level", "min_tech_level", 1, 10)
        self.basic_grid.add_file_field("Outfitter Icon", "outfitter_icon")
        self.basic_grid.add_file_field("Outfitter Image", "outfitter_image")
        self.basic_grid.pack(fill=tk.X, padx=5, pady=5)

        # Electronics Type Selection
        type_frame = ttk.LabelFrame(scrollable_frame, text="Electronics System Type")
        type_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.electronics_type_var = tk.StringVar(value="sensors")
        ttk.Radiobutton(type_frame, text="Sensors & Detection", 
                       variable=self.electronics_type_var, value="sensors",
                       command=self.on_electronics_type_change).pack(anchor=tk.W, padx=5, pady=2)
        ttk.Radiobutton(type_frame, text="Targeting & Combat", 
                       variable=self.electronics_type_var, value="targeting",
                       command=self.on_electronics_type_change).pack(anchor=tk.W, padx=5, pady=2)
        ttk.Radiobutton(type_frame, text="Electronic Warfare", 
                       variable=self.electronics_type_var, value="warfare",
                       command=self.on_electronics_type_change).pack(anchor=tk.W, padx=5, pady=2)
        ttk.Radiobutton(type_frame, text="Communication & Navigation", 
                       variable=self.electronics_type_var, value="communication",
                       command=self.on_electronics_type_change).pack(anchor=tk.W, padx=5, pady=2)

        # Sensors & Detection Properties Grid
        self.sensors_grid = StandardParameterGrid(scrollable_frame, "Sensors & Detection Properties")
        self.sensors_grid.add_float_field("Sensor Range Boost", "sensor_range_boost", 0.0, 5000.0, 10.0)
        self.sensors_grid.add_float_field("Detection Strength", "detection_strength", 0.0, 2.0, 0.01)
        self.sensors_grid.add_float_field("Scan Resolution", "scan_resolution", 0.0, 2.0, 0.01)
        self.sensors_grid.add_float_field("Stealth Detection", "stealth_detection", 0.0, 1.0, 0.01)
        self.sensors_grid.add_float_field("Energy Signature Reduction", "energy_signature_reduction", 0.0, 0.8, 0.01)

        # Targeting & Combat Properties Grid
        self.targeting_grid = StandardParameterGrid(scrollable_frame, "Targeting & Combat Properties")
        self.targeting_grid.add_float_field("Targeting Accuracy Boost", "targeting_boost", 0.0, 1.0, 0.01)
        self.targeting_grid.add_float_field("Weapon Range Boost", "weapon_range_boost", 0.0, 2.0, 0.01)
        self.targeting_grid.add_float_field("Lock-on Speed", "lock_on_speed", 0.0, 3.0, 0.1)
        self.targeting_grid.add_float_field("Multi-target Capability", "multi_target_capability", 0.0, 1.0, 0.01)
        self.targeting_grid.add_int_field("Max Simultaneous Targets", "max_targets", 1, 20)
        
        # Electronic Warfare Properties Grid
        self.warfare_grid = StandardParameterGrid(scrollable_frame, "Electronic Warfare Properties")
        self.warfare_grid.add_float_field("Jamming Strength", "jamming_strength", 0.0, 1.0, 0.01)
        self.warfare_grid.add_float_field("Jamming Resistance", "jamming_resistance", 0.0, 1.0, 0.01)
        self.warfare_grid.add_int_field("Jamming Range", "jamming_range", 100, 3000)
        self.warfare_grid.add_float_field("ECM Effectiveness", "ecm_effectiveness", 0.0, 1.0, 0.01)
        self.warfare_grid.add_float_field("ECCM Capability", "eccm_capability", 0.0, 1.0, 0.01)
        
        # Communication & Navigation Properties Grid
        self.communication_grid = StandardParameterGrid(scrollable_frame, "Communication & Navigation Properties")
        self.communication_grid.add_int_field("Communication Range", "communication_range", 100, 10000)
        self.communication_grid.add_float_field("Navigation Accuracy", "navigation_accuracy", 0.0, 2.0, 0.01)
        self.communication_grid.add_float_field("Jump Drive Efficiency", "jump_efficiency", 0.0, 2.0, 0.01)
        self.communication_grid.add_combo_field("Encryption Level", "encryption_level", ["basic", "military", "quantum"])
        self.communication_grid.add_combo_field("Navigation AI", "navigation_ai", ["none", "basic", "advanced", "quantum"])
        
        # System Properties Grid (Common to all types)
        self.system_grid = StandardParameterGrid(scrollable_frame, "System Properties")
        self.system_grid.add_float_field("Energy Drain", "energy_drain", 0.0, 100.0, 0.1)
        self.system_grid.add_float_field("Heat Generation", "heat_generation", 0.0, 50.0, 0.1)
        self.system_grid.add_float_field("Processing Power", "processing_power", 1.0, 10.0, 0.1)
        self.system_grid.add_float_field("Reliability", "reliability", 0.5, 1.0, 0.01)
        self.system_grid.pack(fill=tk.X, padx=5, pady=5)
        
        # Description
        desc_frame = ttk.LabelFrame(scrollable_frame, text="Description")
        desc_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.description_text = tk.Text(desc_frame, height=3, wrap=tk.WORD)
        self.description_text.pack(fill=tk.X, padx=5, pady=5)

        # Save button
        save_frame = ttk.Frame(scrollable_frame)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        ttk.Button(save_frame, text="Save Electronics", command=self.save_item).pack(side=tk.RIGHT, padx=5)
        
        # Initialize UI state
        self.on_electronics_type_change()
    
    def on_electronics_type_change(self):
        """Show/hide UI sections based on electronics type."""
        electronics_type = self.electronics_type_var.get()
        
        # Hide all specialized grids first
        if hasattr(self, 'sensors_grid'):
            self.sensors_grid.frame.pack_forget()
        if hasattr(self, 'targeting_grid'):
            self.targeting_grid.frame.pack_forget()
        if hasattr(self, 'warfare_grid'):
            self.warfare_grid.frame.pack_forget()
        if hasattr(self, 'communication_grid'):
            self.communication_grid.frame.pack_forget()
            
        # Show relevant grids based on type
        if electronics_type == "sensors":
            if hasattr(self, 'sensors_grid'):
                self.sensors_grid.pack(fill=tk.X, padx=5, pady=5)
        elif electronics_type == "targeting":
            if hasattr(self, 'targeting_grid'):
                self.targeting_grid.pack(fill=tk.X, padx=5, pady=5)
        elif electronics_type == "warfare":
            if hasattr(self, 'warfare_grid'):
                self.warfare_grid.pack(fill=tk.X, padx=5, pady=5)
        elif electronics_type == "communication":
            if hasattr(self, 'communication_grid'):
                self.communication_grid.pack(fill=tk.X, padx=5, pady=5)
    
    def load_item_into_editor(self, electronic):
        """Load electronics data into the editor."""
        super().load_item_into_editor(electronic)
        
        # Load using standardized parameter loader
        ParameterLoader.load_outfit_parameters(electronic, self.basic_grid)
        ParameterLoader.load_outfit_parameters(electronic, self.sensors_grid)
        ParameterLoader.load_outfit_parameters(electronic, self.targeting_grid)
        ParameterLoader.load_outfit_parameters(electronic, self.warfare_grid)
        ParameterLoader.load_outfit_parameters(electronic, self.communication_grid)
        ParameterLoader.load_outfit_parameters(electronic, self.system_grid)
        
        # Set electronics type based on subcategory and properties
        subcategory = getattr(electronic, 'subcategory', 'sensors')
        if subcategory in ['sensors', 'scanning']:
            self.electronics_type_var.set('sensors')
        elif subcategory in ['targeting']:
            self.electronics_type_var.set('targeting')
        elif subcategory in ['jamming', 'countermeasures']:
            self.electronics_type_var.set('warfare')
        elif subcategory in ['communication', 'navigation']:
            self.electronics_type_var.set('communication')
        else:
            self.electronics_type_var.set('sensors')  # Default
            
        # Load description
        self.description_text.delete(1.0, tk.END)
        self.description_text.insert(1.0, getattr(electronic, 'description', ''))
        
        # Update UI visibility
        self.on_electronics_type_change()
    
    def save_item(self):
        """Save the current electronics with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No electronics selected to save")
            return
        
        try:
            # Save using standardized parameter loader
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.basic_grid)
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.system_grid)
            
            # Save type-specific parameters based on electronics type
            electronics_type = self.electronics_type_var.get()
            if electronics_type == "sensors":
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.sensors_grid)
            elif electronics_type == "targeting":
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.targeting_grid)
            elif electronics_type == "warfare":
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.warfare_grid)
            elif electronics_type == "communication":
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.communication_grid)
            
            # Save description
            self.current_outfit.description = self.description_text.get(1.0, tk.END).strip()
            
            super().save_item()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save electronics: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new electronics instance."""
        class SimpleElectronics:
            def __init__(self, id, name):
                # Basic properties
                self.id = id
                self.name = name
                self.category = "electronics"
                self.subcategory = "sensors"
                self.cost = 8000
                self.space_required = 2
                self.min_tech_level = 1
                self.outfitter_icon = ""
                self.outfitter_image = ""
                
                # Sensors & Detection Properties
                self.sensor_range_boost = 500.0
                self.detection_strength = 1.0
                self.scan_resolution = 1.0
                self.stealth_detection = 0.0
                self.energy_signature_reduction = 0.0
                
                # Targeting & Combat Properties
                self.targeting_boost = 0.0
                self.weapon_range_boost = 0.0
                self.lock_on_speed = 1.0
                self.multi_target_capability = 0.0
                self.max_targets = 1
                
                # Electronic Warfare Properties
                self.jamming_strength = 0.0
                self.jamming_resistance = 0.0
                self.jamming_range = 1000
                self.ecm_effectiveness = 0.0
                self.eccm_capability = 0.0
                
                # Communication & Navigation Properties
                self.communication_range = 2000
                self.navigation_accuracy = 1.0
                self.jump_efficiency = 1.0
                self.encryption_level = "basic"
                self.navigation_ai = "none"
                
                # System Properties
                self.energy_drain = 5.0
                self.heat_generation = 3.0
                self.processing_power = 1.0
                self.reliability = 0.9
                
                self.description = ""

        return SimpleElectronics(item_id, item_id.replace('_', ' ').title())
