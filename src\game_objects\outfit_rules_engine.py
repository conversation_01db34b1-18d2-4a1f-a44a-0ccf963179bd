"""
Outfit Rules Engine for Escape Velocity Py
Defines the core game mechanics for how different outfit types work
This is the "physics engine" for outfits - outfit editors create items that follow these rules
"""

import pygame as pg
import math
import random
from enum import Enum

# =============================================================================
# DEFENSE SYSTEM RULES
# =============================================================================

# Defense Mount Types
DEFENSE_MOUNT_PASSIVE = "passive"        # Always active (shields, armor)
DEFENSE_MOUNT_FIXED = "fixed"           # Fixed-direction point defense
DEFENSE_MOUNT_TURRET = "turret"         # Rotating point defense turret
DEFENSE_MOUNT_OMNIDIRECTIONAL = "omni"  # 360° coverage (ECM, reactive armor)

# Defense Activation Types
DEFENSE_ACTIVATION_AUTOMATIC = "automatic"  # Fires automatically at threats
DEFENSE_ACTIVATION_MANUAL = "manual"       # Player-controlled
DEFENSE_ACTIVATION_TRIGGERED = "triggered" # Activates on damage/proximity

# Defense Target Types
DEFENSE_TARGET_PROJECTILES = "projectiles"  # Anti-missile/torpedo
DEFENSE_TARGET_SHIPS = "ships"             # Anti-ship weapons
DEFENSE_TARGET_ENERGY = "energy"          # Energy weapon deflection
DEFENSE_TARGET_ALL = "all"                # Universal defense

class DefenseSystem:
    """Core rules for how defense systems work in the game engine."""
    
    @staticmethod
    def update_passive_defenses(ship, dt):
        """Update passive defense systems (shields, armor plating, etc.)"""
        total_shield_regen = 0.0
        total_armor_repair = 0.0
        total_damage_reduction = 0.0
        total_energy_drain = 0.0
        
        # Process all installed defense outfits
        for outfit_id, quantity in ship.installed_outfits.items():
            outfit = get_outfit_by_id(outfit_id)
            if not outfit or outfit.category != "defense":
                continue
                
            for _ in range(quantity):
                if outfit.subcategory == "shields":
                    total_shield_regen += outfit.shield_recharge_boost
                    total_energy_drain += outfit.energy_drain
                elif outfit.subcategory == "armor":
                    total_armor_repair += getattr(outfit, 'armor_repair_rate', 0.0)
                    total_damage_reduction += outfit.damage_reduction
                
        # Apply shield regeneration (if ship has power)
        if ship.power > 0 and total_shield_regen > 0:
            power_cost = total_energy_drain * dt
            if ship.consume_power(power_cost):
                ship.shields = min(ship.max_shields, ship.shields + total_shield_regen * dt)
        
        # Store damage reduction for combat calculations
        ship.damage_reduction = min(0.8, total_damage_reduction)  # Cap at 80%
    
    @staticmethod
    def update_active_defenses(ship, dt, nearby_projectiles):
        """Update active defense systems (point defense, ECM, etc.)"""
        active_defenses = []
        
        # Find all active defense systems
        for outfit_id, quantity in ship.installed_outfits.items():
            outfit = get_outfit_by_id(outfit_id)
            if not outfit or outfit.category != "defense":
                continue
            if outfit.subcategory != "point_defense":
                continue
                
            for i in range(quantity):
                defense_instance = outfit.clone()
                defense_instance.instance_id = f"{outfit_id}_{i}"
                active_defenses.append(defense_instance)
        
        # Process each active defense system
        for defense in active_defenses:
            DefenseSystem.process_point_defense(ship, defense, dt, nearby_projectiles)
    
    @staticmethod
    def process_point_defense(ship, defense_outfit, dt, nearby_projectiles):
        """Process a single point defense system."""
        # Check if system is ready to fire
        if defense_outfit.cooldown_timer > 0:
            defense_outfit.cooldown_timer -= dt
            return
        
        # Find valid targets within range
        valid_targets = []
        defense_range = getattr(defense_outfit, 'defense_range', 200)
        
        for projectile in nearby_projectiles:
            # Check if projectile is hostile to this ship
            if hasattr(projectile, 'owner') and projectile.owner.faction_id == ship.faction_id:
                continue
                
            # Check range
            distance = ship.pos.distance_to(projectile.pos)
            if distance > defense_range:
                continue
                
            # Check if defense can target this projectile type
            target_types = getattr(defense_outfit, 'target_types', ["projectiles"])
            if "projectiles" not in target_types and "all" not in target_types:
                continue
                
            valid_targets.append((projectile, distance))
        
        if not valid_targets:
            return
            
        # Target closest threat
        valid_targets.sort(key=lambda x: x[1])
        target_projectile = valid_targets[0][0]
        
        # Check mount type for firing constraints
        mount_type = getattr(defense_outfit, 'mount_type', DEFENSE_MOUNT_TURRET)
        can_engage = DefenseSystem.can_defense_engage_target(
            ship, defense_outfit, target_projectile, mount_type
        )
        
        if not can_engage:
            return
            
        # Check power requirements
        power_cost = getattr(defense_outfit, 'power_cost', 5.0)
        if not ship.consume_power(power_cost):
            return
            
        # Fire at target
        success_chance = DefenseSystem.calculate_defense_accuracy(
            ship, defense_outfit, target_projectile
        )
        
        if random.random() < success_chance:
            # Successful interception
            target_projectile.kill()  # Remove the projectile
            
            # Visual/audio feedback could be added here
            if hasattr(ship, 'game') and hasattr(ship.game, 'set_status_message'):
                ship.game.set_status_message("Point defense engaged!", (0, 255, 0))
        
        # Set cooldown
        fire_rate = getattr(defense_outfit, 'fire_rate', 2.0)
        defense_outfit.cooldown_timer = 1.0 / fire_rate
    
    @staticmethod
    def can_defense_engage_target(ship, defense_outfit, target, mount_type):
        """Check if a defense system can engage a specific target."""
        if mount_type == DEFENSE_MOUNT_TURRET or mount_type == DEFENSE_MOUNT_OMNIDIRECTIONAL:
            return True  # Can engage in any direction
            
        elif mount_type == DEFENSE_MOUNT_FIXED:
            # Check if target is within firing arc
            to_target = target.pos - ship.pos
            target_angle = math.degrees(math.atan2(to_target.y, to_target.x))
            
            # Normalize angles
            ship_angle = ship.angle % 360
            target_angle = target_angle % 360
            
            # Calculate angle difference
            angle_diff = abs(target_angle - ship_angle)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
                
            # Fixed defenses have limited firing arc
            firing_arc = getattr(defense_outfit, 'firing_arc', 45)  # degrees
            return angle_diff <= firing_arc / 2
            
        return False
    
    @staticmethod
    def calculate_defense_accuracy(ship, defense_outfit, target):
        """Calculate the accuracy of a defense system against a target."""
        base_accuracy = getattr(defense_outfit, 'accuracy', 0.7)
        
        # Factor in target distance
        distance = ship.pos.distance_to(target.pos)
        defense_range = getattr(defense_outfit, 'defense_range', 200)
        distance_factor = 1.0 - (distance / defense_range) * 0.3  # Up to 30% penalty at max range
        
        # Factor in target speed
        if hasattr(target, 'vel'):
            target_speed = target.vel.length()
            speed_factor = max(0.3, 1.0 - target_speed * 0.05)  # Faster targets harder to hit
        else:
            speed_factor = 1.0
            
        # Factor in ship electronics
        electronics_bonus = getattr(ship, 'targeting_accuracy', 1.0) - 1.0
        
        final_accuracy = base_accuracy * distance_factor * speed_factor + electronics_bonus
        return max(0.1, min(0.95, final_accuracy))  # Clamp between 10% and 95%

# =============================================================================
# ENGINE SYSTEM RULES
# =============================================================================

# Engine Types
ENGINE_TYPE_MAIN = "main"           # Primary propulsion
ENGINE_TYPE_MANEUVERING = "maneuvering"  # RCS thrusters
ENGINE_TYPE_AFTERBURNER = "afterburner"  # Temporary boost
ENGINE_TYPE_JUMP = "jump"           # Hyperspace drive

# Engine States
ENGINE_STATE_OFF = "off"
ENGINE_STATE_IDLE = "idle"
ENGINE_STATE_ACTIVE = "active"
ENGINE_STATE_OVERLOAD = "overload"

class EngineSystem:
    """Core rules for how engine systems work in the game engine."""
    
    @staticmethod
    def update_engine_systems(ship, dt, input_state):
        """Update all engine systems based on player input and ship state."""
        # Calculate total engine capabilities
        engine_stats = EngineSystem.calculate_engine_stats(ship)
        
        # Update main propulsion
        EngineSystem.update_main_engines(ship, engine_stats, dt, input_state)
        
        # Update maneuvering thrusters
        EngineSystem.update_maneuvering_thrusters(ship, engine_stats, dt, input_state)
        
        # Update afterburners
        EngineSystem.update_afterburners(ship, engine_stats, dt, input_state)
        
        # Update fuel consumption
        EngineSystem.update_fuel_consumption(ship, engine_stats, dt, input_state)
    
    @staticmethod
    def calculate_engine_stats(ship):
        """Calculate combined engine statistics from all installed engine outfits."""
        stats = {
            'acceleration': ship.base_acceleration,
            'max_speed': ship.base_max_speed,
            'turn_rate': ship.base_turn_rate,
            'fuel_efficiency': 1.0,
            'afterburner_thrust': 0.0,
            'afterburner_speed': 0.0,
            'reverse_thrust_capability': False,
            'thrust_vectoring': False,
            'total_power_drain': 0.0
        }
        
        # Process all engine outfits
        for outfit_id, quantity in ship.installed_outfits.items():
            outfit = get_outfit_by_id(outfit_id)
            if not outfit or outfit.category != "engines":
                continue
                
            for _ in range(quantity):
                # Apply engine bonuses
                stats['acceleration'] += outfit.acceleration_boost
                stats['max_speed'] += outfit.max_speed_boost
                stats['turn_rate'] += outfit.turn_rate_boost
                stats['fuel_efficiency'] *= outfit.fuel_efficiency
                stats['total_power_drain'] += outfit.energy_drain
                
                # Check for special capabilities
                if outfit.id in ["reverse_thrusters", "maneuvering_thrusters", "advanced_engines"]:
                    stats['reverse_thrust_capability'] = True
                    
                if outfit.subcategory == "afterburner":
                    stats['afterburner_thrust'] += getattr(outfit, 'afterburner_thrust', 2.0)
                    stats['afterburner_speed'] += getattr(outfit, 'afterburner_speed', 5.0)
                    
                if "vectoring" in outfit.id or "omnidirectional" in outfit.id:
                    stats['thrust_vectoring'] = True
        
        return stats
    
    @staticmethod
    def update_main_engines(ship, engine_stats, dt, input_state):
        """Update main propulsion systems."""
        thrust_applied = False
        power_consumption = 0.0
        
        # Forward thrust
        if input_state.get('thrust_forward', False):
            if ship.power > 0:
                thrust_power = getattr(ship, 'thruster_power_cost', 10.0) * dt
                if ship.consume_power(thrust_power):
                    # Apply full thrust
                    forward_dir = EngineSystem.get_ship_forward_vector(ship)
                    ship.vel += forward_dir * engine_stats['acceleration'] * dt
                    thrust_applied = True
                    power_consumption += thrust_power
                else:
                    # Low power - reduced thrust
                    forward_dir = EngineSystem.get_ship_forward_vector(ship)
                    ship.vel += forward_dir * engine_stats['acceleration'] * 0.1 * dt
        
        # Reverse thrust (if equipped)
        if input_state.get('thrust_reverse', False) and engine_stats['reverse_thrust_capability']:
            if ship.power > 0:
                thrust_power = getattr(ship, 'thruster_power_cost', 10.0) * 0.6 * dt
                if ship.consume_power(thrust_power):
                    forward_dir = EngineSystem.get_ship_forward_vector(ship)
                    ship.vel -= forward_dir * engine_stats['acceleration'] * 0.6 * dt
                    thrust_applied = True
                    power_consumption += thrust_power
        
        # Apply drag when not thrusting
        if not thrust_applied:
            EngineSystem.apply_space_drag(ship, dt)
        
        # Limit maximum speed
        if ship.vel.length() > engine_stats['max_speed']:
            ship.vel.scale_to_length(engine_stats['max_speed'])
    
    @staticmethod
    def update_maneuvering_thrusters(ship, engine_stats, dt, input_state):
        """Update maneuvering thruster systems."""
        rotation_applied = False
        
        # Rotation
        if input_state.get('turn_left', False) or input_state.get('turn_right', False):
            if ship.power > 0:
                turn_power = 1.0 * dt  # Power cost for turning
                if ship.consume_power(turn_power):
                    turn_direction = 0
                    if input_state.get('turn_left', False):
                        turn_direction -= 1
                    if input_state.get('turn_right', False):
                        turn_direction += 1
                    
                    ship.angle += turn_direction * engine_stats['turn_rate'] * dt
                    ship.angle = ship.angle % 360
                    rotation_applied = True
                elif ship.power > 0:
                    # Minimal turning with remaining power
                    turn_direction = 0
                    if input_state.get('turn_left', False):
                        turn_direction -= 1
                    if input_state.get('turn_right', False):
                        turn_direction += 1
                    
                    ship.angle += turn_direction * engine_stats['turn_rate'] * 0.1 * dt
                    ship.angle = ship.angle % 360
        
        # Thrust vectoring (if equipped)
        if engine_stats['thrust_vectoring']:
            EngineSystem.apply_thrust_vectoring(ship, engine_stats, dt, input_state)
    
    @staticmethod
    def update_afterburners(ship, engine_stats, dt, input_state):
        """Update afterburner systems."""
        if not engine_stats['afterburner_thrust'] > 0:
            return
            
        if input_state.get('afterburner', False):
            # High power consumption
            afterburner_power = 25.0 * dt
            if ship.consume_power(afterburner_power):
                # Apply afterburner effects
                forward_dir = EngineSystem.get_ship_forward_vector(ship)
                ship.vel += forward_dir * engine_stats['afterburner_thrust'] * dt
                
                # Temporary speed boost
                temp_max_speed = engine_stats['max_speed'] + engine_stats['afterburner_speed']
                if ship.vel.length() > temp_max_speed:
                    ship.vel.scale_to_length(temp_max_speed)
                
                # Visual/audio effects could be added here
                if hasattr(ship, 'afterburner_active'):
                    ship.afterburner_active = True
            else:
                if hasattr(ship, 'afterburner_active'):
                    ship.afterburner_active = False
        else:
            if hasattr(ship, 'afterburner_active'):
                ship.afterburner_active = False
    
    @staticmethod
    def update_fuel_consumption(ship, engine_stats, dt, input_state):
        """Update fuel consumption based on engine usage."""
        base_fuel_consumption = 0.1 * dt  # Base idle consumption
        
        # Additional consumption based on thrust usage
        if input_state.get('thrust_forward', False):
            base_fuel_consumption += 1.0 * dt
        if input_state.get('thrust_reverse', False):
            base_fuel_consumption += 0.6 * dt
        if input_state.get('afterburner', False):
            base_fuel_consumption += 5.0 * dt
        
        # Apply fuel efficiency
        actual_consumption = base_fuel_consumption / engine_stats['fuel_efficiency']
        ship.consume_fuel(actual_consumption)
    
    @staticmethod
    def get_ship_forward_vector(ship):
        """Get the forward direction vector for a ship."""
        forward_angle = math.radians(ship.angle - 90)
        return pg.math.Vector2(math.cos(forward_angle), math.sin(forward_angle))
    
    @staticmethod
    def apply_space_drag(ship, dt):
        """Apply realistic space drag (minimal)."""
        if ship.vel.length_squared() > 0:
            # Very minimal drag in space
            drag_factor = 0.002
            drag = ship.vel.normalize() * drag_factor * ship.vel.length_squared()
            if drag.length_squared() < ship.vel.length_squared():
                ship.vel -= drag * dt
            else:
                ship.vel = pg.math.Vector2(0, 0)
    
    @staticmethod
    def apply_thrust_vectoring(ship, engine_stats, dt, input_state):
        """Apply thrust vectoring for ships with that capability."""
        # This could allow strafing, precise positioning, etc.
        # Implementation depends on specific control scheme desired
        pass

# =============================================================================
# ELECTRONICS SYSTEM RULES
# =============================================================================

# Electronics Types
ELECTRONICS_SENSORS = "sensors"
ELECTRONICS_TARGETING = "targeting"
ELECTRONICS_ECM = "ecm"           # Electronic countermeasures
ELECTRONICS_ECCM = "eccm"         # Electronic counter-countermeasures
ELECTRONICS_COMMUNICATION = "communication"
ELECTRONICS_NAVIGATION = "navigation"

# Electronics States
ELECTRONICS_STATE_OFF = "off"
ELECTRONICS_STATE_PASSIVE = "passive"
ELECTRONICS_STATE_ACTIVE = "active"
ELECTRONICS_STATE_JAMMED = "jammed"

class ElectronicsSystem:
    """Core rules for how electronics systems work in the game engine."""
    
    @staticmethod
    def update_electronics_systems(ship, dt, nearby_objects):
        """Update all electronics systems."""
        electronics_stats = ElectronicsSystem.calculate_electronics_stats(ship)
        
        # Update sensor systems
        ElectronicsSystem.update_sensors(ship, electronics_stats, dt, nearby_objects)
        
        # Update targeting systems
        ElectronicsSystem.update_targeting(ship, electronics_stats, dt)
        
        # Update ECM/ECCM systems
        ElectronicsSystem.update_countermeasures(ship, electronics_stats, dt, nearby_objects)
        
        # Update communication systems
        ElectronicsSystem.update_communications(ship, electronics_stats, dt)
    
    @staticmethod
    def calculate_electronics_stats(ship):
        """Calculate combined electronics capabilities."""
        stats = {
            'sensor_range': getattr(ship, 'base_sensor_range', 1000.0),
            'sensor_resolution': 1.0,
            'targeting_accuracy': 1.0,
            'targeting_speed': 1.0,
            'jam_resistance': 0.0,
            'jam_strength': 0.0,
            'communication_range': 5000.0,
            'scan_speed': 1.0,
            'total_power_drain': 0.0
        }
        
        # Process all electronics outfits
        for outfit_id, quantity in ship.installed_outfits.items():
            outfit = get_outfit_by_id(outfit_id)
            if not outfit or outfit.category != "electronics":
                continue
                
            for _ in range(quantity):
                stats['sensor_range'] += outfit.sensor_range_boost
                stats['targeting_accuracy'] += outfit.targeting_boost
                stats['jam_resistance'] += outfit.jamming_strength
                stats['communication_range'] += outfit.communication_range
                stats['scan_speed'] += outfit.scan_speed_boost
                stats['total_power_drain'] += outfit.energy_drain
                
                # Special electronics capabilities
                if "ecm" in outfit.id.lower():
                    stats['jam_strength'] += getattr(outfit, 'jam_strength', 0.3)
                if "sensor" in outfit.id.lower():
                    stats['sensor_resolution'] += getattr(outfit, 'resolution_boost', 0.2)
                if "targeting" in outfit.id.lower():
                    stats['targeting_speed'] += getattr(outfit, 'targeting_speed_boost', 0.3)
        
        return stats
    
    @staticmethod
    def update_sensors(ship, electronics_stats, dt, nearby_objects):
        """Update sensor systems and object detection."""
        if ship.power <= 0:
            ship.detected_objects = []
            return
            
        # Consume power for sensors
        sensor_power = electronics_stats['total_power_drain'] * dt
        if not ship.consume_power(sensor_power):
            # Reduced sensor capability on low power
            electronics_stats['sensor_range'] *= 0.3
            electronics_stats['sensor_resolution'] *= 0.5
        
        # Detect objects within sensor range
        detected_objects = []
        sensor_range = electronics_stats['sensor_range']
        
        for obj in nearby_objects:
            if obj == ship:
                continue
                
            distance = ship.pos.distance_to(obj.pos)
            if distance <= sensor_range:
                # Calculate detection probability based on object size, distance, etc.
                detection_chance = ElectronicsSystem.calculate_detection_chance(
                    ship, obj, distance, electronics_stats
                )
                
                if random.random() < detection_chance:
                    detected_objects.append({
                        'object': obj,
                        'distance': distance,
                        'detection_strength': detection_chance,
                        'classification': ElectronicsSystem.classify_object(obj, electronics_stats)
                    })
        
        ship.detected_objects = detected_objects
    
    @staticmethod
    def update_targeting(ship, electronics_stats, dt):
        """Update targeting computer systems."""
        if not hasattr(ship, 'target_lock_time'):
            ship.target_lock_time = 0.0
        if not hasattr(ship, 'target_lock_strength'):
            ship.target_lock_strength = 0.0
            
        if hasattr(ship, 'targeted_object') and ship.targeted_object:
            # Improve target lock over time
            lock_rate = electronics_stats['targeting_speed'] * dt
            ship.target_lock_time += dt
            ship.target_lock_strength = min(1.0, ship.target_lock_time * lock_rate)
            
            # Apply targeting bonuses to weapons
            ElectronicsSystem.apply_targeting_bonuses(ship, electronics_stats)
        else:
            ship.target_lock_time = 0.0
            ship.target_lock_strength = 0.0
    
    @staticmethod
    def update_countermeasures(ship, electronics_stats, dt, nearby_objects):
        """Update ECM and ECCM systems."""
        if electronics_stats['jam_strength'] > 0:
            # Active jamming - affects nearby ships
            jam_range = 500.0  # Base jamming range
            
            for obj in nearby_objects:
                if obj == ship or not hasattr(obj, 'faction_id'):
                    continue
                    
                # Check if target is hostile
                if hasattr(ship, 'faction_id') and obj.faction_id == ship.faction_id:
                    continue
                    
                distance = ship.pos.distance_to(obj.pos)
                if distance <= jam_range:
                    ElectronicsSystem.apply_jamming_effects(ship, obj, electronics_stats, distance)
        
        # Resist incoming jamming
        if hasattr(ship, 'being_jammed'):
            resistance = electronics_stats['jam_resistance']
            if resistance > ship.jamming_strength:
                ship.being_jammed = False
                ship.jamming_strength = 0.0
    
    @staticmethod
    def update_communications(ship, electronics_stats, dt):
        """Update communication systems."""
        # This could handle fleet coordination, distress calls, etc.
        ship.communication_range = electronics_stats['communication_range']
    
    @staticmethod
    def calculate_detection_chance(ship, target, distance, electronics_stats):
        """Calculate the probability of detecting a target."""
        base_chance = 0.9
        
        # Distance factor
        max_range = electronics_stats['sensor_range']
        distance_factor = 1.0 - (distance / max_range) * 0.5
        
        # Target size factor
        if hasattr(target, 'ship') and hasattr(target.ship, 'size'):
            size_factor = {
                'small': 0.7,
                'medium': 0.9,
                'large': 1.2,
                'capital': 1.5
            }.get(target.ship.size, 1.0)
        else:
            size_factor = 1.0
        
        # Stealth factor (if target has stealth)
        stealth_factor = 1.0
        if hasattr(target, 'stealth_rating'):
            stealth_factor = 1.0 - target.stealth_rating
        
        # Resolution factor
        resolution_factor = electronics_stats['sensor_resolution']
        
        final_chance = base_chance * distance_factor * size_factor * stealth_factor * resolution_factor
        return max(0.1, min(0.95, final_chance))
    
    @staticmethod
    def classify_object(obj, electronics_stats):
        """Classify a detected object based on sensor resolution."""
        if electronics_stats['sensor_resolution'] < 0.5:
            return "Unknown Contact"
        elif electronics_stats['sensor_resolution'] < 1.0:
            if hasattr(obj, 'ship'):
                return f"Ship ({obj.ship.size.title()})"
            else:
                return "Object"
        else:
            # High resolution - detailed classification
            if hasattr(obj, 'ship'):
                return f"{obj.faction_id.title()} {obj.ship.name}"
            elif hasattr(obj, 'name'):
                return obj.name
            else:
                return "Unknown Object"
    
    @staticmethod
    def apply_targeting_bonuses(ship, electronics_stats):
        """Apply targeting computer bonuses to weapons."""
        accuracy_bonus = (electronics_stats['targeting_accuracy'] - 1.0) * ship.target_lock_strength
        
        # Apply to all weapons
        for weapon in getattr(ship, 'weapons', []):
            if not hasattr(weapon, 'base_accuracy'):
                weapon.base_accuracy = weapon.accuracy
            weapon.accuracy = weapon.base_accuracy + accuracy_bonus
    
    @staticmethod
    def apply_jamming_effects(jammer_ship, target_ship, electronics_stats, distance):
        """Apply jamming effects to a target ship."""
        jam_strength = electronics_stats['jam_strength']
        
        # Distance attenuation
        max_jam_range = 500.0
        distance_factor = 1.0 - (distance / max_jam_range)
        effective_jam = jam_strength * distance_factor
        
        # Apply jamming
        target_ship.being_jammed = True
        target_ship.jamming_strength = effective_jam
        
        # Reduce target's electronics effectiveness
        if hasattr(target_ship, 'sensor_range'):
            target_ship.sensor_range *= (1.0 - effective_jam * 0.5)
        if hasattr(target_ship, 'targeting_accuracy'):
            target_ship.targeting_accuracy *= (1.0 - effective_jam * 0.3)

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def get_outfit_by_id(outfit_id):
    """Get outfit by ID - imports from standardized_outfits."""
    try:
        from .standardized_outfits import get_outfit_by_id as get_outfit
        return get_outfit(outfit_id)
    except ImportError:
        return None

def update_all_outfit_systems(ship, dt, input_state=None, nearby_objects=None):
    """Master function to update all outfit systems."""
    if input_state is None:
        input_state = {}
    if nearby_objects is None:
        nearby_objects = []
        
    # Update defense systems
    DefenseSystem.update_passive_defenses(ship, dt)
    if nearby_objects:
        projectiles = [obj for obj in nearby_objects if hasattr(obj, 'damage') and hasattr(obj, 'owner')]
        DefenseSystem.update_active_defenses(ship, dt, projectiles)
    
    # Update engine systems
    EngineSystem.update_engine_systems(ship, dt, input_state)
    
    # Update electronics systems
    ElectronicsSystem.update_electronics_systems(ship, dt, nearby_objects)
