"""
Updated AI ship to work with the new standardized outfit system - Part 2
"""
    def get_angle_to_point(self, target_pos):
        """Calculate the angle needed to face a target position."""
        direction = target_pos - self.pos
        if direction.length_squared() > 0:
            angle = direction.angle_to(pg.math.Vector2(0, -1))
            return angle % 360
        return self.angle

    def apply_thrust_in_facing_direction(self, thrust_multiplier=1.0):
        """Apply thrust in the direction the ship is currently facing."""
        forward_angle = math.radians(self.angle - 90)
        forward = pg.math.Vector2(math.cos(forward_angle), math.sin(forward_angle))
        thrust = forward * self.acceleration * thrust_multiplier
        self.vel += thrust

    def fire_weapon(self, dt):
        """Fire the currently active weapon if possible."""
        if not self.weapons or self.active_weapon_index >= len(self.weapons):
            return None

        weapon = self.weapons[self.active_weapon_index]
        weapon.update(dt)

        if not weapon.can_fire():
            return None

        target = self.target_entity
        if weapon.mount_type in [MOUNT_TYPE_TURRET, MOUNT_TYPE_GUIDED] and not target:
            return None

        # Calculate spawn position and angle
        spawn_pos = self.pos.copy()
        projectile_angle = self.angle

        forward_angle = math.radians(self.angle - 90)
        forward = pg.math.Vector2(math.cos(forward_angle), math.sin(forward_angle))

        if weapon.mount_type == MOUNT_TYPE_FIXED:
            offset_to_nose = self.image_orig.get_height() / 2
            spawn_pos = self.pos + forward * offset_to_nose
        elif weapon.mount_type == MOUNT_TYPE_TURRET and target:
            to_target = target.pos - self.pos
            target_angle = math.degrees(math.atan2(to_target.y, to_target.x)) + 90
            projectile_angle = target_angle % 360
            to_target.normalize()
            spawn_pos = self.pos + to_target * (self.rect.width / 3)

        # Create projectile based on weapon behavior
        if weapon.projectile_behavior == BEHAVIOR_INSTANT:
            projectile = LaserProjectile(
                self.game,
                self,
                spawn_pos,
                projectile_angle,
                weapon.damage,
                weapon.range
            )
        elif weapon.uses_ammo:
            projectile = MissileProjectile(
                self.game,
                self,
                spawn_pos,
                projectile_angle,
                weapon.damage,
                weapon.range,
                target,
                tracking=getattr(weapon, 'tracking_strength', 0.0)
            )
            weapon.current_ammo -= 1
        else:
            projectile = LaserProjectile(
                self.game,
                self,
                spawn_pos,
                projectile_angle,
                weapon.damage,
                weapon.range
            )

        if weapon.fire():
            self.game.all_sprites.add(projectile)
            self.projectiles.add(projectile)
            return projectile

        return None

    def rotate_image(self):
        """Update the ship's image based on its current angle."""
        old_center = self.rect.center

        try:
            if self.spritesheet and self.spritesheet.image:
                frame = self.spritesheet.get_frame_by_angle(self.angle)
                if frame:
                    self.image = frame
                else:
                    self.image = pg.transform.rotate(self.image_orig, -self.angle)
            else:
                self.image = pg.transform.rotate(self.image_orig, -self.angle)
        except Exception as e:
            print(f"AI Ship: Error during rotation: {e}")
            self.image = self.image_orig.copy()

        self.rect = self.image.get_rect()
        self.rect.center = old_center

    def update(self, dt=1/60):
        """Update AI ship state."""
        self.state_timer -= 1
        if self.shield_recharge_timer > 0:
            self.shield_recharge_timer -= 1
        elif self.shields < self.max_shields:
            self.shields = min(self.max_shields, self.shields + 0.1)

        # State Machine
        if self.ai_state == AI_STATE_IDLE:
            self.perform_idle()
        elif self.ai_state == AI_STATE_PATROLLING:
            self.perform_patrolling()
        elif self.ai_state == AI_STATE_ATTACKING:
            self.perform_attacking(dt)
        elif self.ai_state == AI_STATE_FLEEING:
            self.perform_fleeing()
        elif self.ai_state == AI_STATE_DISABLED:
            self.perform_disabled()

        # Update all weapons
        for weapon in self.weapons:
            weapon.update(dt)

        # Update projectiles
        for projectile in list(self.projectiles):
            if projectile.update(dt):
                projectile.kill()

        self.apply_movement()
        self.rotate_image()

    def apply_movement(self):
        # Apply drag
        if self.vel.length_squared() > 0:
            drag = self.vel.normalize() * AI_DRAG_FACTOR * self.vel.length_squared()
            if drag.length_squared() < self.vel.length_squared():
                self.vel -= drag
            else:
                self.vel = pg.math.Vector2(0,0)

        # Limit speed
        if self.vel.length_squared() > self.max_speed * self.max_speed:
            self.vel.scale_to_length(self.max_speed)

        self.pos += self.vel
        self.rect.center = self.pos

        # World bounds
        if self.pos.x > self.game.camera.width: self.pos.x = 0
        if self.pos.x < 0: self.pos.x = self.game.camera.width
        if self.pos.y > self.game.camera.height: self.pos.y = 0
        if self.pos.y < 0: self.pos.y = self.game.camera.height
        self.rect.center = self.pos

    def perform_idle(self):
        if self.state_timer <= 0:
            self.ai_state = AI_STATE_PATROLLING
            self.state_timer = random.randint(120, 300)

    def perform_patrolling(self):
        if self.state_timer <= 0 or self.patrol_target_pos is None or self.pos.distance_to(self.patrol_target_pos) < 50:
            self.patrol_target_pos = pg.math.Vector2(
                random.randrange(100, self.game.camera.width - 100),
                random.randrange(100, self.game.camera.height - 100)
            )
            self.state_timer = random.randint(300, 600)

        if self.patrol_target_pos:
            target_angle = self.get_angle_to_point(self.patrol_target_pos)
            facing_target = self.turn_toward_angle(target_angle)
            
            current_direction = self.patrol_target_pos - self.pos
            if current_direction.length() > 50:
                angle_diff = abs((self.angle - target_angle) % 360)
                if angle_diff > 180:
                    angle_diff = 360 - angle_diff
                
                if angle_diff < 30:
                    thrust_multiplier = max(0.3, 1.0 - angle_diff / 30.0)
                    self.apply_thrust_in_facing_direction(thrust_multiplier)

        if self.state_timer % 60 == 0:
            self.scan_for_targets()

    def perform_attacking(self, dt=1/60):
        if not self.target_entity or not self.target_entity.alive() or self.health <= 0:
            self.ai_state = AI_STATE_PATROLLING
            self.target_entity = None
            return

        direction_to_target = (self.target_entity.pos - self.pos)
        distance = direction_to_target.length()

        if distance > AI_SENSOR_RANGE * 1.5:
            self.ai_state = AI_STATE_PATROLLING
            self.target_entity = None
            return

        target_angle = self.get_angle_to_point(self.target_entity.pos)
        facing_target = self.turn_toward_angle(target_angle)
        
        if distance > 120:
            angle_diff = abs((self.angle - target_angle) % 360)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
            
            if angle_diff < 45:
                thrust_multiplier = max(0.5, 1.0 - angle_diff / 45.0)
                self.apply_thrust_in_facing_direction(thrust_multiplier)
                
        elif distance < 80:
            self.apply_thrust_in_facing_direction(-0.3)
            
        if distance < self.weapon_range:
            angle_diff = abs((self.angle - target_angle) % 360)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
            
            if angle_diff < 20 and random.random() < 0.2:
                self.fire_weapon(dt)

    def perform_fleeing(self):
        if not self.target_entity or not self.target_entity.alive():
            self.ai_state = AI_STATE_PATROLLING
            return

        direction_from_target = (self.pos - self.target_entity.pos)
        if direction_from_target.length_squared() > 0:
            flee_angle = self.get_angle_to_point(self.pos + direction_from_target.normalize() * 100)
            
            self.turn_toward_angle(flee_angle)
            
            angle_diff = abs((self.angle - flee_angle) % 360)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
            
            if angle_diff < 60:
                self.apply_thrust_in_facing_direction(1.2)

        if self.pos.distance_to(self.target_entity.pos) > AI_SENSOR_RANGE * 2:
            self.ai_state = AI_STATE_PATROLLING
            self.target_entity = None

    def scan_for_targets(self):
        # Check player
        player = self.game.player
        if player and player.alive():
            dist_to_player = self.pos.distance_to(player.pos)
            if dist_to_player < AI_SENSOR_RANGE:
                relation_to_player = self.game.faction_relations.get(self.faction_id, {}).get(self.game.player_faction_id, 0.0)
                if relation_to_player < -0.5:
                    self.target_entity = player
                    self.ai_state = AI_STATE_ATTACKING
                    print(f"{self.faction_id} ship attacking player!")
                    return

        # Check other AI ships
        for ship in self.game.ai_ships:
            if ship == self or not ship.alive(): continue
            dist_to_ship = self.pos.distance_to(ship.pos)
            if dist_to_ship < AI_SENSOR_RANGE:
                relation_to_other_ai = self.game.faction_relations.get(self.faction_id, {}).get(ship.faction_id, 0.0)
                if relation_to_other_ai < -0.5:
                    self.target_entity = ship
                    self.ai_state = AI_STATE_ATTACKING
                    print(f"{self.faction_id} ship ({self.ship_type}) attacking {ship.faction_id} ship ({ship.ship_type})!")
                    return

    def take_damage(self, amount, attacker_faction_id="unknown"):
        if self.shields > 0:
            self.shields -= amount
            if self.shields < 0:
                self.health += self.shields
                self.shields = 0
        else:
            self.health -= amount

        self.shield_recharge_timer = self.shield_recharge_delay

        if self.health <= 0:
            self.kill()
            print(f"{self.ship_type} ({self.faction_id}) destroyed!")
            self.create_explosion()
            self.drop_loot()
        elif self.health < self.max_health * 0.1:
            if self.ai_state != AI_STATE_DISABLED:
                self.ai_state = AI_STATE_DISABLED
                print(f"DEBUG: {self.ship_type} ({self.faction_id}) changed state to AI_STATE_DISABLED.")
                self.game.set_status_message(f"{self.ship_type} disabled! Press B to board.", (255, 255, 0))
        elif self.ai_state != AI_STATE_DISABLED:
            attacker_entity = None

            if attacker_faction_id == "player":
                attacker_entity = self.game.player
            else:
                for ship in self.game.ai_ships:
                    if ship.faction_id == attacker_faction_id:
                        attacker_entity = ship
                        break

            if attacker_entity:
                flee_threshold = 0.3
                if self.ship.size == "small":
                    flee_threshold = 0.4
                elif self.ship.size == "large" or self.ship.size == "capital":
                    flee_threshold = 0.2

                personality_factor = random.uniform(0.8, 1.2)
                flee_threshold *= personality_factor

                if self.health / self.max_health < flee_threshold and self.ai_state != AI_STATE_FLEEING:
                    self.target_entity = attacker_entity
                    self.ai_state = AI_STATE_FLEEING
                    print(f"{self.faction_id} ship fleeing from {attacker_faction_id}!")
                    self.game.set_status_message(f"{self.faction_id} ship fleeing!", (255, 255, 0))
                elif self.ai_state != AI_STATE_ATTACKING:
                    self.target_entity = attacker_entity
                    self.ai_state = AI_STATE_ATTACKING
                    print(f"{self.faction_id} ship fighting back against {attacker_faction_id}!")
                    self.game.set_status_message(f"{self.faction_id} ship attacking!", (255, 100, 100))

    def create_explosion(self):
        """Create an explosion effect when the ship is destroyed."""
        print(f"Explosion at {self.pos}")

    def drop_loot(self):
        """Drop loot when the ship is destroyed."""
        base_loot_value = 100 * (1 + ["small", "medium", "large", "capital"].index(self.ship.size))
        loot_value = int(base_loot_value * random.uniform(0.5, 1.5))

        if hasattr(self.game, 'player') and self.game.player:
            self.game.player.credits += loot_value
            self.game.set_status_message(f"Collected {loot_value} credits from destroyed ship", (0, 255, 0))

        print(f"Dropped {loot_value} credits")

    def perform_disabled(self):
        """Behavior when ship is disabled."""
        if self.vel.length_squared() > 0:
            self.vel *= 0.95

        if random.random() < 0.05:
            print(f"Disabled ship {self.ship_type} emitting sparks")

        if hasattr(self.game, 'player') and self.game.player:
            dist_to_player = self.pos.distance_to(self.game.player.pos)
            if dist_to_player < 100:
                if random.random() < 0.1:
                    self.game.set_status_message(f"Press B to board disabled {self.ship_type}", (255, 255, 0))

        if self.shields < self.max_shields:
            self.shields = min(self.max_shields, self.shields + 0.01)
