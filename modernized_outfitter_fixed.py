"""
FIXED Modernized Outfitter for the JSON-based Outfit System
Fixed ammunition purchasing logic to buy exact quantities and prevent game lockups
"""

import pygame as pg
import os
from pathlib import Path
from game_objects.standardized_outfits import (
    get_outfit_by_id, OUTFITS_REGISTRY,
    OUTFIT_CATEGORY_WEAPONS, OUTFIT_CATEGORY_DEFENSE, OUTFIT_CATEGORY_ENGINES,
    OUTFIT_CATEGORY_ELECTRONICS, OUTFIT_CATEGORY_UTILITY, OUTFIT_CATEGORY_AMMUNITION,
    OUTFIT_CATEGORY_SPECIAL, Weapon, Ammunition
)

# UI Colors
BG_COLOR = (15, 15, 25)
PANEL_COLOR = (25, 25, 40)
PANEL_BORDER = (60, 60, 80)
TITLE_COLOR = (220, 220, 255)
TEXT_COLOR = (200, 200, 220)
SELECTED_COLOR = (120, 80, 200)
BUTTON_COLOR = (50, 70, 120)
BUTTON_TEXT_COLOR = (255, 255, 255)
BUTTON_DISABLED_COLOR = (40, 40, 50)
BUTTON_DISABLED_TEXT_COLOR = (120, 120, 120)

SUCCESS_COLOR = (100, 255, 100)
WARNING_COLOR = (255, 255, 100)
ERROR_COLOR = (255, 100, 100)
NEUTRAL_COLOR = (200, 200, 200)

# Category colors
CATEGORY_COLORS = {
    OUTFIT_CATEGORY_WEAPONS: (255, 120, 120),
    OUTFIT_CATEGORY_AMMUNITION: (255, 180, 100),
    OUTFIT_CATEGORY_DEFENSE: (120, 150, 255),
    OUTFIT_CATEGORY_ENGINES: (120, 255, 120),
    OUTFIT_CATEGORY_ELECTRONICS: (255, 120, 255),
    OUTFIT_CATEGORY_UTILITY: (255, 255, 120),
    OUTFIT_CATEGORY_SPECIAL: (200, 255, 200)
}

class ModernizedOutfitterFixed:
    """Modern outfitter interface with JSON data and image support - FIXED VERSION."""

    def __init__(self, game):
        self.game = game
        self.planet = None
        self.available_outfits = []
        self.player_outfits = []
        self.current_tab = "buy"
        self.current_category = OUTFIT_CATEGORY_WEAPONS
        self.selected_outfit = None
        self.scroll_offset = 0
        self.max_visible_items = 6
        self.ui_rects = {}
        self.outfit_item_rects = []
        self.outfit_images = {}  # Cache for loaded images
        self.ammo_quantity = 1  # For ammo purchases - this is individual missiles/rounds
        
        # Add debug mode
        self.debug_mode = True

    def debug_print(self, message):
        """Print debug messages if debug mode is enabled"""
        if self.debug_mode:
            print(f"[OUTFITTER DEBUG] {message}")

    def open(self, planet):
        """Open outfitter on planet."""
        self.planet = planet
        if planet.tech_level == 0:
            self.game.set_status_message("This planet has no outfitter.", ERROR_COLOR)
            return False
        self._update_available_outfits()
        self._update_player_outfits()
        return True

    def _update_available_outfits(self):
        """Update available outfits based on tech level."""
        self.available_outfits = []
        self.debug_print(f"Checking {len(OUTFITS_REGISTRY)} outfits from JSON...")
        
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            tech_level_req = getattr(outfit, 'min_tech_level', 1)
            if tech_level_req <= self.planet.tech_level:
                # Check ship size compatibility if the outfit has restrictions
                if hasattr(outfit, 'ship_size_restrictions') and outfit.ship_size_restrictions:
                    if self.game.player.ship.size not in outfit.ship_size_restrictions:
                        continue
                self.available_outfits.append(outfit)
                self.debug_print(f"  ✅ {outfit.name} (Tech {tech_level_req})")
            else:
                self.debug_print(f"  ❌ {outfit.name} (Tech {tech_level_req} > {self.planet.tech_level})")
        
        # Sort by category, then by cost
        self.available_outfits.sort(key=lambda x: (getattr(x, 'category', 'unknown'), getattr(x, 'cost', 0)))
        self.debug_print(f"Total available: {len(self.available_outfits)} outfits")

    def _update_player_outfits(self):
        """Update player's installed outfits."""
        self.player_outfits = []
        for outfit_id, quantity in self.game.player.installed_outfits.items():
            outfit = get_outfit_by_id(outfit_id)
            if outfit and quantity > 0:
                self.player_outfits.append((outfit, quantity))
        self.player_outfits.sort(key=lambda x: (getattr(x[0], 'category', 'unknown'), x[0].name))

    def _get_outfits_in_category(self, category):
        """Get outfits in current category for current tab."""
        if self.current_tab == "buy":
            return [o for o in self.available_outfits if getattr(o, 'category', 'unknown') == category]
        else:
            return [(o, q) for o, q in self.player_outfits if getattr(o, 'category', 'unknown') == category]

    def _load_outfit_image(self, outfit, image_type="icon"):
        """Load outfit image with caching."""
        cache_key = f"{outfit.id}_{image_type}"
        if cache_key in self.outfit_images:
            return self.outfit_images[cache_key]

        image_path = ""
        if image_type == "icon":
            image_path = getattr(outfit, 'outfitter_icon', '')
        elif image_type == "image":
            image_path = getattr(outfit, 'outfitter_image', '')

        if not image_path:
            # Return None if no image path specified
            self.outfit_images[cache_key] = None
            return None

        try:
            # Try to load the image
            if not os.path.isabs(image_path):
                # Convert relative path to absolute
                full_path = os.path.join(os.getcwd(), image_path)
            else:
                full_path = image_path

            if os.path.exists(full_path):
                image = pg.image.load(full_path).convert_alpha()
                self.outfit_images[cache_key] = image
                return image
            else:
                # File doesn't exist - return None for default placeholder
                self.outfit_images[cache_key] = None
                return None

        except Exception as e:
            # Error loading - return None for default placeholder
            self.outfit_images[cache_key] = None
            return None

    def run(self, screen):
        """Main outfitter loop."""
        clock = pg.time.Clock()
        running = True

        while running and self.game.running:
            clock.tick(60)

            for event in pg.event.get():
                if event.type == pg.QUIT:
                    self.game.running = False
                    return "QUIT"
                elif event.type == pg.KEYDOWN:
                    if event.key == pg.K_ESCAPE:
                        # Clear any status messages and return
                        self.game.status_message = ""
                        self.game.status_message_timer = 0
                        return "DOCKED"  # Return to docked state
                elif event.type == pg.MOUSEBUTTONDOWN:
                    if event.button == 1:
                        result = self._handle_click(pg.mouse.get_pos())
                        if result == "DOCKED":
                            return "DOCKED"
                elif event.type == pg.MOUSEWHEEL:
                    self._handle_scroll(event.y)

            # Update status message timer
            if hasattr(self.game, 'status_message_timer') and self.game.status_message_timer > 0:
                self.game.status_message_timer -= 1
                if self.game.status_message_timer <= 0:
                    self.game.status_message = ""

            self._draw(screen)
            pg.display.flip()

        return "DOCKED"  # Default return if loop exits

    def _handle_click(self, mouse_pos):
        """Handle mouse clicks."""
        # Tab buttons
        if 'tab_buy' in self.ui_rects and self.ui_rects['tab_buy'].collidepoint(mouse_pos):
            self.current_tab = "buy"
            self.selected_outfit = None
            self.scroll_offset = 0
        elif 'tab_sell' in self.ui_rects and self.ui_rects['tab_sell'].collidepoint(mouse_pos):
            self.current_tab = "sell"
            self.selected_outfit = None
            self.scroll_offset = 0

        # Category buttons
        for category in CATEGORY_COLORS.keys():
            if f'cat_{category}' in self.ui_rects and self.ui_rects[f'cat_{category}'].collidepoint(mouse_pos):
                self.current_category = category
                self.selected_outfit = None
                self.scroll_offset = 0

        # Outfit list items
        for i, rect in enumerate(self.outfit_item_rects):
            if rect.collidepoint(mouse_pos):
                self._select_outfit_at_index(i)
                break

        # Action button
        if 'action_button' in self.ui_rects and self.ui_rects['action_button'].collidepoint(mouse_pos):
            if self.selected_outfit:
                if self.current_tab == "buy":
                    self._buy_outfit_fixed()
                else:
                    self._sell_outfit()
                    
        # Quantity buttons for ammo
        if self.selected_outfit and hasattr(self.selected_outfit, 'category') and self.selected_outfit.category == OUTFIT_CATEGORY_AMMUNITION:
            if 'qty_minus' in self.ui_rects and self.ui_rects['qty_minus'].collidepoint(mouse_pos):
                self.ammo_quantity = max(1, self.ammo_quantity - 1)
            elif 'qty_plus' in self.ui_rects and self.ui_rects['qty_plus'].collidepoint(mouse_pos):
                self.ammo_quantity = min(99, self.ammo_quantity + 1)

        # Back button
        if 'back_button' in self.ui_rects and self.ui_rects['