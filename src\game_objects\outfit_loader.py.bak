"""
Outfit Loader for Escape Velocity Py
This module handles loading outfit data from JSON files to override hardcoded values
"""

import json
import os
from .standardized_outfits import OUTFITS_REGISTRY

def load_outfits_from_json(json_file_path="outfits_data.json"):
    """
    Load outfit data from JSON file and update the registry.
    This allows the editor changes to persist in the game.
    """
    # Check if the file exists relative to the main game directory
    if not os.path.isabs(json_file_path):
        # Go up from src/game_objects to the main game directory
        current_dir = os.path.dirname(__file__)
        game_root = os.path.dirname(os.path.dirname(current_dir))
        json_file_path = os.path.join(game_root, json_file_path)
    
    if not os.path.exists(json_file_path):
        print(f"Outfit JSON file not found: {json_file_path}")
        return False
    
    try:
        with open(json_file_path, 'r') as f:
            saved_data = json.load(f)
        
        # Update existing outfits with saved data
        updates_applied = 0
        for outfit_id, outfit_data in saved_data.items():
            if outfit_id in OUTFITS_REGISTRY:
                outfit = OUTFITS_REGISTRY[outfit_id]
                
                # Update basic properties
                for prop in ['name', 'cost', 'space_required', 'min_tech_level', 'outfitter_icon', 'description']:
                    if prop in outfit_data and hasattr(outfit, prop):
                        old_value = getattr(outfit, prop)
                        new_value = outfit_data[prop]
                        if old_value != new_value:
                            setattr(outfit, prop, new_value)
                            print(f"Updated {outfit_id}.{prop}: {old_value} -> {new_value}")
                
                # Update category-specific properties
                if outfit.category == "weapons":
                    for prop in ['mount_type', 'damage', 'fire_rate', 'range', 'energy_usage', 'uses_ammo', 'ammo_type', 'max_ammo']:
                        if prop in outfit_data and hasattr(outfit, prop):
                            old_value = getattr(outfit, prop)
                            new_value = outfit_data[prop]
                            if old_value != new_value:
                                setattr(outfit, prop, new_value)
                                print(f"Updated {outfit_id}.{prop}: {old_value} -> {new_value}")
                
                elif outfit.category == "ammunition":
                    for prop in ['ammo_type', 'quantity', 'damage', 'projectile_speed', 'projectile_behavior', 'tracking_strength', 'explosion_radius']:
                        if prop in outfit_data and hasattr(outfit, prop):
                            old_value = getattr(outfit, prop)
                            new_value = outfit_data[prop]
                            if old_value != new_value:
                                setattr(outfit, prop, new_value)
                                print(f"Updated {outfit_id}.{prop}: {old_value} -> {new_value}")
                
                elif outfit.category == "defense":
                    for prop in ['shield_boost', 'armor_boost', 'shield_recharge_boost', 'damage_reduction']:
                        if prop in outfit_data and hasattr(outfit, prop):
                            old_value = getattr(outfit, prop)
                            new_value = outfit_data[prop]
                            if old_value != new_value:
                                setattr(outfit, prop, new_value)
                                print(f"Updated {outfit_id}.{prop}: {old_value} -> {new_value}")
                
                elif outfit.category == "engines":
                    for prop in ['acceleration_boost', 'max_speed_boost', 'turn_rate_boost', 'fuel_efficiency', 'energy_drain']:
                        if prop in outfit_data and hasattr(outfit, prop):
                            old_value = getattr(outfit, prop)
                            new_value = outfit_data[prop]
                            if old_value != new_value:
                                setattr(outfit, prop, new_value)
                                print(f"Updated {outfit_id}.{prop}: {old_value} -> {new_value}")
                
                elif outfit.category == "electronics":
                    for prop in ['sensor_range_boost', 'targeting_boost', 'jamming_strength', 'communication_range', 'energy_drain', 'scan_speed_boost']:
                        if prop in outfit_data and hasattr(outfit, prop):
                            old_value = getattr(outfit, prop)
                            new_value = outfit_data[prop]
                            if old_value != new_value:
                                setattr(outfit, prop, new_value)
                                print(f"Updated {outfit_id}.{prop}: {old_value} -> {new_value}")
                
                elif outfit.category == "utility":
                    for prop in ['cargo_space_boost', 'fuel_capacity_boost', 'crew_capacity_boost', 'life_support_boost', 'energy_generation', 'energy_storage_boost']:
                        if prop in outfit_data and hasattr(outfit, prop):
                            old_value = getattr(outfit, prop)
                            new_value = outfit_data[prop]
                            if old_value != new_value:
                                setattr(outfit, prop, new_value)
                                print(f"Updated {outfit_id}.{prop}: {old_value} -> {new_value}")
                
                updates_applied += 1
            else:
                print(f"Warning: Outfit '{outfit_id}' found in JSON but not in registry")
        
        print(f"Successfully loaded outfit data from {json_file_path}")
        print(f"Applied updates to {updates_applied} outfits")
        return True
    
    except Exception as e:
        print(f"Failed to load outfit data from JSON: {e}")
        return False

def save_outfits_to_json(json_file_path="outfits_data.json"):
    """
    Save current outfit data to JSON file.
    This creates a backup of the current state.
    """
    # Check if the file path is relative
    if not os.path.isabs(json_file_path):
        # Go up from src/game_objects to the main game directory
        current_dir = os.path.dirname(__file__)
        game_root = os.path.dirname(os.path.dirname(current_dir))
        json_file_path = os.path.join(game_root, json_file_path)
    
    try:
        outfits_data = {}
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            outfit_data = {
                'id': outfit.id,
                'name': outfit.name,
                'category': outfit.category,
                'subcategory': getattr(outfit, 'subcategory', ''),
                'cost': getattr(outfit, 'cost', 1000),
                'space_required': getattr(outfit, 'space_required', 1),
                'min_tech_level': getattr(outfit, 'min_tech_level', 1),
                'outfitter_icon': getattr(outfit, 'outfitter_icon', ''),
                'description': getattr(outfit, 'description', ''),
            }

            # Add category-specific properties
            if outfit.category == "weapons":
                outfit_data.update({
                    'mount_type': getattr(outfit, 'mount_type', 'fixed'),
                    'damage': getattr(outfit, 'damage', 10),
                    'fire_rate': getattr(outfit, 'fire_rate', 1.0),
                    'range': getattr(outfit, 'range', 300),
                    'energy_usage': getattr(outfit, 'energy_usage', 5),
                    'uses_ammo': getattr(outfit, 'uses_ammo', False),
                    'ammo_type': getattr(outfit, 'ammo_type', ''),
                    'max_ammo': getattr(outfit, 'max_ammo', 0)
                })
            elif outfit.category == "ammunition":
                outfit_data.update({
                    'ammo_type': getattr(outfit, 'ammo_type', ''),
                    'quantity': getattr(outfit, 'quantity', 10),
                    'damage': getattr(outfit, 'damage', 50),
                    'projectile_speed': getattr(outfit, 'projectile_speed', 8),
                    'projectile_behavior': getattr(outfit, 'projectile_behavior', 'dumbfire'),
                    'tracking_strength': getattr(outfit, 'tracking_strength', 0.0),
                    'explosion_radius': getattr(outfit, 'explosion_radius', 20)
                })
            elif outfit.category == "defense":
                outfit_data.update({
                    'shield_boost': getattr(outfit, 'shield_boost', 0),
                    'armor_boost': getattr(outfit, 'armor_boost', 0),
                    'shield_recharge_boost': getattr(outfit, 'shield_recharge_boost', 0.0),
                    'damage_reduction': getattr(outfit, 'damage_reduction', 0.0)
                })

            outfits_data[outfit_id] = outfit_data

        with open(json_file_path, 'w') as f:
            json.dump(outfits_data, f, indent=2)

        print(f"Saved {len(outfits_data)} outfits to {json_file_path}")
        return True

    except Exception as e:
        print(f"Failed to save outfits to JSON: {e}")
        return False
