# Sprite Assets for Escape Velocity Py

This directory contains all the sprite assets for the Escape Velocity Py game. The sprites are organized into subdirectories by entity type.

## Directory Structure

```
sprites/
├── ships/
│   ├── small/
│   ├── medium/
│   ├── large/
│   └── capital/
├── planets/
├── stations/
├── projectiles/
└── effects/
```

## Creating Spritesheets

Use the `generate_sprite_sheet_improved.py` tool in the root directory to create spritesheets for your game entities. This tool will:

1. Take a single image as input
2. Generate a spritesheet with multiple rotation frames
3. Create a metadata JSON file with frame information

### Example Usage

1. Create a base image for your ship/planet/station
2. Run the spritesheet generator:
   ```
   python generate_sprite_sheet_improved.py
   ```
3. Select your input image
4. Choose the appropriate settings:
   - For ships: 32 frames, square layout
   - For planets: 8-16 frames, horizontal layout
   - For stations: 1-8 frames, horizontal layout
5. Save the output to the appropriate directory based on the entity type and size

## Naming Conventions

See the `NAMING_CONVENTION.md` file in this directory for detailed information on how to name your sprite assets.

## Adding New Sprites

To add new sprites to the game:

1. Create your base image (PNG format recommended)
2. Use the spritesheet generator to create a spritesheet
3. Place the spritesheet and metadata files in the appropriate directory
4. The game will automatically load the sprites based on entity type and ID

## Recommended Sprite Sizes

- **Small Ships**: 128x128 or 256x256 pixels per frame
- **Medium Ships**: 256x256 or 512x512 pixels per frame
- **Large Ships**: 512x512 pixels per frame
- **Capital Ships**: 512x512 or 1024x1024 pixels per frame
- **Planets**: 256x256 to 1024x1024 pixels per frame
- **Stations**: 256x256 to 1024x1024 pixels per frame
- **Projectiles**: 64x64 to 128x128 pixels per frame
- **Effects**: 128x128 to 512x512 pixels per frame

## Tips for Creating Good Sprites

1. **Use transparent backgrounds** (PNG format)
2. **Center your objects** in the image
3. **Use consistent lighting** for all frames
4. **Keep a consistent scale** across similar entities
5. **Use high resolution** source images when possible
6. **Test with different frame counts** to find the right balance between smoothness and file size
