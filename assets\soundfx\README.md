# Sound Effects Directory

This directory contains sound effects for the Escape Velocity Py game.

## Directory Structure

- `weapons/` - Weapon firing sounds
- `engines/` - Engine and thruster sounds  
- `ambient/` - Background and ambient sounds

## Supported Formats

The game supports the following audio formats:
- MP3 (.mp3)
- WAV (.wav)
- OGG (.ogg)

## Usage

### Weapon Sounds
Place weapon firing sound effects in the `weapons/` directory. These can be assigned to weapons using the enhanced editor.

Example weapon sound files:
- `laser_fire.mp3` - Basic laser weapon
- `missile_launch.wav` - Missile launcher
- `plasma_cannon.ogg` - Plasma weapon
- `railgun_shot.mp3` - Railgun weapon

### Engine Sounds
Place engine and thruster sounds in the `engines/` directory.

### Ambient Sounds
Place background and ambient sounds in the `ambient/` directory.

## Adding Sound Effects

1. Place your sound files in the appropriate subdirectory
2. Open the enhanced editor (`enhanced_editor_refactored.py`)
3. Go to the Weapons tab
4. Select a weapon to edit
5. In the Sound Effects section, use the dropdown or Browse button to select a sound file
6. Use the Preview button to test the sound
7. Save the weapon

The sound will now play when that weapon is fired in-game.

## Volume Control

Sound effects volume can be controlled through the SoundManager class in the game code. The system supports:
- Master volume control
- Sound effects volume control
- Individual sound volume overrides

## Performance Notes

- Sound files are cached after first load for better performance
- Larger sound files may cause brief pauses during first playback
- For best performance, use compressed formats like MP3 or OGG
- Keep weapon sound files short (under 2 seconds) for responsive gameplay
