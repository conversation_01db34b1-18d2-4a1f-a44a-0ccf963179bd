"""
Sync Script: Export hardcoded example outfits to JSON files
This will create the initial JSON files that both the game and editor can use
"""

import sys
import os
import json

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("=== SYNCING HARDCODED OUTFITS TO JSON ===")

try:
    # Import the game's outfit system
    from game_objects.standardized_outfits import OUTFITS_REGISTRY, register_outfit
    from game_objects.standardized_outfits import *
    
    # Clear any existing registry
    OUTFITS_REGISTRY.clear()
    
    # Import and create the hardcoded examples
    from game_objects.example_outfits import create_example_outfits
    create_example_outfits()
    
    print(f"Created {len(OUTFITS_REGISTRY)} hardcoded outfits")
    
    # Now export them to JSON using the data loader's save function
    from game_objects.outfit_data_loader import save_current_outfits_to_file
    
    # Save to the game root directory
    success = save_current_outfits_to_file("outfits_data.json")
    
    if success:
        print("✅ Successfully exported hardcoded outfits to outfits_data.json")
        print("Both the game and editor should now see the same outfits!")
        
        # Verify the file was created
        json_path = os.path.join(os.path.dirname(__file__), "outfits_data.json")
        if os.path.exists(json_path):
            with open(json_path, 'r') as f:
                data = json.load(f)
            print(f"✅ Verified: JSON file contains {len(data)} outfits")
            print(f"   Sample outfits: {list(data.keys())[:5]}")
        else:
            print("❌ JSON file was not created at expected location")
    else:
        print("❌ Failed to export outfits to JSON")
        
except Exception as e:
    print(f"❌ Error during sync: {e}")
    import traceback
    traceback.print_exc()

print("=== SYNC COMPLETE ===")
