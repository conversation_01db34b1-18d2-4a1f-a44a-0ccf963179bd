import pygame as pg
import random

class System:
    def __init__(self, system_id, name, pos_x, pos_y, faction=None, description=""):
        self.id = system_id
        self.name = name
        self.pos = pg.math.Vector2(pos_x, pos_y) # Position on the galaxy map
        self.faction = faction
        self.description = description
        self.planets = [] # List of Planet objects in this system
        self.connections = [] # List of IDs of connected systems (hyperlanes)
        self.is_current_system = False # For highlighting on map
        self.is_explored = False # For fog of war
        self.map_rect = None # For clicking on the map

    def add_planet(self, planet):
        self.planets.append(planet)

    def add_connection(self, target_system_id):
        if target_system_id not in self.connections:
            self.connections.append(target_system_id)

    def __repr__(self):
        return f"System(ID: {self.id}, Name: {self.name}, Pos: {self.pos}, Connections: {len(self.connections)})"
