import sys
import os

# If this script is run directly, add the 'src' directory to sys.path
# Get the absolute path of the current script
current_script_path = os.path.abspath(__file__)
# Navigate up two levels to get to the 'src' directory
# (from game_objects/test_shipyard_import.py to game_objects/ to src/)
src_dir = os.path.dirname(os.path.dirname(current_script_path))
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

try:
    from game_objects.ships import SHIPS, get_ship_by_id
    print("SUCCESS: game_objects.ships imported successfully.")
except ImportError as e:
    print(f"FAILURE: Could not import game_objects.ships.")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"FAILURE: An unexpected error occurred during import.")
    import traceback
    traceback.print_exc()
