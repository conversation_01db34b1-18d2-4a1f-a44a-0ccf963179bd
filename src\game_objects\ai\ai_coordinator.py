"""
AI Coordinator - Main AI class that orchestrates all AI subsystems
Provides a clean interface between the game and the modular AI system.
"""
import pygame as pg
from .systems import AI<PERSON>ensor<PERSON><PERSON><PERSON>, AIStateManager, AIMovementManager, AIWeaponManager
from .core.ai_constants import AI_STATE_IDLE, AI_STATE_ATTACKING

class AICoordinator:
    """
    Main AI coordinator that manages all AI subsystems.
    Provides a unified interface for AI behavior while maintaining modular architecture.
    """
    
    def __init__(self, ai_ship):
        """
        Initialize AI coordinator with all subsystems.
        
        Args:
            ai_ship: The AI ship this coordinator manages
        """
        self.ship = ai_ship
        self.game_time = 0
        
        # Initialize all subsystems
        self.sensor_manager = AISensorManager(ai_ship)
        self.state_manager = AIStateManager(ai_ship)
        self.movement_manager = AIMovementManager(ai_ship)
        self.weapon_manager = AIWeaponManager(ai_ship)
        
        # Make subsystems available to ship for backward compatibility
        ai_ship.sensor_manager = self.sensor_manager
        ai_ship.state_manager = self.state_manager
        ai_ship.movement_manager = self.movement_manager
        ai_ship.weapon_manager = self.weapon_manager
        
        # Performance tracking
        self.update_count = 0
        self.last_performance_check = 0
        
        print(f"AI Coordinator initialized for {getattr(ai_ship, 'ship_type', 'unknown')} ship")
    
    def update(self, dt=1/60):
        """
        Main update method that coordinates all AI subsystems.
        
        Args:
            dt: Delta time (default 60 FPS)
        """
        self.game_time += 1
        self.update_count += 1
        
        # Update all subsystems in order
        self.sensor_manager.update(dt, self.game_time)
        self.state_manager.update(dt, self.game_time)
        
        # Get current state and target for other systems
        current_state = self.state_manager.get_current_state()
        current_target = self.sensor_manager.get_current_target()
        
        # Update movement based on state and target
        self.movement_manager.update(dt, current_state, current_target)
        
        # Update weapon system
        self.weapon_manager.update(dt, self.game_time)
        
        # Handle combat if in attacking state
        if current_state == AI_STATE_ATTACKING and current_target:
            self.handle_combat(current_target)
        
        # Performance monitoring
        if self.game_time - self.last_performance_check > 3600:  # Every minute
            self.check_performance()
            self.last_performance_check = self.game_time
    
    def handle_combat(self, target):
        """
        Handle combat behavior when attacking a target.
        
        Args:
            target: The target entity to attack
        """
        if not target or not hasattr(target, 'pos'):
            return
        
        # Try to fire at target
        if self.weapon_manager.can_fire_at_target(target):
            self.weapon_manager.fire_at_target(target, self.game_time)
    
    def check_performance(self):
        """Check and report AI performance metrics."""
        updates_per_minute = self.update_count
        self.update_count = 0
        
        # Log performance if it seems problematic
        if updates_per_minute > 4000:  # More than ~67 FPS
            print(f"Warning: High AI update rate for {self.ship.ship_type}: {updates_per_minute}/min")
    
    def get_ai_state(self):
        """Get current AI state (for backward compatibility)."""
        return self.state_manager.get_current_state()
    
    def set_ai_state(self, new_state):
        """Set AI state (for external control)."""
        self.state_manager.force_state(new_state)
    
    def get_target_entity(self):
        """Get current target (for backward compatibility)."""
        return self.sensor_manager.get_current_target()
    
    def force_target(self, target):
        """Force a specific target (for external control)."""
        self.sensor_manager.current_target = target
        if target:
            self.state_manager.force_state(AI_STATE_ATTACKING)
    
    def get_debug_info(self):
        """
        Get comprehensive debug information about AI state.
        
        Returns:
            dict: Debug information from all subsystems
        """
        return {
            'ship_type': getattr(self.ship, 'ship_type', 'unknown'),
            'faction': getattr(self.ship, 'faction_id', 'unknown'),
            'state': self.state_manager.get_current_state(),
            'state_timer': self.state_manager.get_state_timer(),
            'target': str(self.sensor_manager.get_current_target()),
            'threat_level': self.sensor_manager.get_threat_level(),
            'hostiles': self.sensor_manager.get_hostile_count(),
            'allies': self.sensor_manager.get_ally_count(),
            'movement': self.movement_manager.get_movement_info(),
            'weapon': self.weapon_manager.get_weapon_info(),
            'weapon_count': self.weapon_manager.get_weapon_count(),
            'health_ratio': self.ship.health / self.ship.max_health if self.ship.max_health > 0 else 0,
            'position': (int(self.ship.pos.x), int(self.ship.pos.y)),
            'velocity': round(self.ship.vel.length(), 1),
            'game_time': self.game_time,
            'update_count': self.update_count
        }
    
    def print_debug_info(self):
        """Print debug information to console."""
        info = self.get_debug_info()
        print(f"\n=== AI Debug: {info['ship_type']} ({info['faction']}) ===")
        print(f"State: {info['state']} (timer: {info['state_timer']})")
        print(f"Target: {info['target']}")
        print(f"Threat: {info['threat_level']:.2f} | Hostiles: {info['hostiles']} | Allies: {info['allies']}")
        print(f"Movement: {info['movement']}")
        print(f"Weapon: {info['weapon']}")
        print(f"Health: {info['health_ratio']:.2f} | Pos: {info['position']} | Vel: {info['velocity']}")
        print(f"Performance: {info['update_count']} updates, game time: {info['game_time']}")
    
    def get_state_history(self):
        """Get state transition history for debugging."""
        return self.state_manager.get_transition_history()
    
    def force_sensor_scan(self):
        """Force an immediate sensor scan (for debugging)."""
        self.sensor_manager.force_scan()
    
    def is_healthy(self):
        """
        Check if AI is functioning properly.
        
        Returns:
            bool: True if AI appears to be working correctly
        """
        # Check if all subsystems are present
        if not all([
            self.sensor_manager,
            self.state_manager, 
            self.movement_manager,
            self.weapon_manager
        ]):
            return False
        
        # Check if ship has basic properties
        if not hasattr(self.ship, 'pos') or not hasattr(self.ship, 'vel'):
            return False
        
        # Check if state is valid
        current_state = self.state_manager.get_current_state()
        if current_state not in ['IDLE', 'PATROLLING', 'ATTACKING', 'FLEEING', 'DISABLED', 'TRADING']:
            return False
        
        return True
    
    def cleanup(self):
        """Clean up AI coordinator and subsystems."""
        # Clear references to prevent memory leaks
        if hasattr(self.ship, 'sensor_manager'):
            delattr(self.ship, 'sensor_manager')
        if hasattr(self.ship, 'state_manager'):
            delattr(self.ship, 'state_manager')
        if hasattr(self.ship, 'movement_manager'):
            delattr(self.ship, 'movement_manager')
        if hasattr(self.ship, 'weapon_manager'):
            delattr(self.ship, 'weapon_manager')
        
        self.sensor_manager = None
        self.state_manager = None
        self.movement_manager = None
        self.weapon_manager = None
        
        print(f"AI Coordinator cleaned up for {getattr(self.ship, 'ship_type', 'unknown')} ship")

# Convenience function for creating AI coordinators
def create_ai_coordinator(ai_ship):
    """
    Create and initialize an AI coordinator for a ship.
    
    Args:
        ai_ship: The ship to add AI to
        
    Returns:
        AICoordinator: Initialized AI coordinator
    """
    coordinator = AICoordinator(ai_ship)
    
    # Set initial AI state
    coordinator.set_ai_state(AI_STATE_IDLE)
    
    return coordinator
