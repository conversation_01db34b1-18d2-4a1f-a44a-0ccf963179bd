"""
AI Utilities - Helper functions used across the AI system
Eliminates duplicate code and provides consistent behavior
"""
import random
import pygame as pg
from .ai_constants import (
    DISABLE_THRESHOLD, 
    FLEE_THRESHOLDS, 
    FLEE_THRESHOLDS_BY_SIZE,
    UPDATE_STAGGER_OFFSET
)

def get_weapon_damage(weapon):
    """
    Get weapon damage values, handling both new and legacy damage systems.
    
    Args:
        weapon: The weapon object
        
    Returns:
        tuple: (shield_damage, armor_damage)
    """
    shield_damage = getattr(weapon, 'shield_damage', 0)
    armor_damage = getattr(weapon, 'armor_damage', 0)
    
    # Fallback to legacy damage system if new system isn't defined
    if shield_damage == 0 and armor_damage == 0:
        legacy_damage = getattr(weapon, 'damage', 10)
        shield_damage = legacy_damage
        armor_damage = legacy_damage
    
    return shield_damage, armor_damage

def calculate_disable_state(shields, armor, max_armor):
    """
    Consistent disable state calculation.
    
    Args:
        shields: Current shield value
        armor: Current armor value  
        max_armor: Maximum armor value
        
    Returns:
        bool: True if ship should be disabled
    """
    # Ship is disabled when shields are 0 AND armor is at or below threshold
    if shields <= 0:
        armor_ratio = armor / max_armor if max_armor > 0 else 0
        return armor_ratio <= DISABLE_THRESHOLD
    return False

def should_flee(current_health, max_health, personality, ship_size):
    """
    Determine if a ship should flee based on health, personality, and size.
    
    Args:
        current_health: Current health value
        max_health: Maximum health value
        personality: AI personality type
        ship_size: Ship size category
        
    Returns:
        bool: True if ship should flee
    """
    if max_health <= 0:
        return True
    
    health_ratio = current_health / max_health
    
    # Get base threshold from personality
    base_threshold = FLEE_THRESHOLDS.get(personality, 0.3)
    
    # Modify by ship size
    size_threshold = FLEE_THRESHOLDS_BY_SIZE.get(ship_size, 0.3)
    
    # Use the more conservative (higher) threshold
    final_threshold = max(base_threshold, size_threshold)
    
    # Add some randomness (±20%)
    personality_factor = random.uniform(0.8, 1.2)
    final_threshold *= personality_factor
    
    return health_ratio < final_threshold

def stagger_update_timer(ship_id, base_interval):
    """
    Create staggered update intervals to prevent all ships from updating on the same frame.
    
    Args:
        ship_id: Unique identifier for the ship (can be object id or index)
        base_interval: Base interval between updates
        
    Returns:
        int: Staggered interval for this specific ship
    """
    # Use hash of ship_id to create consistent but varied offsets
    ship_hash = hash(str(ship_id)) % UPDATE_STAGGER_OFFSET
    return base_interval + ship_hash

def normalize_angle(angle):
    """
    Normalize angle to 0-360 range.
    
    Args:
        angle: Angle in degrees
        
    Returns:
        float: Normalized angle (0-360)
    """
    return angle % 360

def angle_difference(angle1, angle2):
    """
    Calculate the shortest angular difference between two angles.
    
    Args:
        angle1: First angle in degrees
        angle2: Second angle in degrees
        
    Returns:
        float: Angular difference (-180 to 180)
    """
    diff = (angle2 - angle1) % 360
    if diff > 180:
        diff -= 360
    return diff

def distance_squared(pos1, pos2):
    """
    Calculate squared distance between two positions (faster than distance).
    
    Args:
        pos1: First position (Vector2 or tuple)
        pos2: Second position (Vector2 or tuple)
        
    Returns:
        float: Squared distance
    """
    dx = pos1[0] - pos2[0] if hasattr(pos1, '__getitem__') else pos1.x - pos2.x
    dy = pos1[1] - pos2[1] if hasattr(pos1, '__getitem__') else pos1.y - pos2.y
    return dx * dx + dy * dy

def clamp(value, min_value, max_value):
    """
    Clamp a value between min and max.
    
    Args:
        value: Value to clamp
        min_value: Minimum allowed value
        max_value: Maximum allowed value
        
    Returns:
        float: Clamped value
    """
    return max(min_value, min(value, max_value))

def lerp(start, end, factor):
    """
    Linear interpolation between two values.
    
    Args:
        start: Starting value
        end: Ending value  
        factor: Interpolation factor (0-1)
        
    Returns:
        float: Interpolated value
    """
    return start + (end - start) * clamp(factor, 0.0, 1.0)

def get_faction_relation(faction_relations, faction1, faction2):
    """
    Get the relationship value between two factions.
    
    Args:
        faction_relations: The game's faction relations dictionary
        faction1: First faction ID
        faction2: Second faction ID
        
    Returns:
        float: Relation value (-1.0 to 1.0, default 0.0)
    """
    return faction_relations.get(faction1, {}).get(faction2, 0.0)

def is_hostile(faction_relations, faction1, faction2, threshold=-0.5):
    """
    Check if two factions are hostile to each other.
    
    Args:
        faction_relations: The game's faction relations dictionary
        faction1: First faction ID
        faction2: Second faction ID
        threshold: Hostility threshold (default -0.5)
        
    Returns:
        bool: True if factions are hostile
    """
    relation = get_faction_relation(faction_relations, faction1, faction2)
    return relation < threshold

def choose_random_position_in_circle(center, radius):
    """
    Choose a random position within a circle.
    
    Args:
        center: Center position (Vector2)
        radius: Circle radius
        
    Returns:
        Vector2: Random position within circle
    """
    angle = random.uniform(0, 2 * 3.14159)
    distance = random.uniform(0, radius)
    
    x = center.x + distance * pg.math.Vector2(1, 0).rotate_rad(angle).x
    y = center.y + distance * pg.math.Vector2(1, 0).rotate_rad(angle).y
    
    return pg.math.Vector2(x, y)
