"""
AI State System - Manages AI state transitions and behavior execution
Prevents rapid state switching and ensures smooth AI behavior.
"""
import random
from ..core.ai_constants import (
    AI_STATE_IDLE,
    AI_STATE_PATROLLING, 
    AI_STATE_ATTACKING,
    AI_STATE_FLEEING,
    AI_STATE_DISABLED,
    AI_STATE_TRADING,
    STATE_TRANSITION_COOLDOWN,
    ALL_AI_STATES
)
from ..core.ai_utils import calculate_disable_state, should_flee

class AIStateManager:
    """
    Manages AI state transitions with cooldowns to prevent rapid switching.
    Ensures consistent and believable AI behavior.
    """
    
    def __init__(self, ai_ship):
        self.ship = ai_ship
        self.current_state = AI_STATE_IDLE
        self.previous_state = None
        self.state_timer = 0
        self.last_transition_time = 0
        self.transition_cooldown = STATE_TRANSITION_COOLDOWN
        
        # State-specific data
        self.state_data = {}
        
        # Transition history for debugging
        self.transition_history = []
        
    def update(self, dt, game_time):
        """
        Update state system and check for transitions.
        
        Args:
            dt: Delta time
            game_time: Current game time in frames
        """
        self.state_timer += 1
        
        # Check for forced state changes (health-based)
        forced_state = self._check_forced_transitions()
        if forced_state:
            self._force_transition(forced_state, game_time)
            return
        
        # Check for normal state transitions (with cooldown)
        if self._can_transition(game_time):
            new_state = self._evaluate_state_transitions()
            if new_state and new_state != self.current_state:
                self._transition_to_state(new_state, game_time)
    
    def _check_forced_transitions(self):
        """
        Check for transitions that must happen immediately (health-based).
        
        Returns:
            str: Forced state or None
        """
        # Death check
        if self.ship.health <= 0:
            return None  # Ship is dead, no state needed
        
        # Disable check using consistent logic
        if calculate_disable_state(self.ship.shields, self.ship.health, self.ship.max_health):
            return AI_STATE_DISABLED
        
        return None
    
    def _can_transition(self, game_time):
        """
        Check if enough time has passed since last transition.
        
        Args:
            game_time: Current game time in frames
            
        Returns:
            bool: True if transition is allowed
        """
        return (game_time - self.last_transition_time) >= self.transition_cooldown
    
    def _evaluate_state_transitions(self):
        """
        Evaluate what state the AI should transition to.
        
        Returns:
            str: New state or None to stay in current state
        """
        # Get sensor data if available
        sensor_manager = getattr(self.ship, 'sensor_manager', None)
        current_target = sensor_manager.get_current_target() if sensor_manager else None
        threat_level = sensor_manager.get_threat_level() if sensor_manager else 0.0
        
        # State transition logic based on current state
        if self.current_state == AI_STATE_DISABLED:
            # Can only leave disabled state if health improves
            if not calculate_disable_state(self.ship.shields, self.ship.health, self.ship.max_health):
                return AI_STATE_IDLE
        
        elif self.current_state == AI_STATE_IDLE:
            # Look for targets or start patrolling
            if current_target:
                return AI_STATE_ATTACKING
            elif random.random() < 0.1:  # 10% chance to start patrolling
                return AI_STATE_PATROLLING
        
        elif self.current_state == AI_STATE_PATROLLING:
            # Switch to combat if target found
            if current_target:
                return AI_STATE_ATTACKING
            # Continue patrolling for a while
            elif self.state_timer > 600:  # 10 seconds
                return AI_STATE_IDLE
        
        elif self.current_state == AI_STATE_ATTACKING:
            # Check if we should flee
            if self._should_flee():
                return AI_STATE_FLEEING
            # Check if target is lost
            elif not current_target:
                return AI_STATE_PATROLLING
        
        elif self.current_state == AI_STATE_FLEEING:
            # Check if we can stop fleeing
            if not self._should_flee() and threat_level < 0.3:
                return AI_STATE_IDLE
            # Continue fleeing if still threatened
            elif threat_level > 0.5:
                return AI_STATE_FLEEING
        
        elif self.current_state == AI_STATE_TRADING:
            # Trading logic would go here
            # For now, just return to idle after a while
            if self.state_timer > 1200:  # 20 seconds
                return AI_STATE_IDLE
        
        return None  # Stay in current state
    
    def _should_flee(self):
        """
        Determine if the ship should flee based on health and personality.
        
        Returns:
            bool: True if ship should flee
        """
        personality = getattr(self.ship, 'ai_personality', 'balanced')
        ship_size = getattr(self.ship, 'ship_size', 'medium')
        
        return should_flee(
            self.ship.health, 
            self.ship.max_health, 
            personality, 
            ship_size
        )
    
    def _transition_to_state(self, new_state, game_time):
        """
        Transition to a new state with proper logging and cleanup.
        
        Args:
            new_state: The state to transition to
            game_time: Current game time
        """
        if new_state not in ALL_AI_STATES:
            print(f"Warning: Invalid AI state '{new_state}' for {self.ship.ship_type}")
            return
        
        # Record transition
        self.previous_state = self.current_state
        self.current_state = new_state
        self.state_timer = 0
        self.last_transition_time = game_time
        
        # Add to history for debugging
        self.transition_history.append({
            'from': self.previous_state,
            'to': new_state,
            'time': game_time,
            'health_ratio': self.ship.health / self.ship.max_health if self.ship.max_health > 0 else 0
        })
        
        # Keep only last 10 transitions
        if len(self.transition_history) > 10:
            self.transition_history.pop(0)
        
        # State-specific initialization
        self._initialize_state(new_state)
        
        # Debug logging
        if hasattr(self.ship, 'ship_type') and hasattr(self.ship, 'faction_id'):
            print(f"AI State: {self.ship.ship_type} ({self.ship.faction_id}) {self.previous_state} -> {new_state}")
    
    def _force_transition(self, new_state, game_time):
        """
        Force an immediate transition (bypasses cooldown).
        
        Args:
            new_state: The state to transition to
            game_time: Current game time
        """
        self._transition_to_state(new_state, game_time)
    
    def _initialize_state(self, state):
        """
        Initialize state-specific data and behavior.
        
        Args:
            state: The state being entered
        """
        # Clear previous state data
        self.state_data.clear()
        
        if state == AI_STATE_PATROLLING:
            # Set up patrol parameters
            self.state_data['patrol_center'] = self.ship.pos.copy()
            self.state_data['patrol_target'] = None
            
        elif state == AI_STATE_ATTACKING:
            # Set up combat parameters
            self.state_data['last_shot_time'] = 0
            self.state_data['combat_maneuver'] = 'approach'
            
        elif state == AI_STATE_FLEEING:
            # Set up flee parameters
            self.state_data['flee_direction'] = None
            self.state_data['flee_target'] = None
            
        elif state == AI_STATE_DISABLED:
            # Set up disabled parameters
            self.state_data['distress_timer'] = 0
            self.state_data['repair_attempts'] = 0
    
    def get_current_state(self):
        """Get the current AI state."""
        return self.current_state
    
    def get_state_timer(self):
        """Get how long we've been in the current state."""
        return self.state_timer
    
    def get_state_data(self, key, default=None):
        """Get state-specific data."""
        return self.state_data.get(key, default)
    
    def set_state_data(self, key, value):
        """Set state-specific data."""
        self.state_data[key] = value
    
    def get_transition_history(self):
        """Get recent state transition history for debugging."""
        return self.transition_history.copy()
    
    def can_interrupt_state(self):
        """Check if current state can be interrupted by external events."""
        # Some states should not be interrupted
        uninterruptible_states = [AI_STATE_DISABLED]
        return self.current_state not in uninterruptible_states
    
    def force_state(self, new_state):
        """Force a state change (for external control)."""
        if new_state in ALL_AI_STATES:
            self._force_transition(new_state, 0)  # Use 0 for external transitions
        else:
            print(f"Warning: Attempted to force invalid state '{new_state}'")
