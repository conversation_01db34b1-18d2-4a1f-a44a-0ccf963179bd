"""
Test script to verify Phase 1 AI fixes are working
Tests imports, damage system, and disable logic
"""

def test_imports():
    """Test that all new imports work correctly"""
    try:
        from game_objects.ai.core.ai_constants import (
            AI_STATE_IDLE, DISABLE_THRESHOLD, FLEE_THRESHOLDS
        )
        from game_objects.ai.core.ai_utils import (
            get_weapon_damage, calculate_disable_state, should_flee
        )
        print("✅ All imports working correctly")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_damage_system():
    """Test the unified damage system"""
    try:
        from game_objects.ai.core.ai_utils import get_weapon_damage
        
        # Mock weapon with new damage system
        class MockWeaponNew:
            shield_damage = 15
            armor_damage = 10
        
        # Mock weapon with old damage system  
        class MockWeaponOld:
            damage = 20
        
        # Mock weapon with no damage
        class MockWeaponNone:
            pass
        
        # Test new system
        shield, armor = get_weapon_damage(MockWeaponNew())
        assert shield == 15 and armor == 10, f"New system failed: {shield}, {armor}"
        
        # Test old system fallback
        shield, armor = get_weapon_damage(MockWeaponOld())
        assert shield == 20 and armor == 20, f"Old system failed: {shield}, {armor}"
        
        # Test no damage fallback
        shield, armor = get_weapon_damage(MockWeaponNone())
        assert shield == 10 and armor == 10, f"No damage failed: {shield}, {armor}"
        
        print("✅ Damage system working correctly")
        return True
    except Exception as e:
        print(f"❌ Damage system error: {e}")
        return False

def test_disable_logic():
    """Test the consistent disable logic"""
    try:
        from game_objects.ai.core.ai_utils import calculate_disable_state
        
        # Test cases: shields, armor, max_armor, expected_result
        test_cases = [
            (100, 100, 100, False),  # Full health
            (0, 20, 100, False),     # Shields down, armor above threshold
            (0, 15, 100, True),      # Shields down, armor at threshold
            (0, 10, 100, True),      # Shields down, armor below threshold
            (50, 10, 100, False),    # Shields up, armor low (not disabled)
        ]
        
        for shields, armor, max_armor, expected in test_cases:
            result = calculate_disable_state(shields, armor, max_armor)
            assert result == expected, f"Disable logic failed for ({shields}, {armor}, {max_armor}): got {result}, expected {expected}"
        
        print("✅ Disable logic working correctly")
        return True
    except Exception as e:
        print(f"❌ Disable logic error: {e}")
        return False

def test_flee_logic():
    """Test the flee logic"""
    try:
        from game_objects.ai.core.ai_utils import should_flee
        from game_objects.ai.core.ai_constants import AI_PERSONALITY_AGGRESSIVE, AI_PERSONALITY_COWARD
        
        # Aggressive personality should flee less
        aggressive_result = should_flee(20, 100, AI_PERSONALITY_AGGRESSIVE, "medium")
        
        # Coward personality should flee more
        coward_result = should_flee(80, 100, AI_PERSONALITY_COWARD, "medium")
        
        print(f"✅ Flee logic working: aggressive={aggressive_result}, coward={coward_result}")
        return True
    except Exception as e:
        print(f"❌ Flee logic error: {e}")
        return False

def main():
    """Run all tests"""
    print("=== Phase 1 AI Fixes Test ===\n")
    
    tests = [
        test_imports,
        test_damage_system, 
        test_disable_logic,
        test_flee_logic
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"=== Results: {passed}/{len(tests)} tests passed ===")
    
    if passed == len(tests):
        print("🎉 Phase 1 fixes successful! Ready for Phase 2.")
    else:
        print("⚠️  Some tests failed. Check the issues above.")

if __name__ == "__main__":
    main()
