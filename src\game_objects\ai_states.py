"""
AI States Module - Individual state classes for AI behavior
This module contains the state machine logic for AI ships.
"""
import pygame as pg
import random
import math
from game_objects.ai_core import *
from game_objects.ai.core.ai_utils import calculate_disable_state, should_flee
from game_objects.ai.core.ai_constants import DISABLE_THRESHOLD

class AIStateManager:
    """
    Manages AI state transitions and behavior execution.
    """

    def __init__(self, ai_ship):
        self.ship = ai_ship
        self.states = {
            AI_STATE_IDLE: IdleState(ai_ship),
            AI_STATE_PATROLLING: PatrollingState(ai_ship),
            AI_STATE_ATTACKING: AttackingState(ai_ship),
            AI_STATE_FLEEING: FleeingState(ai_ship),
            AI_STATE_DISABLED: DisabledState(ai_ship),
            AI_STATE_TRADING: TradingState(ai_ship)
        }

    def update(self, dt):
        """Update the current state."""
        current_state = self.states.get(self.ship.ai_state)
        if current_state:
            current_state.update(dt)

        # Check for state transitions
        self._check_state_transitions()

    def _check_state_transitions(self):
        """Check if the AI should transition to a different state."""
        # Health-based transitions
        if self.ship.health <= 0:
            return  # Ship is dead

        # Check for disable state using consistent logic
        if calculate_disable_state(self.ship.shields, self.ship.health, self.ship.max_health):
            if self.ship.ai_state != AI_STATE_DISABLED:
                self.ship.ai_state = AI_STATE_DISABLED
                print(f"DEBUG: {self.ship.ship_type} ({self.ship.faction_id}) disabled (shields: {self.ship.shields:.1f}, armor: {self.ship.health:.1f}/{self.ship.max_health}).")
                self.ship.game.set_status_message(f"{self.ship.ship_type} disabled! Press B to board.", (255, 255, 0))
                return

        # Target-based transitions
        if self.ship.ai_state not in [AI_STATE_DISABLED, AI_STATE_FLEEING]:
            # Use the sensor manager for target scanning
            pass  # Sensor manager handles this

class AIState:
    """Base class for AI states."""

    def __init__(self, ai_ship):
        self.ship = ai_ship

    def update(self, dt):
        """Update this state. Override in subclasses."""
        pass

class IdleState(AIState):
    """AI ship is idle and not doing much."""

    def update(self, dt):
        self.ship.state_timer -= 1
        if self.ship.state_timer <= 0:
            self.ship.ai_state = AI_STATE_PATROLLING
            self.ship.state_timer = random.randint(120, 300)

class PatrollingState(AIState):
    """AI ship is patrolling around the system."""

    def update(self, dt):
        # Check if we need a new patrol target
        if (self.ship.state_timer <= 0 or
            self.ship.patrol_target_pos is None or
            self.ship.pos.distance_to(self.ship.patrol_target_pos) < 50):

            self.ship.patrol_target_pos = pg.math.Vector2(
                random.randrange(100, self.ship.game.camera.width - 100),
                random.randrange(100, self.ship.game.camera.height - 100)
            )
            self.ship.state_timer = random.randint(300, 600)

        # Move toward patrol target
        if self.ship.patrol_target_pos:
            target_angle = self.ship.get_angle_to_point(self.ship.patrol_target_pos)
            self.ship.turn_toward_angle(target_angle)

            # Apply thrust if roughly facing the target
            angle_diff = abs((self.ship.angle - target_angle) % 360)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            if angle_diff < 45:
                thrust_multiplier = max(0.3, 1.0 - angle_diff / 45.0)
                self.ship.apply_thrust_in_facing_direction(thrust_multiplier)

class AttackingState(AIState):
    """AI ship is engaged in combat."""

    def __init__(self, ai_ship):
        super().__init__(ai_ship)
        self.combat_manager = None

    def update(self, dt):
        # Import here to avoid circular imports
        if not self.combat_manager:
            from game_objects.ai_combat import AICombatManager
            self.combat_manager = AICombatManager(self.ship)

        # Check if target is still valid
        if not self.ship.target_entity or not self.ship.target_entity.alive() or self.ship.health <= 0:
            self.ship.ai_state = AI_STATE_PATROLLING
            self.ship.target_entity = None
            return

        direction_to_target = (self.ship.target_entity.pos - self.ship.pos)
        distance = direction_to_target.length()

        # If target is too far away, give up
        if distance > AI_SENSOR_RANGE * 1.5:
            self.ship.ai_state = AI_STATE_PATROLLING
            self.ship.target_entity = None
            return

        # Get optimal engagement range based on weapons
        optimal_range = self.combat_manager.get_optimal_engagement_range()

        # Weapon selection and switching
        if self.combat_manager.should_switch_weapons(self.ship.target_entity, distance):
            self.combat_manager.switch_to_best_weapon(self.ship.target_entity, distance)

        # Movement and positioning
        self._handle_combat_movement(distance, optimal_range)

        # Weapon firing
        if distance < self.ship.weapon_range:
            self._handle_weapon_firing(dt, distance)

    def _handle_combat_movement(self, distance, optimal_range):
        """Handle movement during combat based on weapon types and distance."""
        target_angle = self.ship.get_angle_to_point(self.ship.target_entity.pos)

        # Different tactics based on weapon loadout
        if self.ship.has_fixed_weapons and not self.ship.has_turret_weapons:
            # Ships with only fixed weapons need to point at target
            self.ship.turn_toward_angle(target_angle)

            # Move to optimal range
            if distance > optimal_range + 20:
                # Too far - close in
                angle_diff = abs((self.ship.angle - target_angle) % 360)
                if angle_diff > 180:
                    angle_diff = 360 - angle_diff

                if angle_diff < 45:
                    thrust_multiplier = max(0.5, 1.0 - angle_diff / 45.0)
                    self.ship.apply_thrust_in_facing_direction(thrust_multiplier)
            elif distance < optimal_range - 20:
                # Too close - back away
                self.ship.apply_thrust_in_facing_direction(-0.3)

        elif self.ship.has_turret_weapons:
            # Ships with turrets can use more flexible tactics
            if distance > optimal_range + 30:
                # Close in
                self.ship.turn_toward_angle(target_angle)
                self.ship.apply_thrust_in_facing_direction(0.8)
            elif distance < optimal_range - 30:
                # Maintain distance
                self.ship.apply_thrust_in_facing_direction(-0.2)
            else:
                # At good range - use evasive maneuvers
                if random.random() < 0.1:  # 10% chance to change direction
                    evasion_angle = target_angle + random.uniform(-90, 90)
                    self.ship.turn_toward_angle(evasion_angle)
                    self.ship.apply_thrust_in_facing_direction(0.5)

        else:
            # Default behavior
            self.ship.turn_toward_angle(target_angle)
            if distance > optimal_range:
                self.ship.apply_thrust_in_facing_direction(0.7)
            elif distance < optimal_range * 0.7:
                self.ship.apply_thrust_in_facing_direction(-0.3)

    def _handle_weapon_firing(self, dt, distance):
        """Handle weapon firing logic."""
        current_weapon = self.ship.weapons[self.ship.active_weapon_index] if self.ship.weapons else None
        if not current_weapon:
            return

        # Different firing logic based on weapon type
        if current_weapon.mount_type == MOUNT_TYPE_FIXED:
            # Fixed weapons need to be pointing at target
            target_angle = self.ship.get_angle_to_point(self.ship.target_entity.pos)
            angle_diff = abs((self.ship.angle - target_angle) % 360)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            # Fire if pointing roughly at target
            if angle_diff < 20 and random.random() < 0.3:
                self.combat_manager.fire_weapon(dt)

        elif current_weapon.mount_type == MOUNT_TYPE_TURRET:
            # Turrets can fire more freely
            if random.random() < 0.4:
                self.combat_manager.fire_weapon(dt)

        elif current_weapon.uses_ammo:
            # Missiles - use sparingly and at good range
            if distance > 100 and distance < current_weapon.range * 0.8:
                if random.random() < 0.15:  # Lower chance to conserve ammo
                    self.combat_manager.fire_weapon(dt)

        else:
            # Default firing
            if random.random() < 0.25:
                self.combat_manager.fire_weapon(dt)

class FleeingState(AIState):
    """AI ship is fleeing from combat."""

    def update(self, dt):
        if not self.ship.target_entity or not self.ship.target_entity.alive():
            self.ship.ai_state = AI_STATE_PATROLLING
            return

        # Calculate flee direction (away from target)
        direction_from_target = (self.ship.pos - self.ship.target_entity.pos)
        if direction_from_target.length_squared() > 0:
            flee_angle = self.ship.get_angle_to_point(self.ship.pos + direction_from_target.normalize() * 100)

            self.ship.turn_toward_angle(flee_angle)

            angle_diff = abs((self.ship.angle - flee_angle) % 360)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            if angle_diff < 60:
                self.ship.apply_thrust_in_facing_direction(1.2)

        # Stop fleeing if far enough away
        if self.ship.pos.distance_to(self.ship.target_entity.pos) > AI_SENSOR_RANGE * 2:
            self.ship.ai_state = AI_STATE_PATROLLING
            self.ship.target_entity = None

class DisabledState(AIState):
    """AI ship is disabled and drifting."""

    def update(self, dt):
        # Gradually slow down
        if self.ship.vel.length_squared() > 0:
            self.ship.vel *= 0.95

        # Occasional sparks effect
        if random.random() < 0.05:
            print(f"Disabled ship {self.ship.ship_type} emitting sparks")

        # Check if player is nearby for boarding message
        if hasattr(self.ship.game, 'player') and self.ship.game.player:
            dist_to_player = self.ship.pos.distance_to(self.ship.game.player.pos)
            if dist_to_player < 100:
                if random.random() < 0.1:
                    self.ship.game.set_status_message(f"Press B to board disabled {self.ship.ship_type}", (255, 255, 0))

        # Slowly regenerate shields
        if self.ship.shields < self.ship.max_shields:
            self.ship.shields = min(self.ship.max_shields, self.ship.shields + 0.01)

class TradingState(AIState):
    """AI ship is engaged in trading activities."""

    def update(self, dt):
        # Basic trading behavior - could be expanded
        # For now, just patrol with different movement patterns
        if self.ship.state_timer <= 0:
            self.ship.ai_state = AI_STATE_PATROLLING
            self.ship.state_timer = random.randint(600, 1200)
