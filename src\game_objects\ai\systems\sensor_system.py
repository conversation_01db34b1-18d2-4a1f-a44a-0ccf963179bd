"""
AI Sensor System - Target detection and scanning for AI ships
Handles target acquisition, threat assessment, and sensor management.
Optimized to prevent scanning spam and improve performance.
"""
import pygame as pg
import random
from ..core.ai_constants import (
    AI_SENSOR_RANGE, 
    TARGET_SCAN_INTERVAL,
    MAX_SHIPS_SCAN_PER_FRAME,
    AI_STATE_ATTACKING,
    AI_STATE_FLEEING
)
from ..core.ai_utils import (
    distance_squared, 
    is_hostile, 
    stagger_update_timer
)

class AISensorManager:
    """
    Manages AI sensor operations including target detection and threat assessment.
    Implements performance optimizations to prevent scanning spam.
    """
    
    def __init__(self, ai_ship):
        self.ship = ai_ship
        self.last_scan_time = 0
        self.scan_interval = stagger_update_timer(id(ai_ship), TARGET_SCAN_INTERVAL)
        self.current_target = None
        self.known_hostiles = []  # Cache of known hostile entities
        self.known_allies = []    # Cache of known allied entities
        self.threat_level = 0.0   # Current threat assessment (0.0 to 1.0)
        
        # Performance tracking
        self.scans_this_frame = 0
        
    def update(self, dt, game_time):
        """
        Update sensor system. Only performs scanning at staggered intervals.
        
        Args:
            dt: Delta time
            game_time: Current game time in frames
        """
        # Only scan at our designated interval
        if game_time - self.last_scan_time >= self.scan_interval:
            self.perform_scan()
            self.last_scan_time = game_time
            
        # Update threat assessment
        self.update_threat_assessment()
        
        # Validate current target
        self.validate_current_target()
    
    def perform_scan(self):
        """
        Perform a sensor scan to detect targets and threats.
        Uses priority-based target selection for better AI behavior.
        """
        if not hasattr(self.ship, 'game') or not self.ship.game:
            return
            
        # Clear old data
        self.known_hostiles.clear()
        self.known_allies.clear()
        
        # Scan for player
        if hasattr(self.ship.game, 'player') and self.ship.game.player:
            self._scan_entity(self.ship.game.player, "player")
        
        # Scan for other AI ships
        if hasattr(self.ship.game, 'ai_ships'):
            for other_ship in self.ship.game.ai_ships:
                if other_ship != self.ship and other_ship.alive():
                    self._scan_entity(other_ship, "ai_ship")
        
        # Select best target based on priority
        self.select_priority_target()
    
    def _scan_entity(self, entity, entity_type):
        """
        Scan a specific entity and categorize it.
        
        Args:
            entity: The entity to scan
            entity_type: Type of entity ("player" or "ai_ship")
        """
        if not entity or not hasattr(entity, 'pos'):
            return
            
        # Check if entity is in sensor range
        distance_sq = distance_squared(self.ship.pos, entity.pos)
        if distance_sq > AI_SENSOR_RANGE * AI_SENSOR_RANGE:
            return
            
        # Determine relationship
        if entity_type == "player":
            entity_faction = "player"
        else:
            entity_faction = getattr(entity, 'faction_id', 'unknown')
        
        # Check faction relations
        if hasattr(self.ship.game, 'faction_relations'):
            is_enemy = is_hostile(
                self.ship.game.faction_relations, 
                self.ship.faction_id, 
                entity_faction
            )
        else:
            # Fallback: assume player is hostile, others neutral
            is_enemy = (entity_faction == "player")
        
        # Categorize entity
        entity_data = {
            'entity': entity,
            'distance_sq': distance_sq,
            'faction': entity_faction,
            'threat_level': self._assess_entity_threat(entity, distance_sq)
        }
        
        if is_enemy:
            self.known_hostiles.append(entity_data)
        else:
            self.known_allies.append(entity_data)
    
    def _assess_entity_threat(self, entity, distance_sq):
        """
        Assess the threat level of an entity.
        
        Args:
            entity: The entity to assess
            distance_sq: Squared distance to entity
            
        Returns:
            float: Threat level (0.0 to 1.0)
        """
        threat = 0.0
        
        # Base threat from proximity (closer = more threatening)
        max_range_sq = AI_SENSOR_RANGE * AI_SENSOR_RANGE
        proximity_threat = 1.0 - (distance_sq / max_range_sq)
        threat += proximity_threat * 0.3
        
        # Threat from entity size/power
        if hasattr(entity, 'max_health'):
            # Larger ships are more threatening
            health_threat = min(entity.max_health / 1000.0, 1.0)
            threat += health_threat * 0.2
        
        # Threat from weapons
        if hasattr(entity, 'weapons') and entity.weapons:
            weapon_threat = min(len(entity.weapons) / 5.0, 1.0)
            threat += weapon_threat * 0.3
        
        # Threat from current behavior
        if hasattr(entity, 'ai_state'):
            if entity.ai_state == AI_STATE_ATTACKING:
                threat += 0.2
        
        return min(threat, 1.0)
    
    def select_priority_target(self):
        """
        Select the highest priority target based on threat assessment.
        Priority order:
        1. Enemies attacking me
        2. Enemies attacking allies  
        3. Closest enemies
        4. Weakest enemies
        """
        if not self.known_hostiles:
            self.current_target = None
            return
        
        best_target = None
        best_priority = -1
        
        for hostile_data in self.known_hostiles:
            entity = hostile_data['entity']
            priority = 0
            
            # Priority 1: Enemies attacking me
            if (hasattr(entity, 'target_entity') and 
                entity.target_entity == self.ship):
                priority = 1000
            
            # Priority 2: Enemies attacking allies
            elif (hasattr(entity, 'target_entity') and 
                  self._is_ally(entity.target_entity)):
                priority = 800
            
            # Priority 3: Closest enemies
            else:
                # Base priority on inverse distance
                distance = hostile_data['distance_sq'] ** 0.5
                priority = max(0, 600 - distance)
            
            # Bonus for high threat entities
            priority += hostile_data['threat_level'] * 100
            
            # Bonus for damaged enemies (easier targets)
            if hasattr(entity, 'health') and hasattr(entity, 'max_health'):
                if entity.max_health > 0:
                    damage_ratio = 1.0 - (entity.health / entity.max_health)
                    priority += damage_ratio * 50
            
            if priority > best_priority:
                best_priority = priority
                best_target = entity
        
        self.current_target = best_target
    
    def _is_ally(self, entity):
        """Check if an entity is an ally."""
        if not entity or not hasattr(entity, 'faction_id'):
            return False
            
        for ally_data in self.known_allies:
            if ally_data['entity'] == entity:
                return True
        return False
    
    def update_threat_assessment(self):
        """Update overall threat level assessment."""
        if not self.known_hostiles:
            self.threat_level = 0.0
            return
        
        # Calculate threat based on number and strength of hostiles
        total_threat = 0.0
        for hostile_data in self.known_hostiles:
            total_threat += hostile_data['threat_level']
        
        # Normalize threat level
        self.threat_level = min(total_threat / 3.0, 1.0)  # Cap at 1.0
    
    def validate_current_target(self):
        """Ensure current target is still valid."""
        if not self.current_target:
            return
            
        # Check if target is still alive
        if not self.current_target.alive():
            self.current_target = None
            return
        
        # Check if target is still in range
        if hasattr(self.current_target, 'pos'):
            distance_sq = distance_squared(self.ship.pos, self.current_target.pos)
            if distance_sq > AI_SENSOR_RANGE * AI_SENSOR_RANGE * 1.5:  # 50% buffer
                self.current_target = None
                return
    
    def get_current_target(self):
        """Get the current priority target."""
        return self.current_target
    
    def get_threat_level(self):
        """Get the current threat level (0.0 to 1.0)."""
        return self.threat_level
    
    def get_hostile_count(self):
        """Get the number of known hostiles."""
        return len(self.known_hostiles)
    
    def get_ally_count(self):
        """Get the number of known allies."""
        return len(self.known_allies)
    
    def force_scan(self):
        """Force an immediate sensor scan (use sparingly)."""
        self.perform_scan()
