"""
Sound Manager for Escape Velocity Py
Handles loading and playing sound effects for weapons and other game events.
"""

import pygame as pg
import os
from pathlib import Path

class SoundManager:
    """
    Manages sound effects for the game.
    Handles loading, caching, and playing of sound files.
    """

    def __init__(self, game):
        """
        Initialize the sound manager.

        Args:
            game: The main game instance
        """
        self.game = game
        self.sounds_cache = {}  # Cache loaded sounds
        self.sound_enabled = True
        self.master_volume = 0.7
        self.sfx_volume = 0.8

        # Initialize pygame mixer if not already done
        if not pg.mixer.get_init():
            pg.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)

        # Set up sound directories - use src/assets for proper game structure
        self.sound_root = Path("src/assets/soundfx")
        self.weapon_sounds_dir = self.sound_root / "weapons"
        self.engine_sounds_dir = self.sound_root / "engines"
        self.ambient_sounds_dir = self.sound_root / "ambient"

        # Create directories if they don't exist
        self._ensure_sound_directories()

        print(f"SoundManager initialized. Sound root: {self.sound_root}")

    def _ensure_sound_directories(self):
        """Create sound directories if they don't exist."""
        for directory in [self.weapon_sounds_dir, self.engine_sounds_dir, self.ambient_sounds_dir]:
            directory.mkdir(parents=True, exist_ok=True)

    def load_sound(self, sound_file, category="weapons"):
        """
        Load a sound file and cache it.

        Args:
            sound_file: Name of the sound file (with or without extension)
            category: Category of sound (weapons, engines, ambient)

        Returns:
            pygame.mixer.Sound object or None if failed to load
        """
        if not self.sound_enabled:
            return None

        # Create cache key
        cache_key = f"{category}_{sound_file}"

        # Return cached sound if available
        if cache_key in self.sounds_cache:
            return self.sounds_cache[cache_key]

        # Determine sound directory based on category
        if category == "weapons":
            sound_dir = self.weapon_sounds_dir
        elif category == "engines":
            sound_dir = self.engine_sounds_dir
        elif category == "ambient":
            sound_dir = self.ambient_sounds_dir
        else:
            sound_dir = self.sound_root

        # Try to find the sound file
        sound_path = None

        # If sound_file already has an extension, use it directly
        if '.' in sound_file:
            potential_path = sound_dir / sound_file
            if potential_path.exists():
                sound_path = potential_path
        else:
            # Try common audio extensions
            for ext in ['.mp3', '.wav', '.ogg']:
                potential_path = sound_dir / f"{sound_file}{ext}"
                if potential_path.exists():
                    sound_path = potential_path
                    break

        if not sound_path:
            print(f"Sound file not found: {sound_file} in {sound_dir}")
            return None

        try:
            # Load the sound
            sound = pg.mixer.Sound(str(sound_path))

            # Cache the sound
            self.sounds_cache[cache_key] = sound

            print(f"Loaded sound: {sound_path}")
            return sound

        except Exception as e:
            print(f"Failed to load sound {sound_path}: {e}")
            return None

    def play_sound(self, sound_file, category="weapons", volume=None):
        """
        Play a sound effect.

        Args:
            sound_file: Name of the sound file
            category: Category of sound (weapons, engines, ambient)
            volume: Volume override (0.0 to 1.0), uses default if None
        """
        if not self.sound_enabled:
            return

        sound = self.load_sound(sound_file, category)
        if sound:
            # Calculate final volume
            final_volume = volume if volume is not None else self.sfx_volume
            final_volume *= self.master_volume

            # Set volume and play
            sound.set_volume(final_volume)
            sound.play()

    def play_weapon_sound(self, weapon_sound_file, volume=None):
        """
        Play a weapon firing sound.

        Args:
            weapon_sound_file: Name of the weapon sound file
            volume: Volume override (0.0 to 1.0)
        """
        self.play_sound(weapon_sound_file, "weapons", volume)

    def set_master_volume(self, volume):
        """
        Set the master volume.

        Args:
            volume: Volume level (0.0 to 1.0)
        """
        self.master_volume = max(0.0, min(1.0, volume))

    def set_sfx_volume(self, volume):
        """
        Set the sound effects volume.

        Args:
            volume: Volume level (0.0 to 1.0)
        """
        self.sfx_volume = max(0.0, min(1.0, volume))

    def toggle_sound(self):
        """Toggle sound on/off."""
        self.sound_enabled = not self.sound_enabled
        print(f"Sound {'enabled' if self.sound_enabled else 'disabled'}")

    def clear_cache(self):
        """Clear the sound cache to free memory."""
        self.sounds_cache.clear()
        print("Sound cache cleared")

    def get_available_sounds(self, category="weapons"):
        """
        Get list of available sound files in a category.

        Args:
            category: Category to search (weapons, engines, ambient)

        Returns:
            List of sound file names (without extensions)
        """
        if category == "weapons":
            sound_dir = self.weapon_sounds_dir
        elif category == "engines":
            sound_dir = self.engine_sounds_dir
        elif category == "ambient":
            sound_dir = self.ambient_sounds_dir
        else:
            sound_dir = self.sound_root

        if not sound_dir.exists():
            return []

        sounds = []
        for file_path in sound_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in ['.mp3', '.wav', '.ogg']:
                sounds.append(file_path.stem)  # Filename without extension

        return sorted(sounds)
