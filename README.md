# Escape Velocity Py 🚀

A modern Python recreation of the classic Escape Velocity space trading and combat game series. Built with pygame, this project faithfully captures the open-world space exploration, trading, combat, and ship customization that made the original series so beloved.

## 🎮 Game Overview

**Escape Velocity Py** is a 2D top-down space simulation where you:
- Pilot various spacecraft from nimble scouts to massive capital ships
- Trade commodities between star systems for profit
- Engage in real-time space combat with weapons and shields
- Customize your ship with outfits (weapons, engines, shields, utilities)
- Explore a procedurally generated galaxy with multiple factions
- Complete missions to earn credits and reputation
- Board and plunder disabled enemy ships

## 🏗️ Architecture & Modularity

This codebase is **extremely well-organized and AI-friendly**! The project uses a clean modular architecture that makes it easy to understand, modify, and extend:

### ✅ **Highly Modular Design**
- **Separate systems**: Combat, trading, AI, missions all in distinct modules
- **Clean interfaces**: Each system has well-defined APIs and responsibilities
- **Easy to modify**: Add new ships, weapons, or systems without breaking existing code
- **AI-accessible**: Clear structure makes it straightforward for AI assistants to help

### 📁 **Directory Structure**
```
EscapeVelocityPy/
├── 🎮 src/main.py                    # Main game executable
├── 🧠 src/game_objects/              # Core game systems (modular!)
│   ├── ships.py                      # Ship definitions and behaviors
│   ├── outfits.py                    # Weapons, shields, engines, utilities
│   ├── ai_ship.py                    # AI ship behaviors and combat
│   ├── player.py                     # Player ship controls and stats
│   ├── planet.py                     # Planet landing and services
│   ├── trading.py                    # Commodity trading system
│   ├── mission_system.py             # Dynamic mission generation
│   ├── outfitter.py                  # Ship customization interface
│   ├── shipyard.py                   # Ship purchasing system
│   └── faction.py                    # Faction relations and diplomacy
├── 🎨 assets/                        # Game assets (sprites, sounds)
│   ├── images/sprites/               # Ship and planet sprites
│   ├── soundfx/                      # Sound effects
│   └── README.md files               # Asset documentation
├── 🛠️ editor_modules/                # Modular content editing tools
│   ├── main_window.py                # Main editor interface
│   ├── data_manager.py               # Data loading/saving
│   ├── ship_editor.py                # Ship editing interface
│   └── outfit_editors/               # Specialized outfit editors
├── 📊 ships_data.json                # Ship definitions (easily editable)
├── ⚙️ outfits_data.json              # Outfit definitions (easily editable)
└── 🔧 [Development Tools]/           # See "Development Tools" section
```

## 🎯 Key Game Systems

### 🚢 **Ship System**
- **Classes**: Fighter, Freighter, Corvette, Frigate, Cruiser, Battleship, Carrier
- **Sizes**: Small → Medium → Large → Capital (affects cost, capabilities, target priority)
- **Customization**: Outfit space allows mixing weapons, defenses, engines, utilities
- **Physics**: Newtonian movement with momentum, thrust, and turning

### ⚔️ **Combat System**
- **Real-time**: Fast-paced action with projectiles, shields, and armor
- **Weapon Types**: Energy (lasers), Kinetic (mass drivers), Explosive (missiles)
- **AI Behaviors**: Patrol, attack, flee, escort with faction relations
- **Targeting**: Cycle through enemies, friendlies, planets with smart targeting

### 💰 **Economic System**
- **Trading**: Buy low, sell high across different tech level planets
- **Missions**: Cargo delivery, passenger transport, combat missions
- **Resources**: Credits, fuel, power management
- **Progression**: Earn money to buy better ships and outfits

### 🌌 **Galaxy System**
- **Procedural**: 20-50 star systems with random connections
- **Factions**: Federation, Corporation, Pirates, Independent with dynamic relations
- **Exploration**: Discover new systems, each with unique planets and opportunities

## 🛠️ Development Tools (Root Directory Files)

The project includes numerous development and debugging tools:

### **Asset Creation Tools**
- `generate_sprite_sheet_improved.py` - **Advanced sprite sheet generator** with rotation frames, multiple layouts, anti-aliasing
- `fix_ship_sprites.py` - Utility to fix sprite loading issues
- `diagnose_sprites.py` - Debug sprite asset problems

### **Content Editors**
- `simple_editor.py` - **Basic outfit editor** for weapons and defenses
- `enhanced_editor_refactored.py` - **Advanced modular editor** (requires editor_modules/)

### **Data Management**
- `create_ships_json.py` - Generate ships_data.json from code definitions
- `sync_outfits.py` - Synchronize outfit data between systems
- `SYNC_FIX_SUMMARY.md` - Documentation of data sync fixes

### **Debug & Testing**
- `test_game_launch.py` - Test if main game launches without errors
- `debug_outfit_loading.py` - Debug outfit data loading issues
- `debug_sprite_loading.py` - Debug sprite asset loading
- `debug_paths.py` - Debug file path resolution
- `test_outfit_loading.py` - Test outfit system functionality
- `test_sync.py` - Test data synchronization

### **Integration Testing**
- `demo_complete_integration.py` - Full system integration test
- `ai_ship_updated.py` - AI ship behavior testing
- `planet_fixed.py` - Planet system testing

## 🚀 Quick Start

### **Running the Game**
```bash
cd EscapeVelocityPy
python src/main.py
```

### **Game Controls**
- **WASD**: Thrust and turn
- **Space**: Fire weapons
- **Tab**: Cycle targets
- **L**: Land on targeted planet
- **M**: Open galaxy map
- **J**: Jump to selected system
- **P**: Ship status menu
- **B**: Board disabled ships

### **Development Setup**
```bash
# Install dependencies
pip install pygame

# Test if everything works
python test_game_launch.py

# Create custom content
python simple_editor.py
```

## 🔧 Easy Modification Guide

### **Adding New Ships**
1. Edit `ships_data.json` - add your ship with stats
2. Create sprite assets in `assets/images/sprites/ships/`
3. Run `python create_ships_json.py` to update the registry

### **Adding New Weapons**
1. Edit `outfits_data.json` - add weapon with damage, range, etc.
2. Use `simple_editor.py` for a GUI approach
3. Test with `python debug_outfit_loading.py`

### **Modifying AI Behavior**
- Edit `src/game_objects/ai_ship.py` - clean, commented AI state machine
- Adjust aggression, faction relations, combat tactics

### **Balancing Economy**
- Modify commodity prices in `src/game_objects/trading.py`
- Adjust mission payouts in `src/game_objects/mission_system.py`

## 🤖 AI Assistant Collaboration

This codebase is **exceptionally AI-friendly**:

### ✅ **What Makes It Easy for AI**
- **Clear module separation** - each file has a single responsibility
- **Consistent naming** - functions and variables are descriptive
- **Good documentation** - comments explain complex logic
- **JSON data files** - easy to read/modify ship and outfit data
- **Error handling** - graceful failure modes with helpful messages
- **Debug tools** - extensive testing and debugging utilities

### 📖 **AI Quick Reference**
- **Main game loop**: `src/main.py` (Game class)
- **Add content**: Edit JSON files or use `simple_editor.py`
- **Debug issues**: Use files in root directory (debug_*.py, test_*.py)
- **Modify gameplay**: `src/game_objects/` modules
- **Asset pipeline**: `generate_sprite_sheet_improved.py`

### 🎯 **Common AI Tasks**
1. **Balance changes**: Modify JSON data files
2. **New features**: Add to appropriate `src/game_objects/` module  
3. **Bug fixes**: Use debug tools to identify issues
4. **Asset creation**: Use sprite sheet generator
5. **Testing**: Run test files to verify changes

## 📝 Data File Formats

### **ships_data.json Structure**
```json
{
  "ship_id": {
    "name": "Ship Name",
    "ship_class": "fighter",
    "size": "small",
    "cost": 15000,
    "max_speed": 4.0,
    "shields": 50,
    "armor": 30
  }
}
```

### **outfits_data.json Structure**
```json
{
  "outfit_id": {
    "name": "Outfit Name", 
    "category": "weapons",
    "cost": 1500,
    "space_required": 2,
    "damage": 25,
    "fire_rate": 3.0
  }
}
```

## 🐛 Common Issues & Solutions

### **Game Won't Start**
```bash
python test_game_launch.py  # Diagnose startup issues
```

### **Sprites Not Loading**
```bash
python diagnose_sprites.py  # Check sprite assets
python fix_ship_sprites.py  # Fix sprite problems
```

### **Outfits Not Working**
```bash
python debug_outfit_loading.py  # Debug outfit system
python test_outfit_loading.py   # Test outfit functionality
```

## 🎯 Project Status

**Current State**: Fully playable game with all core features implemented
- ✅ Real-time space combat
- ✅ Trading and economy
- ✅ Ship customization
- ✅ Galaxy exploration  
- ✅ AI ship behaviors
- ✅ Mission system
- ✅ Faction relations

**Development Tools**: Comprehensive suite for content creation and debugging

**Code Quality**: Excellent - modular, documented, and maintainable

---

## 💡 For AI Assistants

This project is designed to be **extremely collaborative with AI**. The modular architecture, comprehensive debug tools, and clear data formats make it easy to:
- Quickly understand any part of the system
- Make targeted changes without breaking other systems
- Test modifications with existing debug tools
- Add new content through JSON files or editors

The codebase follows Python best practices and maintains a clean separation of concerns, making it ideal for AI-assisted development and maintenance.
