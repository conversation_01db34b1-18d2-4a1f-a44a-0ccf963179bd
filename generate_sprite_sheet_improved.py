import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from tkinterdnd2 import DND_FILES, TkinterDnD
from PIL import Image, ImageTk, ImageDraw, ImageFilter
import os
import json
import math

def generate_sprite_sheet(input_file, output_dir, ship_name, frame_count, sprite_size, sheet_layout, anti_aliasing, background_color, trim_transparent):
    with Image.open(input_file) as img:
        # Convert to RGBA if not already
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        
        # Resize with high-quality resampling
        original_img = img.resize((sprite_size, sprite_size), Image.Resampling.LANCZOS)
        
        # Optional: trim transparent edges
        if trim_transparent:
            bbox = original_img.getbbox()
            if bbox:
                original_img = original_img.crop(bbox)
                # Resize again to maintain sprite_size
                original_img = original_img.resize((sprite_size, sprite_size), Image.Resampling.LANCZOS)
        
        # Calculate sheet dimensions based on layout
        if sheet_layout == "horizontal":
            sheet_width = sprite_size * frame_count
            sheet_height = sprite_size
            cols, rows = frame_count, 1
        elif sheet_layout == "vertical":
            sheet_width = sprite_size
            sheet_height = sprite_size * frame_count
            cols, rows = 1, frame_count
        else:  # square
            cols = math.ceil(math.sqrt(frame_count))
            rows = math.ceil(frame_count / cols)
            sheet_width = sprite_size * cols
            sheet_height = sprite_size * rows
        
        # Create sprite sheet with specified background
        if background_color == "transparent":
            sprite_sheet = Image.new('RGBA', (sheet_width, sheet_height), (0, 0, 0, 0))
        else:
            sprite_sheet = Image.new('RGB', (sheet_width, sheet_height), background_color)
        
        # Generate rotation frames
        frames_data = []
        for i in range(frame_count):
            angle = 360 / frame_count * i
            
            if anti_aliasing:
                # High-quality rotation with supersampling
                temp_size = sprite_size * 2
                temp_img = original_img.resize((temp_size, temp_size), Image.Resampling.LANCZOS)
                rotated = temp_img.rotate(-angle, expand=False, resample=Image.Resampling.BICUBIC)
                rotated = rotated.resize((sprite_size, sprite_size), Image.Resampling.LANCZOS)
            else:
                rotated = original_img.rotate(-angle, expand=False, resample=Image.Resampling.BICUBIC)
            
            # Calculate position in sheet
            col = i % cols
            row = i // cols
            x = col * sprite_size
            y = row * sprite_size
            
            # Paste into sprite sheet
            if background_color == "transparent":
                sprite_sheet.paste(rotated, (x, y), rotated)
            else:
                sprite_sheet.paste(rotated, (x, y))
            
            # Store frame metadata
            frames_data.append({
                "frame": i,
                "angle": angle,
                "x": x,
                "y": y,
                "width": sprite_size,
                "height": sprite_size
            })
        
        # Save sprite sheet
        output_file = os.path.join(output_dir, f"{ship_name}_spritesheet.png")
        sprite_sheet.save(output_file, 'PNG')
        
        # Save metadata JSON
        metadata = {
            "ship_name": ship_name,
            "frame_count": frame_count,
            "sprite_size": sprite_size,
            "sheet_width": sheet_width,
            "sheet_height": sheet_height,
            "layout": sheet_layout,
            "frames": frames_data
        }
        
        metadata_file = os.path.join(output_dir, f"{ship_name}_metadata.json")
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        return output_file, metadata_file

class SpritesheetGeneratorApp:
    def __init__(self, master):
        self.master = master
        master.title("Advanced Spritesheet Generator")
        master.geometry("700x600")
        master.resizable(True, True)

        # Variables
        self.input_file = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.ship_name = tk.StringVar()
        self.frame_count = tk.IntVar(value=32)
        self.sprite_size = tk.IntVar(value=512)
        self.sheet_layout = tk.StringVar(value="horizontal")
        self.anti_aliasing = tk.BooleanVar(value=True)
        self.background_color = tk.StringVar(value="transparent")
        self.trim_transparent = tk.BooleanVar(value=False)
        
        self.preview_image = None
        self.create_widgets()
        self.output_dir.set(self.load_output_dir())

        master.drop_target_register(DND_FILES)
        master.dnd_bind('<<Drop>>', self.drop)

    def create_widgets(self):
        # Main frame with scrollbar
        main_frame = ttk.Frame(self.master)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Input section
        input_frame = ttk.LabelFrame(main_frame, text="Input Settings", padding=10)
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(input_frame, text="Input File:").grid(row=0, column=0, sticky="e", padx=5, pady=5)
        self.input_entry = ttk.Entry(input_frame, textvariable=self.input_file, width=50)
        self.input_entry.grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(input_frame, text="Browse", command=self.browse_input).grid(row=0, column=2, padx=5, pady=5)
        
        # Preview frame
        preview_frame = ttk.LabelFrame(main_frame, text="Preview", padding=10)
        preview_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.preview_label = ttk.Label(preview_frame, text="Drop or select an image to see preview")
        self.preview_label.pack()
        
        # Output section
        output_frame = ttk.LabelFrame(main_frame, text="Output Settings", padding=10)
        output_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(output_frame, text="Output Directory:").grid(row=0, column=0, sticky="e", padx=5, pady=5)
        ttk.Entry(output_frame, textvariable=self.output_dir, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(output_frame, text="Browse", command=self.browse_output).grid(row=0, column=2, padx=5, pady=5)
        
        ttk.Label(output_frame, text="Ship Name:").grid(row=1, column=0, sticky="e", padx=5, pady=5)
        ttk.Entry(output_frame, textvariable=self.ship_name, width=50).grid(row=1, column=1, columnspan=2, padx=5, pady=5)
        
        # Generation settings
        gen_frame = ttk.LabelFrame(main_frame, text="Generation Settings", padding=10)
        gen_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(gen_frame, text="Frame Count:").grid(row=0, column=0, sticky="e", padx=5, pady=5)
        frame_spinbox = ttk.Spinbox(gen_frame, from_=4, to=64, textvariable=self.frame_count, width=10)
        frame_spinbox.grid(row=0, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(gen_frame, text="Sprite Size:").grid(row=0, column=2, sticky="e", padx=5, pady=5)
        size_spinbox = ttk.Spinbox(gen_frame, from_=64, to=2048, increment=64, textvariable=self.sprite_size, width=10)
        size_spinbox.grid(row=0, column=3, sticky="w", padx=5, pady=5)
        
        ttk.Label(gen_frame, text="Sheet Layout:").grid(row=1, column=0, sticky="e", padx=5, pady=5)
        layout_combo = ttk.Combobox(gen_frame, textvariable=self.sheet_layout, 
                                   values=["horizontal", "vertical", "square"], state="readonly", width=12)
        layout_combo.grid(row=1, column=1, sticky="w", padx=5, pady=5)
        
        ttk.Label(gen_frame, text="Background:").grid(row=1, column=2, sticky="e", padx=5, pady=5)
        bg_combo = ttk.Combobox(gen_frame, textvariable=self.background_color,
                               values=["transparent", "white", "black", "gray"], state="readonly", width=12)
        bg_combo.grid(row=1, column=3, sticky="w", padx=5, pady=5)
        
        # Advanced options
        advanced_frame = ttk.LabelFrame(main_frame, text="Advanced Options", padding=10)
        advanced_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Checkbutton(advanced_frame, text="High-quality anti-aliasing", variable=self.anti_aliasing).grid(row=0, column=0, sticky="w", padx=5, pady=5)
        ttk.Checkbutton(advanced_frame, text="Trim transparent edges", variable=self.trim_transparent).grid(row=0, column=1, sticky="w", padx=5, pady=5)
        
        # Action buttons
        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(action_frame, text="Generate Spritesheet", command=self.generate).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="Preview Rotation", command=self.preview_rotation).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="How It Works", command=self.show_instructions).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="Presets", command=self.show_presets).pack(side=tk.LEFT, padx=5)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))

    def update_preview(self):
        if self.input_file.get() and os.path.exists(self.input_file.get()):
            try:
                with Image.open(self.input_file.get()) as img:
                    # Resize for preview
                    img.thumbnail((200, 200), Image.Resampling.LANCZOS)
                    self.preview_image = ImageTk.PhotoImage(img)
                    self.preview_label.configure(image=self.preview_image, text="")
            except Exception as e:
                self.preview_label.configure(image="", text=f"Error loading image: {str(e)}")

    def browse_input(self):
        filename = filedialog.askopenfilename(
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.bmp *.tiff *.gif")]
        )
        if filename:
            self.input_file.set(filename)
            self.update_preview()
            # Auto-generate ship name from filename
            if not self.ship_name.get():
                name = os.path.splitext(os.path.basename(filename))[0]
                self.ship_name.set(name)

    def browse_output(self):
        directory = filedialog.askdirectory()
        if directory:
            self.output_dir.set(directory)
            self.save_output_dir(directory)

    def preview_rotation(self):
        if not self.input_file.get():
            messagebox.showwarning("Warning", "Please select an input file first.")
            return
        
        # Create preview window
        preview_window = tk.Toplevel(self.master)
        preview_window.title("Rotation Preview")
        preview_window.geometry("400x450")
        
        try:
            with Image.open(self.input_file.get()) as img:
                if img.mode != 'RGBA':
                    img = img.convert('RGBA')
                
                img = img.resize((200, 200), Image.Resampling.LANCZOS)
                
                # Create angle slider
                ttk.Label(preview_window, text="Rotation Angle:").pack(pady=5)
                angle_var = tk.DoubleVar()
                angle_scale = ttk.Scale(preview_window, from_=0, to=360, orient=tk.HORIZONTAL, 
                                       variable=angle_var, length=350)
                angle_scale.pack(pady=5)
                
                # Preview label
                preview_label = ttk.Label(preview_window)
                preview_label.pack(pady=10)
                
                def update_rotation(val=None):
                    angle = angle_var.get()
                    rotated = img.rotate(-angle, expand=False, resample=Image.Resampling.BICUBIC)
                    photo = ImageTk.PhotoImage(rotated)
                    preview_label.configure(image=photo)
                    preview_label.image = photo  # Keep a reference
                
                angle_scale.configure(command=update_rotation)
                update_rotation()  # Initial update
                
        except Exception as e:
            messagebox.showerror("Error", f"Could not create preview: {str(e)}")
            preview_window.destroy()

    def generate(self):
        if not self.input_file.get() or not self.output_dir.get() or not self.ship_name.get():
            messagebox.showwarning("Warning", "Please fill in all required fields.")
            return
        
        self.status_var.set("Generating spritesheet...")
        self.master.update()
        
        try:
            # Convert background color
            bg_color = self.background_color.get()
            if bg_color == "white":
                bg_color = (255, 255, 255)
            elif bg_color == "black":
                bg_color = (0, 0, 0)
            elif bg_color == "gray":
                bg_color = (128, 128, 128)
            else:
                bg_color = "transparent"
            
            output_file, metadata_file = generate_sprite_sheet(
                self.input_file.get(),
                self.output_dir.get(),
                self.ship_name.get(),
                self.frame_count.get(),
                self.sprite_size.get(),
                self.sheet_layout.get(),
                self.anti_aliasing.get(),
                bg_color,
                self.trim_transparent.get()
            )
            
            self.status_var.set("Spritesheet generated successfully!")
            messagebox.showinfo("Success", 
                f"Files generated:\n- {output_file}\n- {metadata_file}")
            
        except Exception as e:
            self.status_var.set("Error occurred")
            messagebox.showerror("Error", str(e))

    def show_instructions(self):
        instructions = """
        Advanced Spritesheet Generator

        Features:
        • Multiple layout options (horizontal, vertical, square grid)
        • High-quality anti-aliasing for smooth rotations
        • Transparent or colored backgrounds
        • Edge trimming to remove unnecessary transparent space
        • JSON metadata export with frame coordinates
        • Live rotation preview

        Usage:
        1. Drop or select a top-down north-facing sprite image
        2. Configure generation settings (frames, size, layout)
        3. Choose advanced options if needed
        4. Click "Generate Spritesheet"

        Output:
        • PNG spritesheet with all rotation frames
        • JSON metadata file with frame positions and angles

        Game Integration:
        Use the metadata JSON to easily load and display the correct
        frame based on the object's rotation angle in your game engine.
        
        Tip: Use powers of 2 for sprite sizes (64, 128, 256, 512, etc.)
        for better GPU compatibility in game engines.
        """
        messagebox.showinfo("How It Works", instructions)

    def show_presets(self):
        presets_window = tk.Toplevel(self.master)
        presets_window.title("Presets")
        presets_window.geometry("400x300")
        
        ttk.Label(presets_window, text="Common Presets", font=("Arial", 12, "bold")).pack(pady=10)
        
        presets = [
            ("8-Direction (Classic)", 8, 256, "horizontal"),
            ("16-Direction (Smooth)", 16, 512, "horizontal"),
            ("32-Direction (Very Smooth)", 32, 512, "square"),
            ("64-Direction (Ultra Smooth)", 64, 512, "square"),
            ("Mobile Game (Small)", 16, 128, "square"),
            ("HD Game (Large)", 32, 1024, "square"),
        ]
        
        for name, frames, size, layout in presets:
            frame = ttk.Frame(presets_window)
            frame.pack(fill=tk.X, padx=20, pady=5)
            
            ttk.Label(frame, text=name, width=20).pack(side=tk.LEFT)
            ttk.Label(frame, text=f"{frames} frames, {size}px", width=15).pack(side=tk.LEFT)
            ttk.Button(frame, text="Apply", 
                      command=lambda f=frames, s=size, l=layout: self.apply_preset(f, s, l)).pack(side=tk.RIGHT)

    def apply_preset(self, frames, size, layout):
        self.frame_count.set(frames)
        self.sprite_size.set(size)
        self.sheet_layout.set(layout)

    def drop(self, event):
        file_path = event.data.strip('{}')
        self.input_file.set(file_path)
        self.update_preview()
        # Auto-generate ship name from filename
        if not self.ship_name.get():
            name = os.path.splitext(os.path.basename(file_path))[0]
            self.ship_name.set(name)

    def save_output_dir(self, directory):
        try:
            with open("output_dir.txt", "w") as f:
                f.write(directory)
        except:
            pass

    def load_output_dir(self):
        try:
            if os.path.exists("output_dir.txt"):
                with open("output_dir.txt", "r") as f:
                    return f.read().strip()
        except:
            pass
        return ""

if __name__ == "__main__":
    root = TkinterDnD.Tk()
    app = SpritesheetGeneratorApp(root)
    root.mainloop()
